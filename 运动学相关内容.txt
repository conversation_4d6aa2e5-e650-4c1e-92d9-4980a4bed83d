
=== 关键词: 运动学 (第13行) ===
乔贵方1，2*，高春晖1，蒋欣怡1，徐思敏1，刘 娣1
（1. 南京工程学院 自动化学院，江苏 南京 211167；
2. 东南大学 仪器科学与工程学院，江苏 南京 210096）
摘要：鉴于高端智能制造领域对高精度应用场景下的工业机器人绝对定位精度的更高要求。本文主要研究基于支持向
量回归（Support Vector Regression ， SVR）模型的机器人空间误差预测方法。针对Staubli TX60型串联工业机器人进行
了运动学建模和误差分析。搭建了基于Leica AT960激光跟踪仪的机器人测量实验平台，并进行了大量空间位姿点的
测量，通过真实数据集训练优化SVR模型。基于SVR方法对机器人实际位姿误差进行预测与补偿，避免了复杂的误差
建模过程。机器人平均位置误差和平均姿态误差分别由补偿前的（0.706 1 mm，0.174 2°）降低至（0.055 6 mm，
0.024 6°），位置误差降低了92.12%，姿态误差降低了85.88%。最后，通过与BP，Elman神经网络以及传统LM几何参
数标定方法进行对比，验证了基于SVR模型进行空间误差预测对机器人位置和姿态误差降低效果的有效性和均衡性。
关 键 词：支持向量回归；非模型标定；工业机器人；误差预测；机器人标定
==================================================

=== 关键词: 位姿 (第14行) ===
（1. 南京工程学院 自动化学院，江苏 南京 211167；
2. 东南大学 仪器科学与工程学院，江苏 南京 210096）
摘要：鉴于高端智能制造领域对高精度应用场景下的工业机器人绝对定位精度的更高要求。本文主要研究基于支持向
量回归（Support Vector Regression ， SVR）模型的机器人空间误差预测方法。针对Staubli TX60型串联工业机器人进行
了运动学建模和误差分析。搭建了基于Leica AT960激光跟踪仪的机器人测量实验平台，并进行了大量空间位姿点的
测量，通过真实数据集训练优化SVR模型。基于SVR方法对机器人实际位姿误差进行预测与补偿，避免了复杂的误差
建模过程。机器人平均位置误差和平均姿态误差分别由补偿前的（0.706 1 mm，0.174 2°）降低至（0.055 6 mm，
0.024 6°），位置误差降低了92.12%，姿态误差降低了85.88%。最后，通过与BP，Elman神经网络以及传统LM几何参
数标定方法进行对比，验证了基于SVR模型进行空间误差预测对机器人位置和姿态误差降低效果的有效性和均衡性。
关 键 词：支持向量回归；非模型标定；工业机器人；误差预测；机器人标定
中图分类号：TP394.1；TH691.9 文献标识码：A doi：10.37188/OPE.20243218.2783
==================================================

=== 关键词: 位姿 (第74行) ===
误差与非几何误差因素［3］的存在，工业机器人的 补偿方法，提高了误差补偿的性能和稳定性，可
绝对定位精度仍为毫米级，难以满足“智能机器 以显著降低机器人定位误差。Du等［13］基于BP
人”重点专项中对绝对定位精度优于0.1 mm和 神经网络进行定位误差补偿，同时考虑了几何误
姿态角精度优于0.1°的精度要求［4］。因此，标定 差和非几何误差因素，可将机器人定位精度提高
技术作为一种能够有效提高机器人绝对定位精 80%以上；Wang等［14］基于深度置信网络和误差相
度的技术手段而被重点研究。 似度建立了工业机器人位姿误差预测模型，并在
机器人误差来源主要由关节误差、几何参数 KUKA KR500-3 6机器人上验证了方案的有效
误差以及由关节柔性、齿轮间隙等因素引起的非 性，补偿后，最大绝对位置误差和姿态误差分别从
几何参数误差构成［5］，又以几何参数误差为主，但 1.524 mm，0.082°减小到 0.244 mm 和 0.037°。
非几何参数误差仍占总误差的10%~20%，同样 Hu J等［15］基于GPSO-DNN优化神经网络预测模
在一定程度上影响了机器人精度。高贯斌等 型，进行定位误差预测与补偿，将机器人定位误差
==================================================

=== 关键词: 关节角 (第87行) ===
地解决了由于关节迟滞等因素造成的方向性误 针对机器人非模型标定过程中关键的误差
差。当前机器人标定技术多集中于离线标定方 预测过程，鉴于支持向量回归（Support Vector
法，主要包括基于误差模型的几何参数标定与非 Regression，SVR）在处理小样本、非线性回归和
模型标定方法。几何参数标定基本步骤分为建 高维数据方面的优越表现，提出一种基于支持
模、测量、辨识和补偿［7］。由于该方法仅针对几何 向量回归模型的机器人空间误差预测方法。通
参数误差，误差建模与参数辨识过程繁琐且无法 过SVR模型建立机器人关节角与机器人位置和
建立完备的数学模型，补偿效果有限。非模型标 姿态误差的映射机理，实现机器人位姿误差
定方法能够综合地考虑几何参数误差与非几何 预测。
参数误差因素，通过建立误差与机器人理论位姿
2 工业机器人运动学模型与误差
或关节角之间的映射模型，利用Kriging插值、模
==================================================

=== 关键词: 位姿 (第88行) ===
差。当前机器人标定技术多集中于离线标定方 预测过程，鉴于支持向量回归（Support Vector
法，主要包括基于误差模型的几何参数标定与非 Regression，SVR）在处理小样本、非线性回归和
模型标定方法。几何参数标定基本步骤分为建 高维数据方面的优越表现，提出一种基于支持
模、测量、辨识和补偿［7］。由于该方法仅针对几何 向量回归模型的机器人空间误差预测方法。通
参数误差，误差建模与参数辨识过程繁琐且无法 过SVR模型建立机器人关节角与机器人位置和
建立完备的数学模型，补偿效果有限。非模型标 姿态误差的映射机理，实现机器人位姿误差
定方法能够综合地考虑几何参数误差与非几何 预测。
参数误差因素，通过建立误差与机器人理论位姿
2 工业机器人运动学模型与误差
或关节角之间的映射模型，利用Kriging插值、模
糊插值等空间插值法［8］或极限学习机ELM，BP 分析
==================================================

=== 关键词: 位姿 (第90行) ===
模型标定方法。几何参数标定基本步骤分为建 高维数据方面的优越表现，提出一种基于支持
模、测量、辨识和补偿［7］。由于该方法仅针对几何 向量回归模型的机器人空间误差预测方法。通
参数误差，误差建模与参数辨识过程繁琐且无法 过SVR模型建立机器人关节角与机器人位置和
建立完备的数学模型，补偿效果有限。非模型标 姿态误差的映射机理，实现机器人位姿误差
定方法能够综合地考虑几何参数误差与非几何 预测。
参数误差因素，通过建立误差与机器人理论位姿
2 工业机器人运动学模型与误差
或关节角之间的映射模型，利用Kriging插值、模
糊插值等空间插值法［8］或极限学习机ELM，BP 分析
神经网络等神经网络法进行机器人误差预测［9］。
通过文献［10］中基于伪目标迭代的误差补偿方法 2.1 工业机器人运动学模型
==================================================

=== 关键词: 运动学 (第91行) ===
模、测量、辨识和补偿［7］。由于该方法仅针对几何 向量回归模型的机器人空间误差预测方法。通
参数误差，误差建模与参数辨识过程繁琐且无法 过SVR模型建立机器人关节角与机器人位置和
建立完备的数学模型，补偿效果有限。非模型标 姿态误差的映射机理，实现机器人位姿误差
定方法能够综合地考虑几何参数误差与非几何 预测。
参数误差因素，通过建立误差与机器人理论位姿
2 工业机器人运动学模型与误差
或关节角之间的映射模型，利用Kriging插值、模
糊插值等空间插值法［8］或极限学习机ELM，BP 分析
神经网络等神经网络法进行机器人误差预测［9］。
通过文献［10］中基于伪目标迭代的误差补偿方法 2.1 工业机器人运动学模型
或基于Newton-Rapson的误差补偿方法等运动 本文待标定的Staubli TX60工业机器人为
==================================================

=== 关键词: 关节角 (第92行) ===
参数误差，误差建模与参数辨识过程繁琐且无法 过SVR模型建立机器人关节角与机器人位置和
建立完备的数学模型，补偿效果有限。非模型标 姿态误差的映射机理，实现机器人位姿误差
定方法能够综合地考虑几何参数误差与非几何 预测。
参数误差因素，通过建立误差与机器人理论位姿
2 工业机器人运动学模型与误差
或关节角之间的映射模型，利用Kriging插值、模
糊插值等空间插值法［8］或极限学习机ELM，BP 分析
神经网络等神经网络法进行机器人误差预测［9］。
通过文献［10］中基于伪目标迭代的误差补偿方法 2.1 工业机器人运动学模型
或基于Newton-Rapson的误差补偿方法等运动 本文待标定的Staubli TX60工业机器人为

==================================================

=== 关键词: 运动学 (第95行) ===
参数误差因素，通过建立误差与机器人理论位姿
2 工业机器人运动学模型与误差
或关节角之间的映射模型，利用Kriging插值、模
糊插值等空间插值法［8］或极限学习机ELM，BP 分析
神经网络等神经网络法进行机器人误差预测［9］。
通过文献［10］中基于伪目标迭代的误差补偿方法 2.1 工业机器人运动学模型
或基于Newton-Rapson的误差补偿方法等运动 本文待标定的Staubli TX60工业机器人为

==================================================


==================================================

=== 关键词: 运动学 (第104行) ===


=== 第 3 页 ===

第 18 期 乔贵方，等：基于支持向量回归的工业机器人空间误差预测 2785
六自由度串联机器人，其机械结构及关节坐标系 Staubli TX60工业机器人的正运动学模型，相邻
的示意图如图 1 所示。根据 M-DH 模型建立 连杆坐标系的转换关系如式（1）所示：
A i=Rot(Z i ，θ i)Trans(Z i ，d i)Trans(X i ，a i)Rot(X i ，α i)Rot(Y i ，β i)=
■||cosθ icosβ i-sinθ isinα isinβ
i
-sinθ icosα
==================================================

=== 关键词: 关节角 (第156行) ===
i
，β
i
分别为Staubli TX60机器人的
Tab.1 Theoretical MD-H parameters of Staubli TX60 robot
关节角、连杆偏距、连杆长度、连杆转角、相邻平
行两关节处绕Y轴转动的旋转参数β。
i θ i/rad d i/mm a i/mm α i/rad β i/rad
1 π 0 0 π/2 -
根据图 1 建立的关节坐标系，待标定的
2 π/2 0 290 0 0
==================================================

=== 关键词: 变换矩阵 (第164行) ===
1 π 0 0 π/2 -
根据图 1 建立的关节坐标系，待标定的
2 π/2 0 290 0 0
Staubli TX60机器人的M-DH模型参数如表1所
3 π/2 20 0 π/2 -
示。根据相邻坐标系的齐次变换矩阵A 可得工
i
4 π 310 0 π/2 -
业机器人末端位姿变换矩阵如式（2）所示：
5 π 0 0 π/2 -
T n=A
==================================================

=== 关键词: 变换矩阵 (第167行) ===
Staubli TX60机器人的M-DH模型参数如表1所
3 π/2 20 0 π/2 -
示。根据相邻坐标系的齐次变换矩阵A 可得工
i
4 π 310 0 π/2 -
业机器人末端位姿变换矩阵如式（2）所示：
5 π 0 0 π/2 -
T n=A
1
A
2
==================================================

=== 关键词: 旋转矩阵 (第189行) ===
■
|||| ， （2）
6 0 70 0 0 -
其中：T 为机器人末端相对于基坐标系的齐次变
n
换矩阵，R 为机器人末端姿态旋转矩阵，P 为机 2.2 工业机器人标定系统与误差分析
n n
器人末端位置矢量，分别表示理论姿态和理论 图2为本文所搭建的工业机器人标定系统，
位置。
该系统利用Leica AT960激光跟踪仪测量待标定
工业机器人Staubli TX60。Leica AT960激光跟
==================================================

=== 关键词: 运动学 (第212行) ===


=== 第 4 页 ===

2786 光学 精密工程 第 32 卷
关运动学建模及参数辨识程序利用Matlab软件。 对于给定机器人数据集D=｛（x
i
，y
i
）｝，（i=
本文中所涉及的测量过程均符合GB/T-12642- 1，2，…，m），构建回归模型：
==================================================

=== 关键词: 位姿 (第222行) ===
本文中所涉及的测量过程均符合GB/T-12642- 1，2，…，m），构建回归模型：
( ) ( )
2013及ISO-9283工业机器人性能规范及其试验 f x =wTϕ x +b， （7）
方法标准［16］。 其中：w为特征权重向量，b为偏置项，ϕ ( x ) 为非
根据激光跟踪仪的测量数据，计算工业机器 线性映射函数。
人的末端位姿误差ΔT为： SVR通过寻找最优超平面，最大化间隔带宽
ΔT=T r-T n= ■
■
| || |R
0
r P
==================================================

=== 关键词: 变换矩阵 (第296行) ===
根据微分运动原理［17］，工业机器人的末端位 y i-f ( x
i
) ≤ε+ξ 
i
，
姿变换矩阵dT为： ξ i≥0，ξ  i≥0，i=1，2，⋯，m
dT=T r-T n=σT
n
， （4）
式中，C为惩罚因子，用于平衡最小化间隔和控
式中，σ为工业机器人末端相对于基坐标系的微
==================================================

=== 关键词: 变换矩阵 (第303行) ===
n
， （4）
式中，C为惩罚因子，用于平衡最小化间隔和控
式中，σ为工业机器人末端相对于基坐标系的微
制损失，且直接影响最小化函数式（8）求解模型
分变换矩阵，如式（5）所示：
权重w和偏置b的过程。C值越大表明对训练数
■|| 0 -δz δy dx■|| 据的拟合能力越强。但C值过大，可能会导致过
|| ||
σ= ■
■
==================================================

=== 关键词: 位姿 (第373行) ===
的
。
|| ||
■ 0 0 0 1 ■ C值。
将式（5）中矩阵写成向量形式，如式（6）所 通过拉格朗日乘子法将问题转化为SVR模
示。从而计算出机器人的末端位姿误差。 型对偶问题，经过求解可获得 SVR 特征函数
e=[d
x
d
y
d
==================================================

=== 关键词: 关节角 (第414行) ===
。SVR核函数只需
与基于模型的机器人标定方法相比，基于非 满足Mercer定理即可，无需确定ϕ ( x ) 具体形式。
模型的机器人标定方法能够综合地考虑工业机 研究表明径向基（Radial Basis Function，RBF）核
器人多种误差因素，避免了复杂的误差建模过 函数对于复杂的非线性关系具有较好的拟合能
程。其中误差预测过程为非模型标定方法的关 力，因此本文选用RBF核函数进行数据的非线性
键步骤，通过构建机器人关节角与位姿误差之间 处理。RBF核函数如式（10）：
( )
的映射模型实现机器人误差预测。鉴于SVR在 K ( x
i
，x ) =exp -γ‖x i-x‖2 ， γ>0，（10）
处理小样本、非线性回归和高维数据方面具有优
==================================================

=== 关键词: 位姿 (第426行) ===
越的表现，可通过核函数将输入数据映射到高维
其中：γ是RBF核函数的带宽参数，γ=
2σ2
，控制
特征空间，从而允许模型拟合复杂的非线性关 高斯核函数的局部作用范围，γ过大，则σ过小，
系，可以更好地处理机器人位姿误差数据。因 会造成过拟合，导致泛化能力较差。
此，本文采用SVR实现工业机器人空间误差的 考虑到六自由度串联机器人的逆解计算存
预测。 在奇异点的问题，SVR算法数据集以机器人的关

==================================================

==================================================

=== 关键词: 位姿 (第438行) ===
=== 第 5 页 ===

第 18 期 乔贵方，等：基于支持向量回归的工业机器人空间误差预测 2787
节角数据作为输入向量x
i
，机器人实际位姿误差 δα，δβ，δγ）分别取数据集最大最小值进行归
数据作为输出量y。此外，由于工业机器人的位 一化。
i
姿误差与结构参数之间具有强耦合的互相影响
关系，同时也与空间位姿相关，本文SVR方法以 4 实验结果分析
六关节角作为输入样本特征x i=（θ
==================================================

=== 关键词: 位姿 (第442行) ===
i
，机器人实际位姿误差 δα，δβ，δγ）分别取数据集最大最小值进行归
数据作为输出量y。此外，由于工业机器人的位 一化。
i
姿误差与结构参数之间具有强耦合的互相影响
关系，同时也与空间位姿相关，本文SVR方法以 4 实验结果分析
六关节角作为输入样本特征x i=（θ
1i
，θ
2i
，θ
==================================================

=== 关键词: 关节角 (第443行) ===
，机器人实际位姿误差 δα，δβ，δγ）分别取数据集最大最小值进行归
数据作为输出量y。此外，由于工业机器人的位 一化。
i
姿误差与结构参数之间具有强耦合的互相影响
关系，同时也与空间位姿相关，本文SVR方法以 4 实验结果分析
六关节角作为输入样本特征x i=（θ
1i
，θ
2i
，θ
3i
==================================================

=== 关键词: 位姿 (第452行) ===
，θ
3i
，θ
4i
，
θ ，θ ），以单输出的形式进行模型预测，针对机 4.1 工业机器人位姿测量与数据处理
5i 6i
器人位姿误差（dx，dy，dz，δα，δβ，δγ）分别进行模 机器人标定系统利用Leica AT960激光跟踪
型训练和预测，以提高算法预测的可靠性。SVR 仪对Staubli TX60工业机器人进行位姿数据测
模型结构如图3所示。 量。实验中以Staubli TX60工业机器人的基坐
标系为参考坐标系，在机器人前侧选取边长为
==================================================

=== 关键词: 位姿 (第454行) ===
，θ
4i
，
θ ，θ ），以单输出的形式进行模型预测，针对机 4.1 工业机器人位姿测量与数据处理
5i 6i
器人位姿误差（dx，dy，dz，δα，δβ，δγ）分别进行模 机器人标定系统利用Leica AT960激光跟踪
型训练和预测，以提高算法预测的可靠性。SVR 仪对Staubli TX60工业机器人进行位姿数据测
模型结构如图3所示。 量。实验中以Staubli TX60工业机器人的基坐
标系为参考坐标系，在机器人前侧选取边长为
1 000 mm的立方体范围中，通过RoboDyn软件
随机生成机器人可达的2 000个位姿点，位姿点
==================================================

=== 关键词: 位姿 (第455行) ===
4i
，
θ ，θ ），以单输出的形式进行模型预测，针对机 4.1 工业机器人位姿测量与数据处理
5i 6i
器人位姿误差（dx，dy，dz，δα，δβ，δγ）分别进行模 机器人标定系统利用Leica AT960激光跟踪
型训练和预测，以提高算法预测的可靠性。SVR 仪对Staubli TX60工业机器人进行位姿数据测
模型结构如图3所示。 量。实验中以Staubli TX60工业机器人的基坐
标系为参考坐标系，在机器人前侧选取边长为
1 000 mm的立方体范围中，通过RoboDyn软件
随机生成机器人可达的2 000个位姿点，位姿点
的空间分布如图4所示，并将数据集划分为训练
==================================================

=== 关键词: 位姿 (第459行) ===
器人位姿误差（dx，dy，dz，δα，δβ，δγ）分别进行模 机器人标定系统利用Leica AT960激光跟踪
型训练和预测，以提高算法预测的可靠性。SVR 仪对Staubli TX60工业机器人进行位姿数据测
模型结构如图3所示。 量。实验中以Staubli TX60工业机器人的基坐
标系为参考坐标系，在机器人前侧选取边长为
1 000 mm的立方体范围中，通过RoboDyn软件
随机生成机器人可达的2 000个位姿点，位姿点
的空间分布如图4所示，并将数据集划分为训练
集和测试集，其中训练集占 80%，测试集占
20%。
图3 SVR模型结构
Fig.3 Structure of SVR model
==================================================

=== 关键词: 位姿 (第476行) ===
数值的搜索网格。然后，通过k折交叉验证方法
（取k=5），将训练集分为k折，取其中一折为验证
集，其余为训练集，进行k次迭代，对搜索网格中
的每对C，γ进行交叉验证，计算SVR模型的平
均验证误差；最后，确定使平均验证误差最小的
图4 Staubli TX60工业机器人位姿测量点空间分布
C，γ作为最优模型参数。
Fig.4 Spatial distribution of pose measurement points for
为提高SVR机器人误差预测的准确度并消
Staubli TX60 industrial robot
除位姿量纲不同的影响，本文采用线性归一化方
==================================================

=== 关键词: 位姿 (第481行) ===
图4 Staubli TX60工业机器人位姿测量点空间分布
C，γ作为最优模型参数。
Fig.4 Spatial distribution of pose measurement points for
为提高SVR机器人误差预测的准确度并消
Staubli TX60 industrial robot
除位姿量纲不同的影响，本文采用线性归一化方
法对输入样本关节角x i=（θ
1i
，θ
2i
，θ
==================================================

=== 关键词: 关节角 (第482行) ===
C，γ作为最优模型参数。
Fig.4 Spatial distribution of pose measurement points for
为提高SVR机器人误差预测的准确度并消
Staubli TX60 industrial robot
除位姿量纲不同的影响，本文采用线性归一化方
法对输入样本关节角x i=（θ
1i
，θ
2i
，θ
3i
==================================================

=== 关键词: 位姿 (第496行) ===
5i
，θ
6i
）
根据式（5）~式（6）计算Staubli TX60工业机
及目标值实际位姿误差（dx，dy，dz，δα，δβ，δγ）进
器人在各位姿点的误差，并由欧氏距离计算其综
行归一化数据处理，将原始数据映射至［v ，v ］
min max 合误差，位置综合误差与姿态综合误差计算公式
区间内，线性归一化计算公式如式（11）：
如式（12）和式（13）：
==================================================

=== 关键词: 位姿 (第497行) ===
，θ
6i
）
根据式（5）~式（6）计算Staubli TX60工业机
及目标值实际位姿误差（dx，dy，dz，δα，δβ，δγ）进
器人在各位姿点的误差，并由欧氏距离计算其综
行归一化数据处理，将原始数据映射至［v ，v ］
min max 合误差，位置综合误差与姿态综合误差计算公式
区间内，线性归一化计算公式如式（11）：
如式（12）和式（13）：
( ) ( )
==================================================

=== 关键词: 计算公式 (第499行) ===
）
根据式（5）~式（6）计算Staubli TX60工业机
及目标值实际位姿误差（dx，dy，dz，δα，δβ，δγ）进
器人在各位姿点的误差，并由欧氏距离计算其综
行归一化数据处理，将原始数据映射至［v ，v ］
min max 合误差，位置综合误差与姿态综合误差计算公式
区间内，线性归一化计算公式如式（11）：
如式（12）和式（13）：
( ) ( )
v -v × u-u
u′= max u min -u min +v min ， （11） ΔP= (dx2+dy2+dz2)， （12）
==================================================

=== 关键词: 计算公式 (第500行) ===
根据式（5）~式（6）计算Staubli TX60工业机
及目标值实际位姿误差（dx，dy，dz，δα，δβ，δγ）进
器人在各位姿点的误差，并由欧氏距离计算其综
行归一化数据处理，将原始数据映射至［v ，v ］
min max 合误差，位置综合误差与姿态综合误差计算公式
区间内，线性归一化计算公式如式（11）：
如式（12）和式（13）：
( ) ( )
v -v × u-u
u′= max u min -u min +v min ， （11） ΔP= (dx2+dy2+dz2)， （12）
max min
==================================================

=== 关键词: 位姿 (第508行) ===
v -v × u-u
u′= max u min -u min +v min ， （11） ΔP= (dx2+dy2+dz2)， （12）
max min
其中：u为待归一化的数据，u ，u 分别表示u ΔA= (δx2+δy2+δz2). （13）
max min
的最大值、最小值，其中［v ，v ］区间取［-1， 由数据集位姿点综合误差分析可得，其平均
min max
1］，将数据映射至［-1，1］区间内。归一化过程 位置误差和平均姿态误差分别为0.706 1 mm和
对样本特征θ 1i~θ 6i 以及预测目标值（dx，dy，dz， 0.174 2°，最大位置误差和最大姿态误差分别为

==================================================
==================================================

=== 关键词: 位姿 (第573行) ===


=== 第 7 页 ===

第 18 期 乔贵方，等：基于支持向量回归的工业机器人空间误差预测 2789
表3 机器人位姿误差补偿对比实验结果
Tab.3 Comparison experimental results of robot pose error compensation
Average error Maximum error Standard deviation of error
Model
Position/mm Attitude/（°） Position/mm Attitude/（°） Position/mm Attitude/（°）
Origin 0.706 1 0.174 2 1.194 7 0.280 4 0.276 0 0.051 0
==================================================

=== 关键词: 位姿 (第596行) ===
器人位置和姿态误差降低效果的均衡性上相较
于BP、Elman神经网络有明显优势，姿态精度的
稳定性也有明显提升。
图7 4种方法位置误差拟合效果对比
Fig.7 Comparison of four methods for fitting position errors 5 结 论
工业机器人的位姿误差与结构参数之间具
有强耦合的互相影响关系，同时也与空间位姿相
关。针对该问题，本文重点研究了基于SVR的
机器人空间误差预测方法。采用SVR模型进行
机器人实际位姿误差预测与补偿，能够较好地对
机器人误差数据进行非线性映射。实验结果表
==================================================

=== 关键词: 位姿 (第597行) ===
于BP、Elman神经网络有明显优势，姿态精度的
稳定性也有明显提升。
图7 4种方法位置误差拟合效果对比
Fig.7 Comparison of four methods for fitting position errors 5 结 论
工业机器人的位姿误差与结构参数之间具
有强耦合的互相影响关系，同时也与空间位姿相
关。针对该问题，本文重点研究了基于SVR的
机器人空间误差预测方法。采用SVR模型进行
机器人实际位姿误差预测与补偿，能够较好地对
机器人误差数据进行非线性映射。实验结果表
明，机器人平均综合位置误差和平均综合姿态误
==================================================

=== 关键词: 位姿 (第600行) ===
Fig.7 Comparison of four methods for fitting position errors 5 结 论
工业机器人的位姿误差与结构参数之间具
有强耦合的互相影响关系，同时也与空间位姿相
关。针对该问题，本文重点研究了基于SVR的
机器人空间误差预测方法。采用SVR模型进行
机器人实际位姿误差预测与补偿，能够较好地对
机器人误差数据进行非线性映射。实验结果表
明，机器人平均综合位置误差和平均综合姿态误
差分别由补偿前的（0.706 1 mm，0.174 2°）降低至
（0.055 6 mm，0.024 6°），位 置 误 差 降 低 了
92.12%，姿态误差降低了85.89%；最后，通过与
==================================================

=== 关键词: 位姿 (第633行) ===
Chinese）
Instrumentation and Measurement， 2088， 72：
［9］ CHEN D D， YUAN P J， WANG T M， et al. A
7506112.
compensation method based on error similarity and
［2］ 温秀兰， 宋爱国， 冯月贵， 等. 基于最优位姿集的
error correlation to enhance the position accuracy of
机器人标定及不确定度评定［J］. 仪器仪表学报，
an aviation drilling robot［J］. Measurement Science
2022， 43（9）： 276-283.
and Technology， 2018， 29（8）： 085011.
==================================================

=== 关键词: 运动学 (第649行) ===
ic error compensation methods for serial industrial
ment， 2022， 43（9）： 276-283.（in Chinese）
robots［J］. Mathematical Problems in Engineer⁃
［3］ 姜一舟， 于连栋， 常雅琪， 等. 基于改进差分进化
ing， 2021， 2021： 8086389.
算法的机器人运动学参数标定［J］. 光学 精密工
［11］ GAO T C， MENG F， ZHANG X Y， et al. An
程， 2021， 29（7）： 1580-1588.
operational calibration approach of industrial robots
JIANG Y Z， YU L D， CHANG Y Q， et al. Robot
through a motion capture system and an artificial
==================================================

=== 关键词: 位姿 (第665行) ===
2023， 125（11）： 5135-5147.
［12］ MIN K， NI F L， CHEN Z Y， et al. A robot posi⁃
nematics parameter calibration of serial industrial ro⁃
bots based on partial pose measurement［J］. Mathe⁃ tional error compensation method based on im⁃
matics， 2023， 11（23）： 4802. proved Kriging interpolation and kronecker products
［5］ 温秀兰， 康传帅， 宋爱国， 等. 基于全位姿测量优
［J］. IEEE Transactions on Industrial Electronics，
化的机器人精度研究［J］. 仪器仪表学报， 2019， 40
2024， 71（4）： 3884-3893.
（7）： 81-89. ［13］ SHANGHAI B H， DU SHANGHAI W， YU T.
WEN X L， KANG C S， SONG A G， et al. Study Research on positioning error compensation of in⁃
==================================================
