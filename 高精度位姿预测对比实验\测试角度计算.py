#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同的欧拉角计算方法
找出与激光跟踪仪测量最匹配的角度计算方法
"""

import numpy as np
import pandas as pd
from 理论计算模块 import RobotKinematics
from scipy.spatial.transform import Rotation as R

def test_euler_methods():
    """测试不同的欧拉角计算方法"""
    print("=== 测试欧拉角计算方法 ===")
    
    # 加载数据
    joint_data = pd.read_excel('../theta2000.xlsx', header=None)
    joint_data.columns = [f'theta_{i+1}' for i in range(6)]
    measured_data = pd.read_excel('../real2000.xlsx')
    
    # 创建机器人运动学计算器
    robot = RobotKinematics()
    
    # 测试前10个样本
    n_test = 10
    print(f"\n测试前 {n_test} 个样本的角度计算方法:")
    print("="*100)
    
    methods_errors = {
        'xyz_intrinsic': [],
        'zyx_rpy': [],
        'xyz_scipy': [],
        'zyx_scipy': [],
        'xyz_extrinsic': [],
        'zyx_extrinsic': []
    }
    
    for i in range(n_test):
        # 计算理论位姿
        joint_angles = joint_data.iloc[i].values
        joint_angles_rad = np.deg2rad(joint_angles)
        
        # 计算变换矩阵
        T = np.eye(4)
        for j in range(6):
            a, alpha, d, theta_offset, beta = robot.dh_params[j]
            theta = joint_angles_rad[j] + theta_offset
            T_j = robot.mdh_transform(a, alpha, d, theta, beta)
            T = T @ T_j
        
        # 提取旋转矩阵
        rotation_matrix = T[:3, :3]
        
        # 实测角度
        measured_angles = measured_data.iloc[i, 3:6].values
        
        # 测试不同方法
        methods = test_all_euler_methods(rotation_matrix)
        
        print(f"\n样本 {i+1}:")
        print(f"实测角度: [{measured_angles[0]:7.2f}, {measured_angles[1]:7.2f}, {measured_angles[2]:7.2f}]")
        
        for method_name, angles in methods.items():
            error = np.sqrt(np.sum((angles - measured_angles)**2))
            methods_errors[method_name].append(error)
            print(f"{method_name:15s}: [{angles[0]:7.2f}, {angles[1]:7.2f}, {angles[2]:7.2f}] 误差: {error:6.2f}°")
    
    # 统计各方法的平均误差
    print(f"\n各方法平均角度误差统计:")
    print("="*50)
    
    best_method = None
    best_error = float('inf')
    
    for method_name, errors in methods_errors.items():
        avg_error = np.mean(errors)
        std_error = np.std(errors)
        print(f"{method_name:15s}: 平均误差 {avg_error:6.2f}° ± {std_error:5.2f}°")
        
        if avg_error < best_error:
            best_error = avg_error
            best_method = method_name
    
    print(f"\n🏆 最佳方法: {best_method} (平均误差: {best_error:.2f}°)")
    
    return best_method

def test_all_euler_methods(rotation_matrix):
    """测试所有欧拉角转换方法"""
    methods = {}

    # 方法1: XYZ内旋 (标准公式)
    methods['xyz_intrinsic'] = rotation_matrix_to_euler_xyz_intrinsic(rotation_matrix)

    # 方法2: ZYX欧拉角 (RPY)
    methods['zyx_rpy'] = rotation_matrix_to_euler_zyx_rpy(rotation_matrix)

    # 方法3-6: SciPy库的不同顺序
    r = R.from_matrix(rotation_matrix)
    methods['xyz_scipy'] = r.as_euler('xyz', degrees=True)
    methods['zyx_scipy'] = r.as_euler('zyx', degrees=True)
    methods['xyz_extrinsic'] = r.as_euler('XYZ', degrees=True)  # 外旋
    methods['zyx_extrinsic'] = r.as_euler('ZYX', degrees=True)  # 外旋

    return methods

def rotation_matrix_to_euler_xyz_intrinsic(R):
    """XYZ内旋欧拉角"""
    sy = np.sqrt(R[0, 0] * R[0, 0] + R[1, 0] * R[1, 0])
    
    singular = sy < 1e-6
    
    if not singular:
        x = np.arctan2(R[2, 1], R[2, 2])
        y = np.arctan2(-R[2, 0], sy)
        z = np.arctan2(R[1, 0], R[0, 0])
    else:
        x = np.arctan2(-R[1, 2], R[1, 1])
        y = np.arctan2(-R[2, 0], sy)
        z = 0
    
    return np.array([np.rad2deg(x), np.rad2deg(y), np.rad2deg(z)])

def rotation_matrix_to_euler_zyx_rpy(R):
    """ZYX欧拉角 (Roll-Pitch-Yaw)"""
    roll = np.arctan2(R[2, 1], R[2, 2])
    pitch = np.arctan2(-R[2, 0], np.sqrt(R[2, 1]**2 + R[2, 2]**2))
    yaw = np.arctan2(R[1, 0], R[0, 0])
    
    return np.array([np.rad2deg(roll), np.rad2deg(pitch), np.rad2deg(yaw)])

def test_coordinate_system_alignment():
    """测试坐标系对齐"""
    print("\n=== 测试坐标系对齐 ===")
    
    # 加载数据
    joint_data = pd.read_excel('../theta2000.xlsx', header=None)
    joint_data.columns = [f'theta_{i+1}' for i in range(6)]
    measured_data = pd.read_excel('../real2000.xlsx')
    
    robot = RobotKinematics()
    
    # 分析实测角度的范围和分布
    print("实测角度统计:")
    for i, coord in enumerate(['Rx', 'Ry', 'Rz']):
        angles = measured_data.iloc[:, 3+i].values
        print(f"{coord}: 范围 [{np.min(angles):7.2f}, {np.max(angles):7.2f}], "
              f"均值 {np.mean(angles):7.2f}, 标准差 {np.std(angles):6.2f}")
    
    # 分析理论计算角度的范围
    print("\n理论计算角度统计 (前100个样本):")
    theoretical_angles = []
    
    for i in range(100):
        joint_angles = joint_data.iloc[i].values
        pose = robot.forward_kinematics(joint_angles)
        theoretical_angles.append(pose[3:6])
    
    theoretical_angles = np.array(theoretical_angles)
    
    for i, coord in enumerate(['Rx', 'Ry', 'Rz']):
        angles = theoretical_angles[:, i]
        print(f"{coord}: 范围 [{np.min(angles):7.2f}, {np.max(angles):7.2f}], "
              f"均值 {np.mean(angles):7.2f}, 标准差 {np.std(angles):6.2f}")

def test_angle_wrapping():
    """测试角度包装问题"""
    print("\n=== 测试角度包装问题 ===")
    
    def wrap_angle_180(angle):
        """将角度包装到[-180, 180]范围"""
        return ((angle + 180) % 360) - 180
    
    def wrap_angle_360(angle):
        """将角度包装到[0, 360]范围"""
        return angle % 360
    
    # 测试几个样本的角度包装效果
    joint_data = pd.read_excel('../theta2000.xlsx', header=None)
    joint_data.columns = [f'theta_{i+1}' for i in range(6)]
    measured_data = pd.read_excel('../real2000.xlsx')
    
    robot = RobotKinematics()
    
    print("测试角度包装效果 (前5个样本):")
    print("样本 | 实测角度 | 理论角度(原始) | 理论角度(±180°) | 理论角度(0-360°)")
    print("-" * 80)
    
    for i in range(5):
        joint_angles = joint_data.iloc[i].values
        pose = robot.forward_kinematics(joint_angles)
        theoretical_angles = pose[3:6]
        measured_angles = measured_data.iloc[i, 3:6].values
        
        wrapped_180 = [wrap_angle_180(a) for a in theoretical_angles]
        wrapped_360 = [wrap_angle_360(a) for a in theoretical_angles]
        
        print(f"{i+1:4d} | ({measured_angles[0]:6.1f},{measured_angles[1]:6.1f},{measured_angles[2]:6.1f}) | "
              f"({theoretical_angles[0]:6.1f},{theoretical_angles[1]:6.1f},{theoretical_angles[2]:6.1f}) | "
              f"({wrapped_180[0]:6.1f},{wrapped_180[1]:6.1f},{wrapped_180[2]:6.1f}) | "
              f"({wrapped_360[0]:6.1f},{wrapped_360[1]:6.1f},{wrapped_360[2]:6.1f})")

def main():
    """主函数"""
    print("开始测试角度计算方法...")
    
    # 测试1: 不同欧拉角计算方法
    best_method = test_euler_methods()
    
    # 测试2: 坐标系对齐
    test_coordinate_system_alignment()
    
    # 测试3: 角度包装
    test_angle_wrapping()
    
    print(f"\n=== 测试结论 ===")
    print(f"推荐使用的角度计算方法: {best_method}")
    print("建议:")
    print("1. 根据测试结果选择最佳的欧拉角计算方法")
    print("2. 检查是否需要角度包装处理")
    print("3. 确认坐标系定义与激光跟踪仪一致")

if __name__ == "__main__":
    main()
