# 基于支持向量回归的工业机器人空间误差预测实验报告

## 实验概述

本实验基于论文《基于支持向量回归的工业机器人空间误差预测》，对比了理论计算和机器学习模型在机器人位姿预测中的精度表现。

### 实验目标
1. 验证M-DH运动学模型的理论计算精度
2. 训练SVR模型进行位姿预测
3. 对比两种方法的预测误差
4. 证明ML模型相对理论计算的优势

## 实验设置

### 数据集
- **总样本数**: 2000
- **训练样本**: 1600 (80.0%)
- **测试样本**: 400 (20.0%)
- **输入特征**: 6个关节角度 (θ1-θ6)
- **输出目标**: 3D位置坐标 (X, Y, Z)

### 实验方法
1. **理论计算**: 基于M-DH运动学模型的正解算法
2. **ML预测**: 支持向量回归(SVR)模型
3. **评估指标**: 位置误差、RMSE、MAE、R²

## 实验结果

### 误差统计对比

| 指标 | 理论计算 | ML预测 | 改进 |
|------|----------|--------|------|
| 平均误差 (mm) | 553.5324 | 12.3965 | 97.76% |
| RMSE (mm) | 588.8069 | 17.6466 | 97.00% |
| 最大误差 (mm) | 1191.5763 | 104.8607 | 91.20% |
| 标准差 (mm) | 200.7375 | 12.5590 | - |

### 各轴误差分析

| 轴 | 理论RMSE (mm) | ML RMSE (mm) | 改进率 |
|----|---------------|--------------|--------|
| X轴 | 438.1177 | 11.8551 | 97.29% |
| Y轴 | 252.7201 | 12.0125 | 95.25% |
| Z轴 | 301.4615 | 5.1536 | 98.29% |

### 统计显著性检验

通过配对t检验验证了ML模型相对理论计算的显著改进:
- **p值**: 0.000000
- **效应量 (Cohen's d)**: 3.8049
- **结论**: 差异具有统计显著性 (p < 0.05)

## 主要发现

1. **ML模型显著优于理论计算**: 平均位置误差减少了97.8%
2. **各轴改进均匀**: X、Y、Z三轴的预测精度都有显著提升
3. **稳定性提升**: ML模型的误差标准差更小，预测更稳定
4. **统计显著性**: 改进具有统计学意义，不是偶然现象

## 结论

本实验成功验证了基于支持向量回归的机器学习方法在工业机器人位姿预测中的优势：

1. **精度提升**: ML模型相比传统理论计算方法，位置预测精度提升97.8%
2. **实用价值**: 对于高精度应用场景，ML方法能够有效补偿理论模型的不足
3. **技术可行性**: SVR模型训练简单，预测速度快，适合工业应用

## 实验文件说明

- `数据预处理.py`: 数据加载和预处理模块
- `理论计算模块.py`: M-DH运动学正解实现
- `ML模型训练.py`: SVR模型训练和优化
- `误差分析.py`: 综合误差分析和可视化
- `综合误差分析.png`: 误差对比可视化图表
- `误差分析结果.xlsx`: 详细数值结果

---
*实验日期: 2025-06-18 23:17:50*
*实验平台: Python 3.x + scikit-learn*
