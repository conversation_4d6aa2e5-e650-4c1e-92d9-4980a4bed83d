#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的实验设计 - 符合老师要求
对比理论计算误差 vs 机器学习直接预测误差
证明机器学习方法的有效性
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.neural_network import MLPRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import lightgbm as lgb
from 理论计算模块 import RobotKinematics
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ProperExperimentDesign:
    """正确的实验设计 - 理论计算 vs 机器学习直接预测"""
    
    def __init__(self):
        self.robot = RobotKinematics()
        self.scaler = StandardScaler()
        
    def load_data(self):
        """加载数据"""
        print("=== 加载实验数据 ===")
        
        # 加载关节角度和实测位姿
        self.joint_data = pd.read_excel('../theta2000.xlsx', header=None)
        self.joint_data.columns = [f'theta_{i+1}' for i in range(6)]
        self.measured_data = pd.read_excel('../real2000.xlsx')
        
        print(f"数据规模: {len(self.joint_data)} 个样本")
        print(f"输入维度: 6个关节角度")
        print(f"输出维度: 6个位姿坐标 (X,Y,Z,Rx,Ry,Rz)")
        
        return True
    
    def calculate_theoretical_poses(self):
        """计算理论位姿"""
        print("\n=== 计算理论位姿 ===")
        
        theoretical_poses = []
        for i in range(len(self.joint_data)):
            joint_angles = self.joint_data.iloc[i].values
            pose = self.robot.forward_kinematics(joint_angles)
            theoretical_poses.append(pose)
        
        self.theoretical_data = np.array(theoretical_poses)
        print("理论位姿计算完成")
        
        return True
    
    def train_direct_prediction_models(self):
        """训练直接预测模型 - 关键：直接从关节角度预测位姿"""
        print("\n=== 训练机器学习直接预测模型 ===")
        
        # 数据分割 - 重要：使用真实的训练/测试分割
        X_train, X_test, y_train, y_test = train_test_split(
            self.joint_data.values, self.measured_data.values,
            test_size=0.3, random_state=42  # 30%作为测试集
        )
        
        # 保存测试集索引，用于后续对比
        train_indices, test_indices = train_test_split(
            range(len(self.joint_data)), test_size=0.3, random_state=42
        )
        self.test_indices = test_indices
        
        # 特征标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        print(f"训练集大小: {len(X_train)} 样本")
        print(f"测试集大小: {len(X_test)} 样本")
        
        # 训练多种模型
        self.models = {}
        
        # 1. 神经网络
        print("训练神经网络模型...")
        mlp = MLPRegressor(
            hidden_layer_sizes=(100, 50),
            max_iter=1000,
            random_state=42
        )
        mlp.fit(X_train_scaled, y_train)
        self.models['MLP'] = mlp
        
        # 2. 随机森林
        print("训练随机森林模型...")
        rf = RandomForestRegressor(
            n_estimators=100,
            random_state=42
        )
        rf.fit(X_train, y_train)  # 随机森林不需要标准化
        self.models['RandomForest'] = rf
        
        # 3. LightGBM (需要为每个输出单独训练)
        print("训练LightGBM模型...")
        lgb_models = []
        for i in range(6):  # 6个输出坐标
            lgb_model = lgb.LGBMRegressor(
                n_estimators=100,
                random_state=42,
                verbose=-1
            )
            lgb_model.fit(X_train, y_train[:, i])
            lgb_models.append(lgb_model)
        self.models['LightGBM'] = lgb_models
        
        # 在测试集上预测
        self.ml_predictions = {}
        
        # MLP预测（需要标准化）
        mlp_pred = mlp.predict(X_test_scaled)
        self.ml_predictions['MLP'] = mlp_pred
        
        # 随机森林预测
        rf_pred = rf.predict(X_test)
        self.ml_predictions['RandomForest'] = rf_pred
        
        # LightGBM预测
        lgb_pred = np.column_stack([model.predict(X_test) for model in self.models['LightGBM']])
        self.ml_predictions['LightGBM'] = lgb_pred
        
        # 保存测试集的真实值和理论值
        self.y_test = y_test
        self.theoretical_test = self.theoretical_data[test_indices]
        
        print("机器学习模型训练完成")
        return True
    
    def calculate_errors(self):
        """计算各种方法的误差"""
        print("\n=== 计算预测误差 ===")
        
        # 1. 理论计算误差 (理论值 vs 真实值)
        theory_errors = self.y_test - self.theoretical_test
        self.theory_pos_errors = np.sqrt(np.sum(theory_errors[:, :3]**2, axis=1))
        self.theory_angle_errors = np.sqrt(np.sum(theory_errors[:, 3:]**2, axis=1))
        
        # 2. 机器学习预测误差 (预测值 vs 真实值)
        self.ml_pos_errors = {}
        self.ml_angle_errors = {}
        
        for model_name, predictions in self.ml_predictions.items():
            ml_errors = self.y_test - predictions
            pos_errors = np.sqrt(np.sum(ml_errors[:, :3]**2, axis=1))
            angle_errors = np.sqrt(np.sum(ml_errors[:, 3:]**2, axis=1))
            
            self.ml_pos_errors[model_name] = pos_errors
            self.ml_angle_errors[model_name] = angle_errors
        
        # 统计结果
        print("误差统计结果:")
        print(f"理论计算:")
        print(f"  位置误差: 平均 {np.mean(self.theory_pos_errors):.3f} mm")
        print(f"  角度误差: 平均 {np.mean(self.theory_angle_errors):.3f} 度")
        
        for model_name in self.models.keys():
            print(f"{model_name}:")
            print(f"  位置误差: 平均 {np.mean(self.ml_pos_errors[model_name]):.3f} mm")
            print(f"  角度误差: 平均 {np.mean(self.ml_angle_errors[model_name]):.3f} 度")
        
        return True
    
    def evaluate_model_performance(self):
        """评估模型性能"""
        print("\n=== 模型性能评估 ===")
        
        # 计算R²得分
        print("各模型的R²得分:")
        for model_name, predictions in self.ml_predictions.items():
            r2 = r2_score(self.y_test, predictions)
            print(f"{model_name}: R² = {r2:.4f}")
        
        # 计算各坐标的RMSE
        coord_names = ['X', 'Y', 'Z', 'Rx', 'Ry', 'Rz']
        print(f"\n各坐标的RMSE对比:")
        print(f"{'坐标':<6} {'理论计算':<12} {'MLP':<12} {'RandomForest':<15} {'LightGBM':<12}")
        print("-" * 70)
        
        for i, coord in enumerate(coord_names):
            theory_rmse = np.sqrt(mean_squared_error(self.y_test[:, i], self.theoretical_test[:, i]))
            
            rmse_values = [f"{theory_rmse:.3f}"]
            for model_name in ['MLP', 'RandomForest', 'LightGBM']:
                ml_rmse = np.sqrt(mean_squared_error(self.y_test[:, i], self.ml_predictions[model_name][:, i]))
                rmse_values.append(f"{ml_rmse:.3f}")
            
            print(f"{coord:<6} {rmse_values[0]:<12} {rmse_values[1]:<12} {rmse_values[2]:<15} {rmse_values[3]:<12}")
    
    def create_comparison_visualization(self):
        """创建对比可视化"""
        print("\n=== 生成对比图表 ===")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 位置误差对比
        pos_data = [self.theory_pos_errors]
        labels = ['理论计算']
        
        for model_name in ['MLP', 'RandomForest', 'LightGBM']:
            pos_data.append(self.ml_pos_errors[model_name])
            labels.append(model_name)
        
        axes[0, 0].boxplot(pos_data, labels=labels)
        axes[0, 0].set_title('位置误差分布对比')
        axes[0, 0].set_ylabel('位置误差 (mm)')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 角度误差对比
        angle_data = [self.theory_angle_errors]
        for model_name in ['MLP', 'RandomForest', 'LightGBM']:
            angle_data.append(self.ml_angle_errors[model_name])
        
        axes[0, 1].boxplot(angle_data, labels=labels)
        axes[0, 1].set_title('角度误差分布对比')
        axes[0, 1].set_ylabel('角度误差 (度)')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 位置误差散点图 (理论 vs 最佳ML)
        best_ml_model = min(self.ml_pos_errors.keys(), 
                           key=lambda x: np.mean(self.ml_pos_errors[x]))
        
        axes[0, 2].scatter(self.theory_pos_errors, self.ml_pos_errors[best_ml_model], alpha=0.6)
        max_error = max(max(self.theory_pos_errors), max(self.ml_pos_errors[best_ml_model]))
        axes[0, 2].plot([0, max_error], [0, max_error], 'r--', alpha=0.8)
        axes[0, 2].set_xlabel('理论计算位置误差 (mm)')
        axes[0, 2].set_ylabel(f'{best_ml_model}位置误差 (mm)')
        axes[0, 2].set_title(f'位置误差对比: 理论 vs {best_ml_model}')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 样本误差对比 (前50个样本)
        n_show = min(50, len(self.theory_pos_errors))
        sample_indices = range(n_show)
        
        axes[1, 0].plot(sample_indices, self.theory_pos_errors[:n_show], 
                       'o-', label='理论计算', markersize=4)
        axes[1, 0].plot(sample_indices, self.ml_pos_errors[best_ml_model][:n_show], 
                       's-', label=best_ml_model, markersize=4)
        axes[1, 0].set_xlabel('样本编号')
        axes[1, 0].set_ylabel('位置误差 (mm)')
        axes[1, 0].set_title('样本位置误差对比')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 误差改进百分比
        improvement_data = []
        improvement_labels = []
        
        for model_name in ['MLP', 'RandomForest', 'LightGBM']:
            pos_improvement = (1 - np.mean(self.ml_pos_errors[model_name]) / 
                              np.mean(self.theory_pos_errors)) * 100
            improvement_data.append(pos_improvement)
            improvement_labels.append(model_name)
        
        bars = axes[1, 1].bar(improvement_labels, improvement_data)
        axes[1, 1].set_title('位置精度改进百分比')
        axes[1, 1].set_ylabel('改进百分比 (%)')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 为正值和负值设置不同颜色
        for bar, improvement in zip(bars, improvement_data):
            if improvement > 0:
                bar.set_color('green')
            else:
                bar.set_color('red')
        
        # 各坐标RMSE对比
        coord_names = ['X', 'Y', 'Z', 'Rx', 'Ry', 'Rz']
        theory_rmse = []
        best_ml_rmse = []
        
        for i in range(6):
            theory_rmse.append(np.sqrt(mean_squared_error(self.y_test[:, i], self.theoretical_test[:, i])))
            best_ml_rmse.append(np.sqrt(mean_squared_error(self.y_test[:, i], 
                                                          self.ml_predictions[best_ml_model][:, i])))
        
        x = np.arange(len(coord_names))
        width = 0.35
        
        axes[1, 2].bar(x - width/2, theory_rmse, width, label='理论计算', alpha=0.8)
        axes[1, 2].bar(x + width/2, best_ml_rmse, width, label=best_ml_model, alpha=0.8)
        axes[1, 2].set_xlabel('坐标')
        axes[1, 2].set_ylabel('RMSE')
        axes[1, 2].set_title('各坐标RMSE对比')
        axes[1, 2].set_xticks(x)
        axes[1, 2].set_xticklabels(coord_names)
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('正确的实验对比.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_final_report(self):
        """生成最终报告"""
        print("\n" + "="*80)
        print("正确的实验设计 - 最终报告")
        print("="*80)
        
        print(f"\n📊 实验设计说明:")
        print(f"1. 理论计算: 基于DH参数的运动学计算")
        print(f"2. 机器学习: 直接从关节角度预测位姿（不使用理论值）")
        print(f"3. 对比基准: 激光跟踪仪高精度实测值")
        print(f"4. 评估指标: 预测值与实测值的误差")
        
        # 找出最佳ML模型
        best_model = min(self.ml_pos_errors.keys(), 
                        key=lambda x: np.mean(self.ml_pos_errors[x]))
        
        print(f"\n📈 主要结果:")
        print(f"理论计算误差:")
        print(f"  位置: {np.mean(self.theory_pos_errors):.3f} ± {np.std(self.theory_pos_errors):.3f} mm")
        print(f"  角度: {np.mean(self.theory_angle_errors):.3f} ± {np.std(self.theory_angle_errors):.3f} 度")
        
        print(f"\n最佳机器学习模型 ({best_model}):")
        print(f"  位置: {np.mean(self.ml_pos_errors[best_model]):.3f} ± {np.std(self.ml_pos_errors[best_model]):.3f} mm")
        print(f"  角度: {np.mean(self.ml_angle_errors[best_model]):.3f} ± {np.std(self.ml_angle_errors[best_model]):.3f} 度")
        
        # 计算改进
        pos_improvement = (1 - np.mean(self.ml_pos_errors[best_model]) / 
                          np.mean(self.theory_pos_errors)) * 100
        angle_improvement = (1 - np.mean(self.ml_angle_errors[best_model]) / 
                            np.mean(self.theory_angle_errors)) * 100
        
        print(f"\n🎯 机器学习的改进效果:")
        if pos_improvement > 0:
            print(f"  位置精度提升: {pos_improvement:.1f}%")
        else:
            print(f"  位置精度下降: {abs(pos_improvement):.1f}%")
            
        if angle_improvement > 0:
            print(f"  角度精度提升: {angle_improvement:.1f}%")
        else:
            print(f"  角度精度下降: {abs(angle_improvement):.1f}%")
        
        print(f"\n💡 实验结论:")
        if pos_improvement > 0 or angle_improvement > 0:
            print("✅ 机器学习方法在某些方面优于理论计算")
            print("✅ 数据驱动方法能够学习到理论模型未捕获的模式")
            print("✅ 证明了机器学习在机器人位姿预测中的价值")
        else:
            print("⚠️ 理论计算在当前数据集上表现更好")
            print("💡 可能需要更多数据或改进ML模型")
            print("💡 理论模型的物理基础仍然很重要")

def main():
    """主函数"""
    print("开始正确的实验设计...")
    
    # 创建实验
    experiment = ProperExperimentDesign()
    
    # 执行实验流程
    if not experiment.load_data():
        return False
    
    if not experiment.calculate_theoretical_poses():
        return False
    
    if not experiment.train_direct_prediction_models():
        return False
    
    if not experiment.calculate_errors():
        return False
    
    experiment.evaluate_model_performance()
    experiment.create_comparison_visualization()
    experiment.generate_final_report()
    
    print("\n✅ 正确的实验设计完成！")
    print("这个实验设计符合老师的要求：")
    print("1. 对比理论计算误差 vs 机器学习预测误差")
    print("2. 证明机器学习方法的独立预测能力")
    print("3. 不依赖理论值进行'补偿'")
    
    return True

if __name__ == "__main__":
    main()
