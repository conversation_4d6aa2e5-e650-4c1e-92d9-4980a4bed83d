#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
误差分析模块
对比理论计算、ML预测和实际测量的误差
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import matplotlib.patches as mpatches

class ErrorAnalyzer:
    """
    误差分析类
    """
    
    def __init__(self):
        self.actual_positions = None
        self.theoretical_positions = None
        self.ml_predictions = None
        self.joint_angles = None
        self.error_analysis = {}
        
    def load_data(self, actual_positions, theoretical_positions, ml_predictions, joint_angles):
        """
        加载对比数据
        """
        self.actual_positions = actual_positions
        self.theoretical_positions = theoretical_positions
        self.ml_predictions = ml_predictions
        self.joint_angles = joint_angles
        
        print(f"加载数据完成:")
        print(f"  样本数量: {len(actual_positions)}")
        print(f"  实际位置形状: {actual_positions.shape}")
        print(f"  理论位置形状: {theoretical_positions.shape}")
        print(f"  ML预测形状: {ml_predictions.shape}")
        
    def calculate_errors(self):
        """
        计算各种误差指标
        """
        print("正在计算误差指标...")
        
        # 理论计算误差
        theoretical_errors = np.linalg.norm(
            self.theoretical_positions - self.actual_positions, axis=1
        )
        
        # ML预测误差
        ml_errors = np.linalg.norm(
            self.ml_predictions - self.actual_positions, axis=1
        )
        
        # 各轴误差
        theoretical_x_errors = np.abs(self.theoretical_positions[:, 0] - self.actual_positions[:, 0])
        theoretical_y_errors = np.abs(self.theoretical_positions[:, 1] - self.actual_positions[:, 1])
        theoretical_z_errors = np.abs(self.theoretical_positions[:, 2] - self.actual_positions[:, 2])
        
        ml_x_errors = np.abs(self.ml_predictions[:, 0] - self.actual_positions[:, 0])
        ml_y_errors = np.abs(self.ml_predictions[:, 1] - self.actual_positions[:, 1])
        ml_z_errors = np.abs(self.ml_predictions[:, 2] - self.actual_positions[:, 2])
        
        # 统计分析
        self.error_analysis = {
            'theoretical': {
                'position_errors': theoretical_errors,
                'mean_error': np.mean(theoretical_errors),
                'std_error': np.std(theoretical_errors),
                'max_error': np.max(theoretical_errors),
                'min_error': np.min(theoretical_errors),
                'rmse': np.sqrt(np.mean(theoretical_errors**2)),
                'mae': np.mean(theoretical_errors),
                'x_errors': theoretical_x_errors,
                'y_errors': theoretical_y_errors,
                'z_errors': theoretical_z_errors,
                'rmse_x': np.sqrt(np.mean(theoretical_x_errors**2)),
                'rmse_y': np.sqrt(np.mean(theoretical_y_errors**2)),
                'rmse_z': np.sqrt(np.mean(theoretical_z_errors**2))
            },
            'ml': {
                'position_errors': ml_errors,
                'mean_error': np.mean(ml_errors),
                'std_error': np.std(ml_errors),
                'max_error': np.max(ml_errors),
                'min_error': np.min(ml_errors),
                'rmse': np.sqrt(np.mean(ml_errors**2)),
                'mae': np.mean(ml_errors),
                'x_errors': ml_x_errors,
                'y_errors': ml_y_errors,
                'z_errors': ml_z_errors,
                'rmse_x': np.sqrt(np.mean(ml_x_errors**2)),
                'rmse_y': np.sqrt(np.mean(ml_y_errors**2)),
                'rmse_z': np.sqrt(np.mean(ml_z_errors**2))
            }
        }
        
        # 计算改进百分比
        improvement = {
            'mean_error_improvement': (self.error_analysis['theoretical']['mean_error'] - 
                                     self.error_analysis['ml']['mean_error']) / 
                                     self.error_analysis['theoretical']['mean_error'] * 100,
            'rmse_improvement': (self.error_analysis['theoretical']['rmse'] - 
                               self.error_analysis['ml']['rmse']) / 
                               self.error_analysis['theoretical']['rmse'] * 100,
            'max_error_improvement': (self.error_analysis['theoretical']['max_error'] - 
                                    self.error_analysis['ml']['max_error']) / 
                                    self.error_analysis['theoretical']['max_error'] * 100
        }
        
        self.error_analysis['improvement'] = improvement
        
        print("误差计算完成:")
        print(f"  理论计算平均误差: {self.error_analysis['theoretical']['mean_error']:.4f} mm")
        print(f"  ML预测平均误差: {self.error_analysis['ml']['mean_error']:.4f} mm")
        print(f"  误差改进: {improvement['mean_error_improvement']:.2f}%")
        
        return self.error_analysis
    
    def statistical_significance_test(self):
        """
        统计显著性检验
        """
        theoretical_errors = self.error_analysis['theoretical']['position_errors']
        ml_errors = self.error_analysis['ml']['position_errors']
        
        # 配对t检验
        t_stat, p_value = stats.ttest_rel(theoretical_errors, ml_errors)
        
        # Wilcoxon符号秩检验 (非参数检验)
        wilcoxon_stat, wilcoxon_p = stats.wilcoxon(theoretical_errors, ml_errors)
        
        # 效应量 (Cohen's d)
        pooled_std = np.sqrt((np.var(theoretical_errors) + np.var(ml_errors)) / 2)
        cohens_d = (np.mean(theoretical_errors) - np.mean(ml_errors)) / pooled_std
        
        significance_results = {
            't_statistic': t_stat,
            'p_value': p_value,
            'wilcoxon_statistic': wilcoxon_stat,
            'wilcoxon_p_value': wilcoxon_p,
            'cohens_d': cohens_d,
            'significant': p_value < 0.05
        }
        
        print(f"\n=== 统计显著性检验 ===")
        print(f"配对t检验: t={t_stat:.4f}, p={p_value:.6f}")
        print(f"Wilcoxon检验: W={wilcoxon_stat:.4f}, p={wilcoxon_p:.6f}")
        print(f"Cohen's d (效应量): {cohens_d:.4f}")
        print(f"差异显著性: {'显著' if significance_results['significant'] else '不显著'}")
        
        return significance_results
    
    def create_comprehensive_visualization(self):
        """
        创建综合误差分析可视化
        """
        if not self.error_analysis:
            self.calculate_errors()
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建大图
        fig = plt.figure(figsize=(20, 16))
        
        # 1. 误差分布对比 (2x2子图)
        ax1 = plt.subplot(3, 4, 1)
        theoretical_errors = self.error_analysis['theoretical']['position_errors']
        ml_errors = self.error_analysis['ml']['position_errors']
        
        plt.hist(theoretical_errors, bins=50, alpha=0.7, label='理论计算', color='red')
        plt.hist(ml_errors, bins=50, alpha=0.7, label='ML预测', color='blue')
        plt.xlabel('位置误差 (mm)')
        plt.ylabel('频次')
        plt.title('误差分布对比')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. 箱线图对比
        ax2 = plt.subplot(3, 4, 2)
        plt.boxplot([theoretical_errors, ml_errors], 
                   labels=['理论计算', 'ML预测'])
        plt.ylabel('位置误差 (mm)')
        plt.title('误差分布箱线图')
        plt.grid(True, alpha=0.3)
        
        # 3. 各轴误差对比
        ax3 = plt.subplot(3, 4, 3)
        x_pos = np.arange(3)
        theoretical_axis_errors = [
            self.error_analysis['theoretical']['rmse_x'],
            self.error_analysis['theoretical']['rmse_y'],
            self.error_analysis['theoretical']['rmse_z']
        ]
        ml_axis_errors = [
            self.error_analysis['ml']['rmse_x'],
            self.error_analysis['ml']['rmse_y'],
            self.error_analysis['ml']['rmse_z']
        ]
        
        width = 0.35
        plt.bar(x_pos - width/2, theoretical_axis_errors, width, 
               label='理论计算', alpha=0.7, color='red')
        plt.bar(x_pos + width/2, ml_axis_errors, width, 
               label='ML预测', alpha=0.7, color='blue')
        plt.xlabel('坐标轴')
        plt.ylabel('RMSE (mm)')
        plt.title('各轴RMSE对比')
        plt.xticks(x_pos, ['X', 'Y', 'Z'])
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 4. 改进百分比
        ax4 = plt.subplot(3, 4, 4)
        improvements = [
            self.error_analysis['improvement']['mean_error_improvement'],
            self.error_analysis['improvement']['rmse_improvement'],
            self.error_analysis['improvement']['max_error_improvement']
        ]
        metrics = ['平均误差', 'RMSE', '最大误差']
        
        bars = plt.bar(metrics, improvements, color='green', alpha=0.7)
        plt.ylabel('改进百分比 (%)')
        plt.title('ML模型相对理论计算的改进')
        plt.grid(True, alpha=0.3)
        
        # 在柱子上添加数值
        for bar, improvement in zip(bars, improvements):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{improvement:.1f}%', ha='center', va='bottom')
        
        # 5-8. 位置对比散点图
        for i, axis in enumerate(['X', 'Y', 'Z']):
            ax = plt.subplot(3, 4, 5 + i)
            
            # 理论vs实际
            plt.scatter(self.actual_positions[:, i], self.theoretical_positions[:, i], 
                       alpha=0.5, color='red', s=20, label='理论计算')
            
            # ML vs 实际
            plt.scatter(self.actual_positions[:, i], self.ml_predictions[:, i], 
                       alpha=0.5, color='blue', s=20, label='ML预测')
            
            # 理想线
            min_val = min(self.actual_positions[:, i].min(), 
                         self.theoretical_positions[:, i].min(),
                         self.ml_predictions[:, i].min())
            max_val = max(self.actual_positions[:, i].max(), 
                         self.theoretical_positions[:, i].max(),
                         self.ml_predictions[:, i].max())
            plt.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8)
            
            plt.xlabel(f'实际{axis}位置 (mm)')
            plt.ylabel(f'预测{axis}位置 (mm)')
            plt.title(f'{axis}轴位置对比')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        # 9. 误差随样本变化
        ax9 = plt.subplot(3, 4, 9)
        sample_indices = np.arange(len(theoretical_errors))
        plt.plot(sample_indices, theoretical_errors, alpha=0.7, color='red', 
                label='理论计算', linewidth=1)
        plt.plot(sample_indices, ml_errors, alpha=0.7, color='blue', 
                label='ML预测', linewidth=1)
        plt.xlabel('样本序号')
        plt.ylabel('位置误差 (mm)')
        plt.title('误差变化趋势')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 10. 误差相关性分析
        ax10 = plt.subplot(3, 4, 10)
        plt.scatter(theoretical_errors, ml_errors, alpha=0.6)
        plt.xlabel('理论计算误差 (mm)')
        plt.ylabel('ML预测误差 (mm)')
        plt.title('两种方法误差相关性')
        
        # 计算相关系数
        correlation = np.corrcoef(theoretical_errors, ml_errors)[0, 1]
        plt.text(0.05, 0.95, f'相关系数: {correlation:.3f}', 
                transform=ax10.transAxes, bbox=dict(boxstyle='round', facecolor='white'))
        plt.grid(True, alpha=0.3)
        
        # 11. 统计摘要
        ax11 = plt.subplot(3, 4, 11)
        ax11.axis('off')
        
        summary_text = f"""
        统计摘要:
        
        理论计算:
        • 平均误差: {self.error_analysis['theoretical']['mean_error']:.3f} mm
        • 标准差: {self.error_analysis['theoretical']['std_error']:.3f} mm
        • 最大误差: {self.error_analysis['theoretical']['max_error']:.3f} mm
        • RMSE: {self.error_analysis['theoretical']['rmse']:.3f} mm
        
        ML预测:
        • 平均误差: {self.error_analysis['ml']['mean_error']:.3f} mm
        • 标准差: {self.error_analysis['ml']['std_error']:.3f} mm
        • 最大误差: {self.error_analysis['ml']['max_error']:.3f} mm
        • RMSE: {self.error_analysis['ml']['rmse']:.3f} mm
        
        改进:
        • 平均误差改进: {self.error_analysis['improvement']['mean_error_improvement']:.1f}%
        • RMSE改进: {self.error_analysis['improvement']['rmse_improvement']:.1f}%
        """
        
        ax11.text(0.05, 0.95, summary_text, transform=ax11.transAxes, 
                 fontsize=10, verticalalignment='top',
                 bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        # 12. 3D误差可视化
        ax12 = plt.subplot(3, 4, 12, projection='3d')
        
        # 选择部分点进行3D可视化
        n_points = min(200, len(self.actual_positions))
        indices = np.random.choice(len(self.actual_positions), n_points, replace=False)
        
        scatter = ax12.scatter(self.actual_positions[indices, 0], 
                              self.actual_positions[indices, 1], 
                              self.actual_positions[indices, 2],
                              c=ml_errors[indices], cmap='viridis', alpha=0.6)
        
        ax12.set_xlabel('X (mm)')
        ax12.set_ylabel('Y (mm)')
        ax12.set_zlabel('Z (mm)')
        ax12.set_title('ML预测误差3D分布')
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax12, shrink=0.5)
        cbar.set_label('ML预测误差 (mm)')
        
        plt.tight_layout()
        plt.savefig('新实验设计/综合误差分析.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_sample_comparison_table(self, n_samples=20):
        """
        生成样本对比表格
        """
        # 随机选择样本
        indices = np.random.choice(len(self.actual_positions), n_samples, replace=False)
        
        comparison_data = []
        for i, idx in enumerate(indices):
            theoretical_error = np.linalg.norm(
                self.theoretical_positions[idx] - self.actual_positions[idx]
            )
            ml_error = np.linalg.norm(
                self.ml_predictions[idx] - self.actual_positions[idx]
            )
            
            comparison_data.append({
                '样本序号': idx + 1,
                '关节1(rad)': f"{self.joint_angles[idx, 0]:.4f}",
                '关节2(rad)': f"{self.joint_angles[idx, 1]:.4f}",
                '关节3(rad)': f"{self.joint_angles[idx, 2]:.4f}",
                '实际X(mm)': f"{self.actual_positions[idx, 0]:.2f}",
                '实际Y(mm)': f"{self.actual_positions[idx, 1]:.2f}",
                '实际Z(mm)': f"{self.actual_positions[idx, 2]:.2f}",
                '理论X(mm)': f"{self.theoretical_positions[idx, 0]:.2f}",
                '理论Y(mm)': f"{self.theoretical_positions[idx, 1]:.2f}",
                '理论Z(mm)': f"{self.theoretical_positions[idx, 2]:.2f}",
                'ML预测X(mm)': f"{self.ml_predictions[idx, 0]:.2f}",
                'ML预测Y(mm)': f"{self.ml_predictions[idx, 1]:.2f}",
                'ML预测Z(mm)': f"{self.ml_predictions[idx, 2]:.2f}",
                '理论误差(mm)': f"{theoretical_error:.3f}",
                'ML误差(mm)': f"{ml_error:.3f}",
                '误差改进(mm)': f"{theoretical_error - ml_error:.3f}",
                '改进百分比(%)': f"{(theoretical_error - ml_error)/theoretical_error*100:.1f}"
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        return comparison_df
    
    def save_analysis_results(self, filename='新实验设计/误差分析结果.xlsx'):
        """
        保存分析结果
        """
        if not self.error_analysis:
            self.calculate_errors()
        
        # 统计显著性检验
        significance = self.statistical_significance_test()
        
        # 生成样本对比表
        sample_comparison = self.generate_sample_comparison_table()
        
        with pd.ExcelWriter(filename) as writer:
            # 误差统计摘要
            summary_data = {
                '指标': ['平均误差(mm)', '标准差(mm)', '最大误差(mm)', 'RMSE(mm)', 'MAE(mm)',
                        'X轴RMSE(mm)', 'Y轴RMSE(mm)', 'Z轴RMSE(mm)'],
                '理论计算': [
                    self.error_analysis['theoretical']['mean_error'],
                    self.error_analysis['theoretical']['std_error'],
                    self.error_analysis['theoretical']['max_error'],
                    self.error_analysis['theoretical']['rmse'],
                    self.error_analysis['theoretical']['mae'],
                    self.error_analysis['theoretical']['rmse_x'],
                    self.error_analysis['theoretical']['rmse_y'],
                    self.error_analysis['theoretical']['rmse_z']
                ],
                'ML预测': [
                    self.error_analysis['ml']['mean_error'],
                    self.error_analysis['ml']['std_error'],
                    self.error_analysis['ml']['max_error'],
                    self.error_analysis['ml']['rmse'],
                    self.error_analysis['ml']['mae'],
                    self.error_analysis['ml']['rmse_x'],
                    self.error_analysis['ml']['rmse_y'],
                    self.error_analysis['ml']['rmse_z']
                ]
            }
            
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='误差统计摘要', index=False)
            
            # 改进分析
            improvement_df = pd.DataFrame([self.error_analysis['improvement']])
            improvement_df.to_excel(writer, sheet_name='改进分析', index=False)
            
            # 统计显著性检验
            significance_df = pd.DataFrame([significance])
            significance_df.to_excel(writer, sheet_name='显著性检验', index=False)
            
            # 样本对比
            sample_comparison.to_excel(writer, sheet_name='样本对比', index=False)
        
        print(f"误差分析结果已保存到: {filename}")
        
        return summary_df

def main():
    """
    主函数 - 执行误差分析
    """
    print("=== 机器人位姿预测实验 - 误差分析 ===\n")
    
    print("注意: 请先运行数据预处理、理论计算和ML训练模块")
    
    return None

if __name__ == "__main__":
    main()
