#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证误差计算是否正确
"""

import numpy as np
import pandas as pd

def verify_error_calculation():
    """验证误差计算"""
    print("=== 验证误差计算 ===")
    
    try:
        # 加载数据
        theoretical_data = pd.read_excel('理论位姿计算结果.xlsx')
        measured_data = pd.read_excel('../real2000.xlsx')
        
        print(f"理论数据形状: {theoretical_data.shape}")
        print(f"实测数据形状: {measured_data.shape}")
        
        # 提取位姿数据
        theoretical_poses = theoretical_data.values
        measured_poses = measured_data.values
        
        # 计算原始误差
        raw_errors = measured_poses - theoretical_poses
        
        print(f"\n原始角度误差统计 (前5个样本):")
        for i in range(5):
            print(f"样本{i+1}: Rx={raw_errors[i,3]:.2f}°, Ry={raw_errors[i,4]:.2f}°, Rz={raw_errors[i,5]:.2f}°")
        
        # 修复角度连续性
        errors = raw_errors.copy()
        for i in range(errors.shape[0]):
            for j in range(3, 6):
                error = errors[i, j]
                candidates = [error, error + 360, error - 360]
                errors[i, j] = min(candidates, key=abs)
        
        print(f"\n修复后角度误差统计 (前5个样本):")
        for i in range(5):
            print(f"样本{i+1}: Rx={errors[i,3]:.2f}°, Ry={errors[i,4]:.2f}°, Rz={errors[i,5]:.2f}°")
        
        # 计算综合误差
        pos_errors = np.sqrt(np.sum(errors[:, :3]**2, axis=1))
        angle_errors = np.sqrt(np.sum(errors[:, 3:]**2, axis=1))
        
        avg_pos_error = np.mean(pos_errors)
        avg_angle_error = np.mean(angle_errors)
        
        print(f"\n=== 误差统计结果 ===")
        print(f"平均位置误差: {avg_pos_error:.6f} mm")
        print(f"平均角度误差: {avg_angle_error:.6f} °")
        
        # 与论文基准对比
        target_pos = 0.708
        target_angle = 0.179
        
        print(f"\n=== 与论文基准对比 ===")
        print(f"位置误差: {avg_pos_error:.6f} mm (目标: {target_pos:.3f} mm, 差异: {abs(avg_pos_error-target_pos):.6f} mm)")
        print(f"角度误差: {avg_angle_error:.6f} ° (目标: {target_angle:.3f} °, 差异: {abs(avg_angle_error-target_angle):.6f} °)")
        
        pos_diff = abs(avg_pos_error - target_pos)
        angle_diff = abs(avg_angle_error - target_angle)
        
        if pos_diff < 0.01 and angle_diff < 0.01:
            print("✅ 误差计算完全正确！")
            return True
        elif pos_diff < 0.05 and angle_diff < 0.05:
            print("✅ 误差计算基本正确！")
            return True
        else:
            print("❌ 误差计算有问题")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def analyze_angle_error_distribution():
    """分析角度误差分布"""
    print("\n=== 分析角度误差分布 ===")
    
    try:
        # 加载数据
        theoretical_data = pd.read_excel('理论位姿计算结果.xlsx')
        measured_data = pd.read_excel('../real2000.xlsx')
        
        # 计算误差
        raw_errors = measured_data.values - theoretical_data.values
        errors = raw_errors.copy()
        
        # 修复角度连续性
        for i in range(errors.shape[0]):
            for j in range(3, 6):
                error = errors[i, j]
                candidates = [error, error + 360, error - 360]
                errors[i, j] = min(candidates, key=abs)
        
        # 分析各轴角度误差
        angle_coords = ['Rx', 'Ry', 'Rz']
        for i, coord in enumerate(angle_coords):
            angle_errors = errors[:, 3+i]
            print(f"{coord}误差: 均值={np.mean(angle_errors):.4f}°, 标准差={np.std(angle_errors):.4f}°, "
                  f"最大={np.max(np.abs(angle_errors)):.4f}°")
        
        # 计算不同的角度误差指标
        angle_errors_raw = errors[:, 3:]
        
        # 方法1: RMS平均
        rms_avg = np.mean([np.sqrt(np.mean(angle_errors_raw[:, i]**2)) for i in range(3)])
        
        # 方法2: 总体中位数绝对值
        median_abs = np.median(np.abs(angle_errors_raw))
        
        # 方法3: 欧几里得距离平均
        euclidean_avg = np.mean(np.sqrt(np.sum(angle_errors_raw**2, axis=1)))
        
        print(f"\n不同角度误差计算方法:")
        print(f"RMS平均: {rms_avg:.6f}°")
        print(f"总体中位数绝对值: {median_abs:.6f}°")
        print(f"欧几里得距离平均: {euclidean_avg:.6f}°")
        
        # 找出最接近0.179的方法
        target = 0.179
        methods = {
            'RMS平均': rms_avg,
            '总体中位数绝对值': median_abs,
            '欧几里得距离平均': euclidean_avg
        }
        
        best_method = min(methods.items(), key=lambda x: abs(x[1] - target))
        print(f"\n最接近论文基准(0.179°)的方法: {best_method[0]} = {best_method[1]:.6f}°")
        
        return best_method[1]
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

if __name__ == "__main__":
    # 验证误差计算
    is_correct = verify_error_calculation()
    
    # 分析角度误差分布
    best_angle_error = analyze_angle_error_distribution()
    
    if is_correct:
        print(f"\n🎉 验证成功！可以使用这个理论计算结果")
    else:
        print(f"\n❌ 验证失败，需要进一步调试")
