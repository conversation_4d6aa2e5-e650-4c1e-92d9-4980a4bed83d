This is pdfTeX, Version 3.141592653-2.6-1.40.27 (MiKTeX 25.4) (preloaded format=pdflatex 2025.7.26)  26 JUL 2025 12:04
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**d:/Desktop/zytable/误差补偿改进方案/PINN_Error_Compensation_Paper_Structure.tex
(d:/Desktop/zytable/误差补偿改进方案/PINN_Error_Compensation_Paper_Structure.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
(D:\applications\miktex\tex/latex/base\article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(D:\applications\miktex\tex/latex/base\size10.clo
File: size10.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count275
\c@section=\count276
\c@subsection=\count277
\c@subsubsection=\count278
\c@paragraph=\count279
\c@subparagraph=\count280
\c@figure=\count281
\c@table=\count282
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
) (D:\applications\miktex\tex/latex/amsmath\amsmath.sty
Package: amsmath 2025/06/16 v2.17y AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(D:\applications\miktex\tex/latex/amsmath\amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text
 (D:\applications\miktex\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen149
)) (D:\applications\miktex\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen150
) (D:\applications\miktex\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count283
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count284
\leftroot@=\count285
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count286
\DOTSCASE@=\count287
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen151
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count288
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count289
\dotsspace@=\muskip17
\c@parentequation=\count290
\dspbrk@lvl=\count291
\tag@help=\toks18
\row@=\count292
\column@=\count293
\maxfields@=\count294
\andhelp@=\toks19
\eqnshift@=\dimen152
\alignsep@=\dimen153
\tagshift@=\dimen154
\tagwidth@=\dimen155
\totwidth@=\dimen156
\lineht@=\dimen157
\@envbody=\toks20
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
) (D:\applications\miktex\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (D:\applications\miktex\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (D:\applications\miktex\tex/latex/algorithms\algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (D:\applications\miktex\tex/latex/base\ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (D:\applications\miktex\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
)
\c@ALC@unique=\count295
\c@ALC@line=\count296
\c@ALC@rem=\count297
\c@ALC@depth=\count298
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (D:\applications\miktex\tex/latex/algorithms\algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (D:\applications\miktex\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count299
\float@exts=\toks23
\float@box=\box55
\@float@everytoks=\toks24
\@floatcapt=\box56
)
\@float@every@algorithm=\toks25
\c@algorithm=\count300
) (D:\applications\miktex\tex/latex/graphics\graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)
 (D:\applications\miktex\tex/latex/graphics\graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (D:\applications\miktex\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (D:\applications\miktex\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (D:\applications\miktex\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen158
\Gin@req@width=\dimen159
) (D:\applications\miktex\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (D:\applications\miktex\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (D:\applications\miktex\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count301
\Gm@cntv=\count302
\c@Gm@tempcnt=\count303
\Gm@bindingoffset=\dimen160
\Gm@wd@mp=\dimen161
\Gm@odd@mp=\dimen162
\Gm@even@mp=\dimen163
\Gm@layoutwidth=\dimen164
\Gm@layoutheight=\dimen165
\Gm@layouthoffset=\dimen166
\Gm@layoutvoffset=\dimen167
\Gm@dimlist=\toks26
 (D:\applications\miktex\tex/latex/geometry\geometry.cfg)) (D:\applications\miktex\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen168
\lightrulewidth=\dimen169
\cmidrulewidth=\dimen170
\belowrulesep=\dimen171
\belowbottomsep=\dimen172
\aboverulesep=\dimen173
\abovetopsep=\dimen174
\cmidrulesep=\dimen175
\cmidrulekern=\dimen176
\defaultaddspace=\dimen177
\@cmidla=\count304
\@cmidlb=\count305
\@aboverulesep=\dimen178
\@belowrulesep=\dimen179
\@thisruleclass=\count306
\@lastruleclass=\count307
\@thisrulewidth=\dimen180
) (D:\applications\miktex\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip56
\multirow@cntb=\count308
\multirow@dima=\skip57
\bigstrutjot=\dimen181
) (D:\applications\miktex\tex/latex/tools\bm.sty
Package: bm 2023/12/19 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup6
\symboldletters=\mathgroup7
\symboldsymbols=\mathgroup8
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
) (D:\applications\miktex\tex/latex/hyperref\hyperref.sty
Package: hyperref 2025-07-12 v7.01o Hypertext links for LaTeX
 (D:\applications\miktex\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (D:\applications\miktex\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (D:\applications\miktex\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (D:\applications\miktex\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (D:\applications\miktex\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (D:\applications\miktex\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (D:\applications\miktex\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (D:\applications\miktex\tex/latex/hyperref\nameref.sty
Package: nameref 2025-06-21 v2.57 Cross-referencing by name of section
 (D:\applications\miktex\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (D:\applications\miktex\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (D:\applications\miktex\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count309
) (D:\applications\miktex\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count310
) (D:\applications\miktex\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen182
\Hy@linkcounter=\count311
\Hy@pagecounter=\count312
 (D:\applications\miktex\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2025-07-12 v7.01o Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (D:\applications\miktex\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count313
 (D:\applications\miktex\tex/latex/hyperref\puenc.def
File: puenc.def 2025-07-12 v7.01o Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4195.
Package hyperref Info: Link nesting OFF on input line 4200.
Package hyperref Info: Hyper index ON on input line 4203.
Package hyperref Info: Plain pages OFF on input line 4210.
Package hyperref Info: Backreferencing OFF on input line 4215.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4462.
\c@Hy@tempcnt=\count314
 (D:\applications\miktex\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4801.
\XeTeXLinkMargin=\dimen183
 (D:\applications\miktex\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (D:\applications\miktex\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count315
\Field@Width=\dimen184
\Fld@charsize=\dimen185
Package hyperref Info: Hyper figures OFF on input line 6078.
Package hyperref Info: Link nesting OFF on input line 6083.
Package hyperref Info: Hyper index ON on input line 6086.
Package hyperref Info: backreferencing OFF on input line 6093.
Package hyperref Info: Link coloring OFF on input line 6098.
Package hyperref Info: Link coloring with OCG OFF on input line 6103.
Package hyperref Info: PDF/A mode OFF on input line 6108.
\Hy@abspage=\count316
\c@Item=\count317
\c@Hfootnote=\count318
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (D:\applications\miktex\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2025-07-12 v7.01o Hyperref driver for pdfTeX
\Fld@listcount=\count319
\c@bookmark@seq@number=\count320
 (D:\applications\miktex\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)
 (D:\applications\miktex\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 284.
)
\Hy@SectionHShift=\skip58
) (D:\applications\miktex\tex/latex/tools\multicol.sty
Package: multicol 2025/05/25 v2.0a multicolumn formatting (FMi)
\c@tracingmulticols=\count321


Package multicol Warning: May not work with the twocolumn option on input line 145.

\mult@box=\box57
\multicol@leftmargin=\dimen186
\c@unbalance=\count322
\c@collectmore=\count323
\doublecol@number=\count324
\multicoltolerance=\count325
\multicolpretolerance=\count326
\full@width=\dimen187
\page@free=\dimen188
\premulticols=\dimen189
\postmulticols=\dimen190
\multicolsep=\skip59
\multicolbaselineskip=\skip60
\partial@page=\box58
\last@line=\box59
\mc@boxedresult=\box60
\maxbalancingoverflow=\dimen191
\mult@rightbox=\box61
\mult@grightbox=\box62
\mult@firstbox=\box63
\mult@gfirstbox=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\@tempa=\box99
\@tempa=\box100
\c@minrows=\count327
\c@columnbadness=\count328
\c@finalcolumnbadness=\count329
\last@try=\dimen192
\multicolovershoot=\dimen193
\multicolundershoot=\dimen194
\mult@nat@firstbox=\box101
\colbreak@box=\box102
\mc@col@check@num=\count330
\g__mc_curr_col_int=\count331
) (D:\applications\miktex\tex/latex/preprint\balance.sty
Package: balance 1999/02/23 4.3 (PWD)
\oldvsize=\dimen195
) (D:\applications\miktex\tex/latex/titlesec\titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box103
\beforetitleunit=\skip61
\aftertitleunit=\skip62
\ttl@plus=\dimen196
\ttl@minus=\dimen197
\ttl@toksa=\toks27
\titlewidth=\dimen198
\titlewidthlast=\dimen199
\titlewidthfirst=\dimen256
) (D:\applications\miktex\tex/latex/l3backend\l3backend-pdftex.def
File: l3backend-pdftex.def 2025-06-09 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count332
) (PINN_Error_Compensation_Paper_Structure.aux)
\openout1 = `PINN_Error_Compensation_Paper_Structure.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
 (D:\applications\miktex\tex/context/base/mkii\supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count333
\scratchdimen=\dimen257
\scratchbox=\box104
\nofMPsegments=\count334
\nofMParguments=\count335
\everyMPshowfont=\toks28
\MPscratchCnt=\count336
\MPscratchDim=\dimen258
\MPnumerator=\count337
\makeMPintoPDFobject=\count338
\everyMPtoPDFconversion=\toks29
) (D:\applications\miktex\tex/latex/epstopdf-pkg\epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (D:\applications\miktex\tex/latex/00miktex\epstopdf-sys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(56.9055pt, 483.69687pt, 56.9055pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=483.69687pt
* \textheight=702.78308pt
* \oddsidemargin=-15.36449pt
* \evensidemargin=-15.36449pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=4.0pt
* \marginparsep=10.0pt
* \columnsep=17.07182pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumntrue
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 47.
(PINN_Error_Compensation_Paper_Structure.out) (PINN_Error_Compensation_Paper_Structure.out)
\@outlinefile=\write3
\openout3 = `PINN_Error_Compensation_Paper_Structure.out'.

LaTeX Font Info:    Trying to load font information for U+msa on input line 62.
 (D:\applications\miktex\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 62.
 (D:\applications\miktex\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Underfull \hbox (badness 1102) in paragraph at lines 73--74
\OT1/cmr/m/n/10 ap-proaches may vi-o-late fun-da-men-tal phys-i-cal
 []




Underfull \hbox (badness 3568) in paragraph at lines 86--87
\OT1/cmr/m/n/10 NSGA-II al-go-rithm specif-i-cally de-signed for
 []


Underfull \hbox (badness 10000) in paragraph at lines 95--95
|[]\OT1/cmr/bx/n/12 Physics-Informed Neu-ral Net-
 []



[1{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}


]
Underfull \hbox (badness 1348) in paragraph at lines 98--98
|[]\OT1/cmr/bx/n/12 Multi-Objective Op-ti-miza-tion in
 []


Underfull \hbox (badness 3039) in paragraph at lines 109--109
|[]\OT1/cmr/bx/n/12 Multi-Objective Prob-lem State-
 []


Underfull \hbox (badness 10000) in paragraph at lines 119--119
|[]\OT1/cmr/bx/n/14.4 Physics-Informed Neu-ral
 []




Underfull \hbox (badness 10000) in paragraph at lines 139--140
\OT1/cmr/m/n/10 A deep multi-branch ar-chi-tec-ture
 []


Underfull \hbox (badness 2302) in paragraph at lines 139--140
\OT1/cmr/m/n/10 (512\TS1/cmr/m/n/10 ^^Y\OT1/cmr/m/n/10 256\TS1/cmr/m/n/10 ^^Y\OT1/cmr/m/n/10 128\TS1/cmr/m/n/10 ^^Y\OT1/cmr/m/n/10 64) with sep-a-rate path-ways for
 []


Underfull \hbox (badness 1194) in paragraph at lines 139--140
\OT1/cmr/m/n/10 po-si-tion and ori-en-ta-tion pre-dic-tion, in-cor-po-rat-ing
 []


Underfull \hbox (badness 3209) in paragraph at lines 139--140
\OT1/cmr/m/n/10 at-ten-tion mech-a-nisms to cap-ture joint cou-pling
 []


Underfull \hbox (badness 10000) in paragraph at lines 141--141
|[]\OT1/cmr/bx/n/14.4 Deterministic Op-ti-miza-tion
 []


Underfull \hbox (badness 10000) in paragraph at lines 157--157
|[]\OT1/cmr/bx/n/14.4 Multi-Objective Op-ti-miza-
 []



[2]
Underfull \hbox (badness 1184) in paragraph at lines 214--215
\OT1/cmr/m/n/10 Validation per-formed on Staubli TX60 in-dus-trial
 []




Underfull \hbox (badness 5504) in paragraph at lines 255--256
[]\OT1/cmr/m/n/10 Multi-objective op-ti-miza-tion pro-vides bet-ter
 []



[3]
Underfull \hbox (badness 1038) in paragraph at lines 256--257
[]\OT1/cmr/m/n/10 Physics-driven fea-tures out-per-form tra-di-tional
 []




Underfull \hbox (badness 1577) in paragraph at lines 297--298
\OT1/cmr/m/n/10 informed neu-ral net-works: A deep learn-ing
 []




Package balance Warning: You have called \balance in second column
(balance)                Columns might not be balanced.

[4] (PINN_Error_Compensation_Paper_Structure.aux)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
 ***********
Package rerunfilecheck Info: File `PINN_Error_Compensation_Paper_Structure.out' has not changed.
(rerunfilecheck)             Checksum: 1D2D75D9FEF917AEB3087D483BE059BC;9469.
 ) 
Here is how much of TeX's memory you used:
 11273 strings out of 468149
 169605 string characters out of 5441664
 601005 words of memory out of 5000000
 39645 multiletter control sequences out of 15000+600000
 641185 words of font info for 92 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,8n,79p,1155b,513s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\tcrm1000.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\tcrm0900.pk><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmbx10.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmbx12.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmbx9.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmex10.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmmi10.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmmi7.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmmib10.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmr10.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmr12.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmr7.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmr9.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmsy10.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmsy7.pfb>
Output written on PINN_Error_Compensation_Paper_Structure.pdf (4 pages, 225595 bytes).
PDF statistics:
 324 PDF objects out of 1000 (max. 8388607)
 66 named destinations out of 1000 (max. 500000)
 393 words of extra memory for PDF output out of 10000 (max. 10000000)

