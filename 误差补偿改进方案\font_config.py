#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto-generated matplotlib Chinese font configuration
Generated: 2025-07-20 13:48:00
System: Windows
Selected Font: SimHei
"""

import matplotlib.pyplot as plt
import matplotlib

def setup_chinese_font():
    """Setup Chinese font for matplotlib"""
    # Available fonts list
    available_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong', 'Microsoft JhengHei', 'DejaVu Sans', 'Arial']

    # Configure matplotlib
    plt.rcParams['font.sans-serif'] = available_fonts
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10

    # Clear font cache
    try:
        matplotlib.font_manager._rebuild()
    except:
        pass

    print(f"Font configured: {available_fonts[0]}")
    return available_fonts[0]

# Auto execute configuration
if __name__ == "__main__":
    setup_chinese_font()
