This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.6.29)  11 JUL 2025 13:03
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**c:/Users/<USER>/Desktop/zytable/误差补偿改进方案/高级模型论文.tex
(c:/Users/<USER>/Desktop/zytable/误差补偿改进方案/高级模型论文.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(c:/texlive/2025/texmf-dist/tex/latex/ieeetran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen141
\@IEEEtrantmpdimenB=\dimen142
\@IEEEtrantmpdimenC=\dimen143
\@IEEEtrantmpcountA=\count196
\@IEEEtrantmpcountB=\count197
\@IEEEtrantmpcountC=\count198
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 503.
(c:/texlive/2025/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen144
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen145
\CLASSINFOnormalsizeunitybaselineskip=\dimen146
\IEEEnormaljot=\dimen147
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
\IEEEquantizedlength=\dimen148
\IEEEquantizedlengthdiff=\dimen149
\IEEEquantizedtextheightdiff=\dimen150
\IEEEilabelindentA=\dimen151
\IEEEilabelindentB=\dimen152
\IEEEilabelindent=\dimen153
\IEEEelabelindent=\dimen154
\IEEEdlabelindent=\dimen155
\IEEElabelindent=\dimen156
\IEEEiednormlabelsep=\dimen157
\IEEEiedmathlabelsep=\dimen158
\IEEEiedtopsep=\skip49
\c@section=\count199
\c@subsection=\count266
\c@subsubsection=\count267
\c@paragraph=\count268
\c@IEEEsubequation=\count269
\abovecaptionskip=\skip50
\belowcaptionskip=\skip51
\c@figure=\count270
\c@table=\count271
\@IEEEeqnnumcols=\count272
\@IEEEeqncolcnt=\count273
\@IEEEsubeqnnumrollback=\count274
\@IEEEquantizeheightA=\dimen159
\@IEEEquantizeheightB=\dimen160
\@IEEEquantizeheightC=\dimen161
\@IEEEquantizeprevdepth=\dimen162
\@IEEEquantizemultiple=\count275
\@IEEEquantizeboxA=\box52
\@IEEEtmpitemindent=\dimen163
\IEEEPARstartletwidth=\dimen164
\c@IEEEbiography=\count276
\@IEEEtranrubishbin=\box53
)
** ATTENTION: Overriding command lockouts (line 2).
(c:/texlive/2025/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip52

For additional information on amsmath, use the `?' option.
(c:/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen165
)) (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen166
) (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count277
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count278
\leftroot@=\count279
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count280
\DOTSCASE@=\count281
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen167
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count282
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count283
\dotsspace@=\muskip17
\c@parentequation=\count284
\dspbrk@lvl=\count285
\tag@help=\toks19
\row@=\count286
\column@=\count287
\maxfields@=\count288
\andhelp@=\toks20
\eqnshift@=\dimen168
\alignsep@=\dimen169
\tagshift@=\dimen170
\tagwidth@=\dimen171
\totwidth@=\dimen172
\lineht@=\dimen173
\@envbody=\toks21
\multlinegap=\skip53
\multlinetaggap=\skip54
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (c:/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (c:/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (c:/texlive/2025/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (c:/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (c:/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
\c@ALC@unique=\count289
\c@ALC@line=\count290
\c@ALC@rem=\count291
\c@ALC@depth=\count292
\ALC@tlm=\skip55
\algorithmicindent=\skip56
) (c:/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (c:/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (c:/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen174
\Gin@req@width=\dimen175
) (c:/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
) (c:/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (c:/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen176
\lightrulewidth=\dimen177
\cmidrulewidth=\dimen178
\belowrulesep=\dimen179
\belowbottomsep=\dimen180
\aboverulesep=\dimen181
\abovetopsep=\dimen182
\cmidrulesep=\dimen183
\cmidrulekern=\dimen184
\defaultaddspace=\dimen185
\@cmidla=\count293
\@cmidlb=\count294
\@aboverulesep=\dimen186
\@belowrulesep=\dimen187
\@thisruleclass=\count295
\@lastruleclass=\count296
\@thisrulewidth=\dimen188
) (c:/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip57
\multirow@cntb=\count297
\multirow@dima=\skip58
\bigstrutjot=\dimen189
) (c:/texlive/2025/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (c:/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count298
\float@exts=\toks24
\float@box=\box56
\@float@everytoks=\toks25
\@floatcapt=\box57
)
\@float@every@algorithm=\toks26
\c@algorithm=\count299
) (c:/texlive/2025/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 
 (c:/texlive/2025/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count300
\c@ALG@rem=\count301
\c@ALG@nested=\count302
\ALG@tlm=\skip59
\ALG@thistlm=\skip60
\c@ALG@Lnr=\count303
\c@ALG@blocknr=\count304
\c@ALG@storecount=\count305
\c@ALG@tmpcounter=\count306
\ALG@tmplength=\skip61

c:/texlive/2025/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty:112: LaTeX Error: Command \algorithmic already defined.
               Or name \end... illegal, see p.192 of the manual.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.112    }
          %
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

)
Document Style - pseudocode environments for use with the `algorithmicx' style
) (c:/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count307
\l__pdf_internal_box=\box58
) (./高级模型论文.aux)
\openout1 = `高级模型论文.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.

-- Lines per column: 56 (exact).
(c:/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count308
\scratchdimen=\dimen190
\scratchbox=\box59
\nofMPsegments=\count309
\nofMParguments=\count310
\everyMPshowfont=\toks27
\MPscratchCnt=\count311
\MPscratchDim=\dimen191
\MPnumerator=\count312
\makeMPintoPDFobject=\count313
\everyMPtoPDFconversion=\toks28
) (c:/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (c:/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 31.
 (c:/texlive/2025/texmf-dist/tex/latex/psnfss/ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
)

LaTeX Warning: Citation `robotics_survey_2024' on page 1 undefined on input line 40.


LaTeX Warning: Citation `li2022positioning' on page 1 undefined on input line 42.


LaTeX Warning: Citation `qiao2019svr' on page 1 undefined on input line 42.


LaTeX Warning: Citation `raissi2019physics' on page 1 undefined on input line 44.


LaTeX Warning: Citation `vaswani2017attention' on page 1 undefined on input line 44.


LaTeX Warning: Citation `deb2002fast' on page 1 undefined on input line 44.

LaTeX Font Info:    Trying to load font information for U+msa on input line 48.
(c:/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 48.
 (c:/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)


LaTeX Warning: Citation `calibration_survey_2023' on page 1 undefined on input line 58.


LaTeX Warning: Citation `li2022positioning' on page 1 undefined on input line 60.


LaTeX Warning: Citation `qiao2019svr' on page 1 undefined on input line 60.


LaTeX Warning: Citation `raissi2019physics' on page 1 undefined on input line 64.


LaTeX Warning: Citation `bensch2024physics' on page 1 undefined on input line 72.


LaTeX Warning: Citation `pinn_robotics_2024' on page 1 undefined on input line 72.



[1{c:/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{c:/texlive/2025/texmf-dist/fonts/enc/dvips/base/8r.enc}


]

LaTeX Warning: Citation `vaswani2017attention' on page 2 undefined on input line 76.


LaTeX Warning: Citation `transformer_pose_2024' on page 2 undefined on input line 76.


LaTeX Warning: Citation `transformer_planning_2024' on page 2 undefined on input line 76.


LaTeX Warning: Citation `nsga_robot_2024' on page 2 undefined on input line 80.


LaTeX Warning: Citation `deb2002fast' on page 2 undefined on input line 80.


LaTeX Warning: Citation `multi_obj_calib_2024' on page 2 undefined on input line 80.




Underfull \hbox (badness 2150) in paragraph at lines 124--125
[]\OT1/ptm/b/n/10 Feed-Forward Net-works\OT1/ptm/m/n/10 : Position-wise fully con-
 []


Underfull \hbox (badness 3780) in paragraph at lines 125--126
[]\OT1/ptm/b/n/10 Output Pro-jec-tion\OT1/ptm/m/n/10 : Lin-ear layer map-ping to 6-
 []



[2]

LaTeX Warning: Citation `qiao2019svr' on page 3 undefined on input line 197.


LaTeX Warning: Citation `li2022positioning' on page 3 undefined on input line 211.


LaTeX Warning: Citation `qiao2019svr' on page 3 undefined on input line 213.


LaTeX Warning: Reference `tab:results' on page 3 undefined on input line 235.


Overfull \hbox (8.35863pt too wide) in paragraph at lines 240--258
 [] 
 []





[3]


** Conference Paper **
Before submitting the final camera ready copy, remember to:

 1. Manually equalize the lengths of two columns on the last page
 of your paper;

 2. Ensure that any PostScript and/or PDF output post-processing
 uses only Type 1 fonts and that every step in the generation
 process uses the appropriate paper size.



[4] (./高级模型论文.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 4977 strings out of 473190
 77706 string characters out of 5719979
 493310 words of memory out of 5000000
 28155 multiletter control sequences out of 15000+600000
 602391 words of font info for 123 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 57i,10n,65p,1122b,325s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmib10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr5.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb><c:/texlive/2025/texmf-dist/fonts/type1/urw/times/utmb8a.pfb><c:/texlive/2025/texmf-dist/fonts/type1/urw/times/utmbi8a.pfb><c:/texlive/2025/texmf-dist/fonts/type1/urw/times/utmr8a.pfb><c:/texlive/2025/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on 高级模型论文.pdf (4 pages, 179859 bytes).
PDF statistics:
 88 PDF objects out of 1000 (max. 8388607)
 53 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

