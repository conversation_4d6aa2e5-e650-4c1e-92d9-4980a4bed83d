#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文字体配置工具
解决matplotlib中文显示问题

作者: 张振意
日期: 2025年7月20日
"""

import matplotlib
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import platform

class ChineseFontManager:
    """中文字体管理器"""
    
    def __init__(self):
        self.system = platform.system()
        self.available_fonts = []
        self.selected_font = None
        
    def detect_chinese_fonts(self):
        """检测系统中可用的中文字体"""
        print("🔍 检测系统中文字体...")
        
        # 获取所有字体
        font_list = [f.name for f in fm.fontManager.ttflist]
        
        # 常见中文字体列表
        chinese_fonts = {
            'Windows': ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong', 'Microsoft JhengHei'],
            'Darwin': ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS', 'Hiragino Sans GB'],
            'Linux': ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'Source Han Sans CN']
        }
        
        # 根据系统选择字体列表
        if self.system in chinese_fonts:
            target_fonts = chinese_fonts[self.system]
        else:
            target_fonts = chinese_fonts['Windows']  # 默认使用Windows字体
        
        # 检测可用字体
        for font in target_fonts:
            if font in font_list:
                self.available_fonts.append(font)
                print(f"  ✅ 找到字体: {font}")
        
        # 添加通用字体作为备选
        universal_fonts = ['DejaVu Sans', 'Arial', 'Helvetica']
        for font in universal_fonts:
            if font in font_list and font not in self.available_fonts:
                self.available_fonts.append(font)
                print(f"  📝 备选字体: {font}")
        
        if not self.available_fonts:
            print("  ⚠️ 未找到合适的中文字体")
            self.available_fonts = ['sans-serif']
        
        return self.available_fonts
    
    def configure_matplotlib(self, font_name=None):
        """配置matplotlib中文字体"""
        if not self.available_fonts:
            self.detect_chinese_fonts()
        
        if font_name and font_name in self.available_fonts:
            selected_font = font_name
        else:
            selected_font = self.available_fonts[0]
        
        self.selected_font = selected_font
        
        # 配置matplotlib
        plt.rcParams['font.sans-serif'] = [selected_font] + self.available_fonts
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['font.size'] = 10
        
        # 清除字体缓存
        try:
            matplotlib.font_manager._rebuild()
        except:
            pass
        
        print(f"✅ 配置matplotlib字体: {selected_font}")
        return selected_font
    
    def test_chinese_display(self):
        """测试中文显示效果"""
        print("🧪 测试中文显示效果...")
        
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # 测试文本
            test_texts = [
                "基于PINN的机器人位姿误差补偿",
                "物理信息神经网络",
                "多目标优化算法",
                "确定性初始化策略",
                "局部最优问题解决方案"
            ]
            
            y_positions = [0.8, 0.6, 0.4, 0.2, 0.0]
            
            for i, (text, y) in enumerate(zip(test_texts, y_positions)):
                ax.text(0.1, y, f"{i+1}. {text}", fontsize=14, 
                       transform=ax.transAxes, verticalalignment='center')
            
            ax.set_xlim(0, 1)
            ax.set_ylim(-0.1, 1)
            ax.set_title(f"中文字体测试 - 当前字体: {self.selected_font}", fontsize=16, pad=20)
            ax.axis('off')
            
            # 保存测试图片
            os.makedirs("输出结果", exist_ok=True)
            plt.savefig('输出结果/中文字体测试.png', dpi=300, bbox_inches='tight')
            plt.show()
            
            print("✅ 中文显示测试完成，请查看 '输出结果/中文字体测试.png'")
            return True
            
        except Exception as e:
            print(f"❌ 中文显示测试失败: {e}")
            return False
    
    def install_font_guide(self):
        """提供字体安装指南"""
        print("\n📚 中文字体安装指南:")
        print("="*50)
        
        if self.system == "Windows":
            print("Windows系统:")
            print("1. 通常已预装SimHei、Microsoft YaHei等字体")
            print("2. 如需安装新字体，将.ttf文件复制到 C:\\Windows\\Fonts\\")
            print("3. 或右键字体文件选择'安装'")
            
        elif self.system == "Darwin":  # macOS
            print("macOS系统:")
            print("1. 通常已预装PingFang SC、Heiti SC等字体")
            print("2. 如需安装新字体，双击.ttf文件并点击'安装字体'")
            print("3. 或将字体文件复制到 ~/Library/Fonts/")
            
        elif self.system == "Linux":
            print("Linux系统:")
            print("1. 安装中文字体包:")
            print("   Ubuntu/Debian: sudo apt-get install fonts-wqy-microhei")
            print("   CentOS/RHEL: sudo yum install wqy-microhei-fonts")
            print("2. 或手动安装字体到 ~/.fonts/ 目录")
            print("3. 运行 fc-cache -fv 刷新字体缓存")
        
        print("\n推荐字体下载:")
        print("- 思源黑体: https://github.com/adobe-fonts/source-han-sans")
        print("- 文泉驿微米黑: http://wenq.org/wqy2/")
        print("- 站酷字体: https://www.zcool.com.cn/special/zcoolfonts/")
    
    def create_font_config_file(self):
        """创建字体配置文件"""
        import datetime

        config_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto-generated matplotlib Chinese font configuration
Generated: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
System: {self.system}
Selected Font: {self.selected_font}
"""

import matplotlib.pyplot as plt
import matplotlib

def setup_chinese_font():
    """Setup Chinese font for matplotlib"""
    # Available fonts list
    available_fonts = {self.available_fonts}

    # Configure matplotlib
    plt.rcParams['font.sans-serif'] = available_fonts
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10

    # Clear font cache
    try:
        matplotlib.font_manager._rebuild()
    except:
        pass

    print(f"Font configured: {{available_fonts[0]}}")
    return available_fonts[0]

# Auto execute configuration
if __name__ == "__main__":
    setup_chinese_font()
'''

        try:
            with open('font_config.py', 'w', encoding='utf-8') as f:
                f.write(config_content)

            print("✅ Font configuration file generated: font_config.py")
            print("   Usage: from font_config import setup_chinese_font; setup_chinese_font()")
        except Exception as e:
            print(f"⚠️ Failed to create config file: {e}")

def main():
    """主函数"""
    print("🎨 matplotlib中文字体配置工具")
    print("="*40)
    
    # 创建字体管理器
    font_manager = ChineseFontManager()
    
    # 检测字体
    fonts = font_manager.detect_chinese_fonts()
    
    if fonts:
        print(f"\n📋 检测到 {len(fonts)} 个可用字体:")
        for i, font in enumerate(fonts):
            print(f"  {i+1}. {font}")
        
        # 配置字体
        selected_font = font_manager.configure_matplotlib()
        
        # 测试显示
        success = font_manager.test_chinese_display()
        
        if success:
            print(f"\n🎉 字体配置成功! 当前使用: {selected_font}")
            
            # 生成配置文件
            font_manager.create_font_config_file()
        else:
            print("\n⚠️ 字体测试失败，可能需要安装中文字体")
            font_manager.install_font_guide()
    else:
        print("\n❌ 未找到可用字体")
        font_manager.install_font_guide()

if __name__ == "__main__":
    main()
