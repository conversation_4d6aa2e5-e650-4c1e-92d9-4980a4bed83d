\documentclass[12pt,a4paper]{article}
\usepackage[UTF8]{ctex}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{algorithm}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{bm}
\usepackage{hyperref}
\usepackage{xcolor}
\usepackage{framed}

\geometry{left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}

\title{\textbf{PINN机器人误差补偿详细公式讲解手册\\（本科生友好版）}}

\author{详细数学推导与创新点解析}

\date{\today}

\begin{document}

\maketitle

\tableofcontents

\section{前言：为什么需要这些公式}

\subsection{问题的本质}
想象一下，你有一个机器人手臂，它应该精确地抓取一个零件。但是由于制造误差、温度变化等因素，机器人实际到达的位置和你期望的位置之间总是有偏差。

\textbf{我们的目标}：用数学方法预测和补偿这些误差，让机器人更精确。

\textbf{传统方法的问题}：
\begin{itemize}
\item 容易陷入局部最优（就像爬山时困在小山包上，找不到真正的最高峰）
\item 忽略了物理定律（违反了机器人运动的基本规律）
\item 只考虑一个目标（比如只关心位置准确，不管角度准确）
\end{itemize}

\section{第一章：基础数学概念复习}

\subsection{向量和矩阵基础}

\subsubsection{什么是向量？}
向量就是有方向和大小的量。比如机器人的位置可以用向量表示：
$$\bm{p} = [x, y, z]^T$$

这里：
\begin{itemize}
\item $x, y, z$ 是三维空间中的坐标
\item $T$ 表示转置（把行向量变成列向量）
\item 粗体 $\bm{p}$ 表示这是一个向量
\end{itemize}

\subsubsection{什么是矩阵？}
矩阵是数字的矩形排列。机器人的旋转可以用旋转矩阵表示：
$$\bm{R} = \begin{bmatrix}
r_{11} & r_{12} & r_{13} \\
r_{21} & r_{22} & r_{23} \\
r_{31} & r_{32} & r_{33}
\end{bmatrix}$$

\textcolor{blue}{\textbf{物理意义}}：这个矩阵告诉我们如何从一个坐标系转换到另一个坐标系。

\subsection{三角函数复习}

\subsubsection{为什么机器人学中到处都是sin和cos？}
机器人的关节是旋转的，旋转运动天然就和圆有关，而圆上的点的坐标就是用sin和cos表示的！

对于角度 $\theta$：
\begin{align}
x &= r \cos(\theta) \\
y &= r \sin(\theta)
\end{align}

\textcolor{red}{\textbf{重要性质}}：
\begin{itemize}
\item $\sin^2(\theta) + \cos^2(\theta) = 1$ （勾股定理的推广）
\item $\sin(\theta_1 + \theta_2) = \sin(\theta_1)\cos(\theta_2) + \cos(\theta_1)\sin(\theta_2)$
\item $\cos(\theta_1 + \theta_2) = \cos(\theta_1)\cos(\theta_2) - \sin(\theta_1)\sin(\theta_2)$
\end{itemize}

\section{第二章：机器人运动学基础}

\subsection{什么是正向运动学？}

\textbf{简单理解}：给定机器人各个关节的角度，计算机器人末端（比如机械手）的位置和姿态。

\subsubsection{DH参数法}
这是描述机器人几何结构的标准方法。每个关节用4个参数描述：
\begin{itemize}
\item $a_i$：连杆长度
\item $\alpha_i$：连杆扭转角
\item $d_i$：连杆偏距
\item $\theta_i$：关节角（这是变量）
\end{itemize}

\subsubsection{变换矩阵}
每个关节的变换矩阵（看起来很复杂，但其实就是旋转+平移的组合）：

$$\bm{T}_{i-1}^{i} = \begin{bmatrix}
c\theta_i c\beta_i - s\theta_i s\alpha_i s\beta_i & -s\theta_i c\alpha_i & c\theta_i s\beta_i + s\theta_i s\alpha_i c\beta_i & a_i c\theta_i \\
s\theta_i c\beta_i + c\theta_i s\alpha_i s\beta_i & c\theta_i c\alpha_i & s\theta_i s\beta_i - c\theta_i s\alpha_i c\beta_i & a_i s\theta_i \\
-c\alpha_i s\beta_i & s\alpha_i & c\alpha_i c\beta_i & d_i \\
0 & 0 & 0 & 1
\end{bmatrix}$$

\textcolor{blue}{\textbf{简化记号}}：
\begin{itemize}
\item $c\theta_i = \cos(\theta_i)$
\item $s\theta_i = \sin(\theta_i)$
\item $c\alpha_i = \cos(\alpha_i)$
\item $s\alpha_i = \sin(\alpha_i)$
\end{itemize}

\textcolor{red}{\textbf{不要被吓到！}} 这个矩阵的本质就是：
\begin{itemize}
\item 左上角3×3部分：旋转
\item 右上角3×1部分：平移
\item 最后一行：数学技巧，让计算更方便
\end{itemize}

\subsection{整个机器人的运动学}
6个关节的机器人，总的变换就是6个矩阵相乘：
$$\bm{T}_{0}^{6} = \bm{T}_{0}^{1} \cdot \bm{T}_{1}^{2} \cdot \bm{T}_{2}^{3} \cdot \bm{T}_{3}^{4} \cdot \bm{T}_{4}^{5} \cdot \bm{T}_{5}^{6}$$

或者简写为：
$$\bm{T}_{0}^{6} = \prod_{i=1}^{6} \bm{T}_{i-1}^{i}(\theta_i)$$

\textcolor{blue}{\textbf{物理意义}}：从基座坐标系一步步变换到末端坐标系。

\section{第三章：误差建模的数学原理}

\subsection{什么是误差？}
误差就是"实际值"和"理论值"的差：
$$\bm{\epsilon} = \bm{p}_{actual} - \bm{p}_{theory}$$

对于6维位姿（3个位置+3个角度）：
$$\bm{\epsilon} = [\epsilon_x, \epsilon_y, \epsilon_z, \epsilon_\alpha, \epsilon_\beta, \epsilon_\gamma]^T$$

\subsection{雅可比矩阵：误差传播的关键}

\subsubsection{什么是雅可比矩阵？}
雅可比矩阵描述了"输入的小变化"如何影响"输出的变化"。

对于机器人：
\begin{itemize}
\item 输入：关节角度 $\bm{\theta} = [\theta_1, \theta_2, \ldots, \theta_6]^T$
\item 输出：末端位姿 $\bm{p} = [x, y, z, \alpha, \beta, \gamma]^T$
\end{itemize}

雅可比矩阵：
$$\bm{J}(\bm{\theta}) = \frac{\partial \bm{p}}{\partial \bm{\theta}} = \begin{bmatrix}
\frac{\partial x}{\partial \theta_1} & \frac{\partial x}{\partial \theta_2} & \cdots & \frac{\partial x}{\partial \theta_6} \\
\frac{\partial y}{\partial \theta_1} & \frac{\partial y}{\partial \theta_2} & \cdots & \frac{\partial y}{\partial \theta_6} \\
\vdots & \vdots & \ddots & \vdots \\
\frac{\partial \gamma}{\partial \theta_1} & \frac{\partial \gamma}{\partial \theta_2} & \cdots & \frac{\partial \gamma}{\partial \theta_6}
\end{bmatrix}$$

\textcolor{blue}{\textbf{物理意义}}：第$(i,j)$个元素告诉我们"第$j$个关节角度变化1度，会让第$i$个输出变化多少"。

\subsubsection{线性化误差模型}
当关节角度有小的误差 $\Delta\bm{\theta}$ 时，末端位姿的误差近似为：
$$\bm{\epsilon} \approx \bm{J}(\bm{\theta}) \Delta\bm{\theta}$$

\textcolor{red}{\textbf{注意}}：这只是线性近似！实际上还有非线性项：
$$\bm{\epsilon} = \bm{J}(\bm{\theta}) \Delta\bm{\theta} + \bm{\epsilon}_{nonlinear}$$

\section{第四章：传统方法的局部最优问题}

\subsection{什么是优化问题？}
优化就是找到最好的解。比如我们想找到一组参数，让预测误差最小：
$$\min_{\bm{w}} \mathcal{L}(\bm{w}) = \min_{\bm{w}} \frac{1}{N} \sum_{i=1}^{N} \|\bm{\epsilon}_i - \hat{\bm{\epsilon}}_i\|^2$$

这里：
\begin{itemize}
\item $\bm{w}$：神经网络的权重参数
\item $\mathcal{L}(\bm{w})$：损失函数（越小越好）
\item $\bm{\epsilon}_i$：第$i$个样本的真实误差
\item $\hat{\bm{\epsilon}}_i$：第$i$个样本的预测误差
\item $\|\cdot\|^2$：平方和（让正负误差都被惩罚）
\end{itemize}

\subsection{什么是局部最优？}
想象你在一个有很多山峰的地形中寻找最高点：
\begin{itemize}
\item \textbf{全局最优}：真正的最高峰
\item \textbf{局部最优}：你周围的小山包的顶点
\end{itemize}

\textcolor{red}{\textbf{问题}}：传统优化算法容易困在局部最优，找不到全局最优。

\subsection{Hessian矩阵：判断最优点性质}
Hessian矩阵是二阶导数矩阵：
$$\bm{H} = \frac{\partial^2 \mathcal{L}}{\partial \bm{w}^2} = \begin{bmatrix}
\frac{\partial^2 \mathcal{L}}{\partial w_1^2} & \frac{\partial^2 \mathcal{L}}{\partial w_1 \partial w_2} & \cdots \\
\frac{\partial^2 \mathcal{L}}{\partial w_2 \partial w_1} & \frac{\partial^2 \mathcal{L}}{\partial w_2^2} & \cdots \\
\vdots & \vdots & \ddots
\end{bmatrix}$$

\textcolor{blue}{\textbf{判断规则}}：
\begin{itemize}
\item 如果 $\bm{H}$ 正定（所有特征值>0）→ 局部最小值
\item 如果 $\bm{H}$ 负定（所有特征值<0）→ 局部最大值  
\item 如果 $\bm{H}$ 有正有负特征值 → 鞍点（不是最优点）
\end{itemize}

\section{第五章：我们的创新点1 - 多目标优化框架}

\subsection{为什么需要多目标？}
传统方法只考虑一个目标，但实际上我们有多个相互冲突的目标：

\begin{align}
\min_{\bm{w}} \quad &f_1(\bm{w}) = \|\bm{\epsilon}_{pos} - \hat{\bm{\epsilon}}_{pos}\|_2 \quad \text{（位置精度）} \\
\min_{\bm{w}} \quad &f_2(\bm{w}) = \|\bm{\epsilon}_{ori} - \hat{\bm{\epsilon}}_{ori}\|_2 \quad \text{（角度精度）} \\
\min_{\bm{w}} \quad &f_3(\bm{w}) = \mathcal{R}(\bm{w}) \quad \text{（模型复杂度）}
\end{align}

\textcolor{red}{\textbf{冲突性}}：
\begin{itemize}
\item 提高位置精度可能降低角度精度
\item 降低模型复杂度可能降低整体精度
\item 需要找到平衡点
\end{itemize}

\subsection{Pareto最优解}
当没有一个解在所有目标上都最好时，我们寻找Pareto最优解集合。

\textcolor{blue}{\textbf{定义}}：解 $\bm{w}_1$ 支配解 $\bm{w}_2$，当且仅当：
\begin{itemize}
\item 在所有目标上，$\bm{w}_1$ 不比 $\bm{w}_2$ 差
\item 在至少一个目标上，$\bm{w}_1$ 比 $\bm{w}_2$ 好
\end{itemize}

数学表达：
$$\bm{w}_1 \prec \bm{w}_2 \iff \forall i: f_i(\bm{w}_1) \leq f_i(\bm{w}_2) \text{ 且 } \exists j: f_j(\bm{w}_1) < f_j(\bm{w}_2)$$

\section{第六章：我们的创新点2 - 物理信息神经网络(PINN)}

\subsection{传统神经网络 vs PINN}

\textbf{传统神经网络}：
$$\mathcal{L}_{traditional} = \frac{1}{N} \sum_{i=1}^{N} \|\bm{\epsilon}_i - \hat{\bm{\epsilon}}_i\|^2$$
只关心数据拟合，不管物理定律。

\textbf{PINN}：
$$\mathcal{L}_{PINN} = \mathcal{L}_{data} + \lambda_{physics} \mathcal{L}_{physics} + \lambda_{boundary} \mathcal{L}_{boundary}$$

\textcolor{blue}{\textbf{创新点}}：把物理定律作为"软约束"加入损失函数！

\subsection{物理约束的具体形式}

\subsubsection{运动学约束}
确保神经网络的预测符合机器人运动学：
$$\mathcal{L}_{kinematics} = \frac{1}{N} \sum_{i=1}^{N} \left\|\frac{\partial \mathcal{F}(\bm{\theta}_i)}{\partial \bm{\theta}} - \bm{J}(\bm{\theta}_i)\right\|_F^2$$

\textcolor{blue}{\textbf{物理意义}}：神经网络学到的"导数"应该等于理论上的雅可比矩阵。

\subsubsection{几何约束}
旋转矩阵必须满足正交性：
$$\mathcal{L}_{geometry} = \|\bm{R}^T\bm{R} - \bm{I}\|_F^2 + (\det(\bm{R}) - 1)^2$$

\textcolor{blue}{\textbf{物理意义}}：
\begin{itemize}
\item $\bm{R}^T\bm{R} = \bm{I}$：旋转矩阵是正交的
\item $\det(\bm{R}) = 1$：行列式为1（不是反射）
\end{itemize}

\section{第七章：我们的创新点3 - 确定性初始化}

\subsection{随机初始化的问题}
传统神经网络随机初始化权重，导致：
\begin{itemize}
\item 每次训练结果不同
\item 可能从很差的起点开始
\item 容易陷入局部最优
\end{itemize}

\subsection{我们的确定性初始化}
基于物理先验知识确定初始权重：
$$\bm{w}_{init} = \arg\min_{\bm{w}} \mathcal{L}_{physics}(\bm{w}) + \alpha \|\bm{w}\|_2^2$$

具体实现：
$$\bm{w}_{init} = (\bm{J}^T\bm{J} + \alpha\bm{I})^{-1}\bm{J}^T\bm{\epsilon}_{training}$$

\textcolor{blue}{\textbf{这是什么？}} 这是最小二乘法的解！
\begin{itemize}
\item $\bm{J}^T\bm{J}$：信息矩阵
\item $\alpha\bm{I}$：正则化项（防止过拟合）
\item $\bm{J}^T\bm{\epsilon}_{training}$：目标向量
\end{itemize}

\textcolor{red}{\textbf{创新点}}：用物理知识给神经网络一个"聪明的起点"！

\section{第八章：数学公式总结与创新点对比}

\subsection{传统方法 vs 我们的方法}

\begin{table}[h]
\centering
\caption{方法对比}
\begin{tabular}{|l|l|l|}
\hline
\textbf{方面} & \textbf{传统方法} & \textbf{我们的方法} \\
\hline
目标函数 & 单目标：$\min \mathcal{L}_{data}$ & 多目标：$\min [f_1, f_2, f_3]$ \\
\hline
物理约束 & 无 & $\mathcal{L}_{PINN} = \mathcal{L}_{data} + \mathcal{L}_{physics}$ \\
\hline
初始化 & 随机 & 确定性：$\bm{w}_{init} = (\bm{J}^T\bm{J} + \alpha\bm{I})^{-1}\bm{J}^T\bm{\epsilon}$ \\
\hline
特征工程 & 经验驱动 & 物理驱动：140维→63维 \\
\hline
\end{tabular}
\end{table}

\subsection{我们的三大创新点}

\begin{framed}
\textcolor{red}{\textbf{创新点1：多目标优化框架}}
\begin{itemize}
\item 同时优化位置精度、角度精度、模型复杂度
\item 使用改进的NSGA-II算法
\item 避免了单目标优化的局限性
\end{itemize}
\end{framed}

\begin{framed}
\textcolor{red}{\textbf{创新点2：物理信息神经网络}}
\begin{itemize}
\item 将机器人运动学、动力学定律嵌入损失函数
\item 确保预测结果符合物理规律
\item 提高泛化能力和预测精度
\end{itemize}
\end{framed}

\begin{framed}
\textcolor{red}{\textbf{创新点3：确定性初始化策略}}
\begin{itemize}
\item 基于物理先验的智能初始化
\item 消除随机性，提高收敛稳定性
\item 有效避免局部最优陷阱
\end{itemize}
\end{framed}

\section{下一部分预告}
在下一部分中，我们将详细讲解：
\begin{itemize}
\item NSGA-II多目标优化算法的具体实现
\item 物理驱动特征工程的140维特征构造
\item 实验结果的数学分析
\item 每个公式的编程实现
\end{itemize}

\end{document}
