anaconda-anon-usage==0.7.1
anaconda_powershell_prompt==1.1.0
anaconda_prompt==1.1.0
annotated-types==0.6.0
archspec==0.2.3
boltons==25.0.0
brotlicffi==*******
certifi==2025.7.14
cffi==1.17.1
charset-normalizer==3.3.2
colorama==0.4.6
conda==25.5.1
conda-anaconda-telemetry==0.2.0
conda-anaconda-tos==0.2.1
conda-content-trust==0.2.0
conda-libmamba-solver==25.4.0
conda-package-handling==2.4.0
conda-package-streaming==0.12.0
cryptography==45.0.3
distro==1.9.0
frozendict==2.4.2
idna==3.7
jsonpatch==1.33
jsonpointer==2.1
libmambapy==2.0.5
markdown-it-py==2.2.0
mdurl==0.1.0
menuinst==2.3.0
packaging==24.2
pip==25.1
platformdirs==4.3.7
pluggy==1.5.0
pycosat==0.6.6
pycparser==2.21
pydantic==2.11.7
pydantic-core==2.33.2
pygments==2.19.1
pysocks==1.7.1
reproc==14.2.4
reproc-cpp==14.2.4
requests==2.32.4
rich==13.9.4
ruamel.yaml==0.18.10
ruamel.yaml.clib==0.2.12
setuptools==78.1.1
tqdm==4.67.1
truststore==0.10.0
typing-extensions==4.12.2
typing-inspection==0.4.0
typing_extensions==4.12.2
urllib3==2.5.0
wheel==0.45.1
win_inet_pton==1.1.0
zstandard==0.23.0

numpy~=2.1.2
pandas~=2.3.1
matplotlib~=3.10.3
seaborn~=0.13.2
scipy~=1.15.3
scikit-learn~=1.7.1
joblib~=1.4.2
torch~=2.7.1+cu118