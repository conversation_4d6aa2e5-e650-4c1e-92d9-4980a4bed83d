# 机器人位姿预测实验项目

## 📋 项目概述

本项目使用机器学习方法建立机器人关节角度与末端执行器位姿之间的映射关系，实现高精度的位姿预测。

**实验目标**: 基于6个关节角度参数预测机器人末端执行器的6个位姿参数（X,Y,Z坐标 + Rx,Ry,Rz欧拉角）

## 📁 文件结构

### 📊 数据文件
- `theta2000.xlsx` - 输入特征数据（2000×6，关节角度，无表头）
- `real2000.xlsx` - 目标数据（2000×6，位姿参数，有表头）

### 📝 实验报告
- `机器人位姿预测实验报告.md` - **主要实验报告**（给老师看）
- `最终分析报告.md` - 详细技术分析报告
- `实验结果汇总.xlsx` - 结果数据表格

### 💻 代码文件
- `实验代码附录.py` - **完整实验代码**（包含详细注释）
- `ml_analysis.py` - 原始分析脚本
- `实验结果表格.py` - 结果表格生成脚本

### 📈 图表文件
- `data_exploration.png` - 数据探索分析图
- `model_performance_comparison.png` - 模型性能对比图
- `数据探索分析.png` - 数据分布图表
- `模型性能对比.png` - 性能热力图

### 📋 其他文件
- `总结与推荐.py` - 实验总结脚本
- `model_performance_report.xlsx` - 详细性能报告

## 🚀 快速开始

### 环境要求
```bash
pip install pandas openpyxl scikit-learn numpy matplotlib seaborn xgboost lightgbm
```

### 运行实验
```bash
# 运行完整实验
python 实验代码附录.py

# 生成结果表格
python 实验结果表格.py

# 查看总结
python 总结与推荐.py
```

## 🎯 主要实验结果

### 最佳模型性能

| 目标变量 | 最佳模型 | R²得分 | RMSE | 性能等级 |
|---------|---------|--------|------|---------|
| X坐标 | LightGBM | 0.9928 | 14.35 | 🏆 卓越 |
| Y坐标 | 神经网络 | 0.9985 | 7.90 | 🏆 卓越 |
| Z坐标 | LightGBM | 0.9964 | 9.68 | 🏆 卓越 |
| Rx角度 | 神经网络 | 0.9454 | 10.43 | 🏆 优秀 |
| Ry角度 | LightGBM | 0.6452 | 4.39 | ✅ 良好 |
| Rz角度 | 神经网络 | 0.5796 | 86.97 | ⚠️ 一般 |

### 模型排名
1. **神经网络** (平均R²: 0.8238) - 复杂非线性关系建模
2. **LightGBM** (平均R²: 0.8146) - 生产环境推荐
3. **XGBoost** (平均R²: 0.7995) - 高性能需求

## 🔍 关键发现

### ✅ 成功点
- **位置坐标预测精度极高** (R² > 0.99)，达到工业级标准
- **数据对应关系的重要性**: 正确处理数据格式是成功的关键
- **算法选择有效**: 树模型和神经网络表现优异

### ⚠️ 挑战点
- **Rz角度预测相对困难**: 可能与机器人奇异性配置相关
- **模型复杂度**: 需要在精度和可解释性间平衡

## 💡 实际应用建议

### 立即可部署
- **位置预测**: 使用LightGBM，精度高、速度快
- **Rx角度预测**: 使用神经网络，精度优秀

### 需要优化
- **Ry角度**: 可接受但建议进一步特征工程
- **Rz角度**: 需要更多数据或物理约束

## 🔧 技术要点

### 数据预处理关键点
```python
# 重要：theta2000.xlsx没有表头，需要特殊处理
X_data = pd.read_excel('theta2000.xlsx', header=None)
X_data.columns = [f'theta_{i+1}' for i in range(6)]

# real2000.xlsx有正常表头
y_data = pd.read_excel('real2000.xlsx')
```

### 模型选择策略
- **位置坐标**: 推荐LightGBM（速度快，精度高）
- **姿态角度**: 推荐神经网络（非线性拟合能力强）
- **生产部署**: 优先考虑LightGBM

## 📚 学习价值

本实验展示了：
1. **数据预处理的重要性**: 正确的数据对应关系是成功的基础
2. **算法对比的方法**: 系统性评估多种机器学习算法
3. **实际应用的考量**: 精度、速度、可解释性的权衡
4. **机器人学的应用**: 机器学习在机器人运动学中的有效性

## 👨‍🎓 适用对象

- 机器人学课程实验
- 机器学习应用项目
- 工程实践案例研究
- 算法对比分析

---

**实验完成时间**: 2025年6月18日  
**技术栈**: Python, scikit-learn, XGBoost, LightGBM, 神经网络  
**实验状态**: ✅ 成功完成，结果优异
