#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理模块
用于加载和预处理机器人关节角度和位姿数据
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns

class DataPreprocessor:
    def __init__(self):
        self.scaler_joints = StandardScaler()
        self.scaler_positions = StandardScaler()
        self.joint_data = None
        self.position_data = None
        
    def load_data(self, theta_file='../theta2000.xlsx', real_file='../real2000.xlsx'):
        """
        加载关节角度和实际位姿数据
        """
        print("正在加载数据...")
        
        # 加载关节角度数据
        try:
            self.joint_data = pd.read_excel(theta_file)
            print(f"关节角度数据形状: {self.joint_data.shape}")
            print(f"关节角度数据列名: {list(self.joint_data.columns)}")
        except Exception as e:
            print(f"加载关节角度数据失败: {e}")
            return False
            
        # 加载实际位姿数据
        try:
            self.position_data = pd.read_excel(real_file)
            print(f"位姿数据形状: {self.position_data.shape}")
            print(f"位姿数据列名: {list(self.position_data.columns)}")
        except Exception as e:
            print(f"加载位姿数据失败: {e}")
            return False
            
        return True
    
    def clean_data(self):
        """
        数据清洗
        """
        print("正在进行数据清洗...")
        
        # 检查缺失值
        joint_missing = self.joint_data.isnull().sum()
        position_missing = self.position_data.isnull().sum()
        
        print("关节角度数据缺失值:")
        print(joint_missing[joint_missing > 0])
        
        print("位姿数据缺失值:")
        print(position_missing[position_missing > 0])
        
        # 删除包含缺失值的行
        initial_joint_rows = len(self.joint_data)
        initial_position_rows = len(self.position_data)
        
        self.joint_data = self.joint_data.dropna()
        self.position_data = self.position_data.dropna()
        
        print(f"关节角度数据: {initial_joint_rows} → {len(self.joint_data)} 行")
        print(f"位姿数据: {initial_position_rows} → {len(self.position_data)} 行")
        
        # 确保两个数据集行数一致
        min_rows = min(len(self.joint_data), len(self.position_data))
        self.joint_data = self.joint_data.iloc[:min_rows]
        self.position_data = self.position_data.iloc[:min_rows]
        
        print(f"最终数据行数: {min_rows}")
        
    def extract_features(self):
        """
        提取特征和目标变量
        """
        print("正在提取特征...")
        
        # 提取关节角度特征 (假设前6列是θ1-θ6)
        joint_columns = [col for col in self.joint_data.columns if 'theta' in col.lower() or 'θ' in col]
        if not joint_columns:
            # 如果没有明确的theta列，使用前6列
            joint_columns = self.joint_data.columns[:6]
        
        self.X = self.joint_data[joint_columns].values
        print(f"关节角度特征形状: {self.X.shape}")
        print(f"使用的关节角度列: {joint_columns}")
        
        # 提取位置目标变量 (X, Y, Z)
        position_columns = [col for col in self.position_data.columns if col.upper() in ['X', 'Y', 'Z']]
        if not position_columns:
            # 如果没有明确的X,Y,Z列，使用前3列
            position_columns = self.position_data.columns[:3]
            
        self.y = self.position_data[position_columns].values
        print(f"位置目标变量形状: {self.y.shape}")
        print(f"使用的位置列: {position_columns}")
        
        return self.X, self.y
    
    def split_data(self, test_size=0.2, random_state=42):
        """
        分割训练集和测试集
        """
        print(f"正在分割数据集 (测试集比例: {test_size})...")
        
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            self.X, self.y, test_size=test_size, random_state=random_state
        )
        
        print(f"训练集: {self.X_train.shape[0]} 样本")
        print(f"测试集: {self.X_test.shape[0]} 样本")
        
        return self.X_train, self.X_test, self.y_train, self.y_test
    
    def normalize_data(self):
        """
        数据标准化
        """
        print("正在进行数据标准化...")
        
        # 标准化关节角度数据
        self.X_train_scaled = self.scaler_joints.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler_joints.transform(self.X_test)
        
        # 标准化位置数据
        self.y_train_scaled = self.scaler_positions.fit_transform(self.y_train)
        self.y_test_scaled = self.scaler_positions.transform(self.y_test)
        
        print("数据标准化完成")
        
        return (self.X_train_scaled, self.X_test_scaled, 
                self.y_train_scaled, self.y_test_scaled)
    
    def visualize_data(self):
        """
        数据可视化
        """
        print("正在生成数据可视化...")
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 关节角度分布
        for i in range(min(6, self.X.shape[1])):
            row = i // 3
            col = i % 3
            axes[row, col].hist(self.X[:, i], bins=50, alpha=0.7)
            axes[row, col].set_title(f'关节角度 θ{i+1} 分布')
            axes[row, col].set_xlabel('角度 (弧度)')
            axes[row, col].set_ylabel('频次')
        
        plt.tight_layout()
        plt.savefig('新实验设计/关节角度分布.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 位置数据分布
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        position_labels = ['X', 'Y', 'Z']
        
        for i in range(3):
            axes[i].hist(self.y[:, i], bins=50, alpha=0.7)
            axes[i].set_title(f'位置 {position_labels[i]} 分布')
            axes[i].set_xlabel('位置 (mm)')
            axes[i].set_ylabel('频次')
        
        plt.tight_layout()
        plt.savefig('新实验设计/位置分布.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 3D位置散点图
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        scatter = ax.scatter(self.y[:, 0], self.y[:, 1], self.y[:, 2], 
                           c=range(len(self.y)), cmap='viridis', alpha=0.6)
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_zlabel('Z (mm)')
        ax.set_title('机器人工作空间3D分布')
        
        plt.colorbar(scatter)
        plt.savefig('新实验设计/工作空间3D.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def get_sample_data(self, n_samples=50):
        """
        获取用于展示的样本数据
        """
        indices = np.random.choice(len(self.X_test), n_samples, replace=False)
        
        sample_X = self.X_test[indices]
        sample_y = self.y_test[indices]
        
        return sample_X, sample_y, indices
    
    def save_processed_data(self):
        """
        保存预处理后的数据
        """
        print("正在保存预处理后的数据...")
        
        # 保存训练集
        train_data = pd.DataFrame(
            np.hstack([self.X_train, self.y_train]),
            columns=[f'theta_{i+1}' for i in range(self.X_train.shape[1])] + ['X', 'Y', 'Z']
        )
        train_data.to_excel('新实验设计/训练数据.xlsx', index=False)
        
        # 保存测试集
        test_data = pd.DataFrame(
            np.hstack([self.X_test, self.y_test]),
            columns=[f'theta_{i+1}' for i in range(self.X_test.shape[1])] + ['X', 'Y', 'Z']
        )
        test_data.to_excel('新实验设计/测试数据.xlsx', index=False)
        
        print("数据保存完成")

def main():
    """
    主函数 - 执行完整的数据预处理流程
    """
    print("=== 机器人位姿预测实验 - 数据预处理 ===\n")
    
    # 创建预处理器
    preprocessor = DataPreprocessor()
    
    # 加载数据
    if not preprocessor.load_data():
        print("数据加载失败，程序退出")
        return
    
    # 数据清洗
    preprocessor.clean_data()
    
    # 特征提取
    X, y = preprocessor.extract_features()
    
    # 数据分割
    X_train, X_test, y_train, y_test = preprocessor.split_data()
    
    # 数据标准化
    X_train_scaled, X_test_scaled, y_train_scaled, y_test_scaled = preprocessor.normalize_data()
    
    # 数据可视化
    preprocessor.visualize_data()
    
    # 保存处理后的数据
    preprocessor.save_processed_data()
    
    # 获取样本数据
    sample_X, sample_y, sample_indices = preprocessor.get_sample_data()
    
    print(f"\n=== 数据预处理完成 ===")
    print(f"总样本数: {len(X)}")
    print(f"训练样本数: {len(X_train)}")
    print(f"测试样本数: {len(X_test)}")
    print(f"展示样本数: {len(sample_X)}")
    
    return preprocessor

if __name__ == "__main__":
    preprocessor = main()
