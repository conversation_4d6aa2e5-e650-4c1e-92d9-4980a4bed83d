#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终完整三方对比实验
基于修正后的理论计算，进行位置和角度的完整对比
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
import lightgbm as lgb
from 理论计算模块 import RobotKinematics
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class FinalComparison:
    """最终完整对比分析"""
    
    def __init__(self):
        self.robot = RobotKinematics()
        
    def load_data(self):
        """加载数据"""
        print("=== 加载数据 ===")
        
        # 加载关节角度数据
        self.joint_data = pd.read_excel('../theta2000.xlsx', header=None)
        self.joint_data.columns = [f'theta_{i+1}' for i in range(6)]
        
        # 加载实测数据
        self.measured_data = pd.read_excel('../real2000.xlsx')
        
        print(f"关节角度数据: {self.joint_data.shape}")
        print(f"实测数据: {self.measured_data.shape}")
        
        return True
    
    def calculate_theoretical_poses(self):
        """计算理论位姿"""
        print("\n=== 计算理论位姿 ===")
        
        theoretical_poses = []
        for i in range(len(self.joint_data)):
            joint_angles = self.joint_data.iloc[i].values
            pose = self.robot.forward_kinematics(joint_angles)
            theoretical_poses.append(pose)
        
        self.theoretical_data = pd.DataFrame(theoretical_poses, 
                                           columns=['X_theory', 'Y_theory', 'Z_theory',
                                                   'Rx_theory', 'Ry_theory', 'Rz_theory'])
        
        print(f"理论位姿计算完成: {self.theoretical_data.shape}")
        return True
    
    def train_ml_models(self):
        """训练机器学习模型"""
        print("\n=== 训练机器学习模型 ===")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            self.joint_data, self.measured_data,
            test_size=0.2, random_state=42
        )
        
        # 训练LightGBM模型预测所有6个坐标
        self.ml_models = {}
        self.ml_predictions = np.zeros((len(self.joint_data), 6))
        
        coord_names = ['X', 'Y', 'Z', 'Rx', 'Ry', 'Rz']
        for i, coord in enumerate(coord_names):
            print(f"训练 {coord} 坐标预测模型...")
            
            model = lgb.LGBMRegressor(n_estimators=100, random_state=42, verbose=-1)
            model.fit(X_train, y_train.iloc[:, i])
            
            # 预测所有样本
            predictions = model.predict(self.joint_data)
            self.ml_predictions[:, i] = predictions
            
            self.ml_models[coord] = model
            
            # 评估性能
            test_pred = model.predict(X_test)
            rmse = np.sqrt(np.mean((test_pred - y_test.iloc[:, i])**2))
            print(f"  {coord} 坐标 RMSE: {rmse:.3f}")
        
        self.ml_data = pd.DataFrame(self.ml_predictions, 
                                   columns=['X_ml', 'Y_ml', 'Z_ml', 'Rx_ml', 'Ry_ml', 'Rz_ml'])
        
        print("机器学习模型训练完成")
        return True
    
    def calculate_comprehensive_errors(self):
        """计算综合误差"""
        print("\n=== 计算综合误差 ===")
        
        # 提取数据
        measured = self.measured_data.values
        theoretical = self.theoretical_data.values
        ml_pred = self.ml_predictions
        
        # 分别计算位置和角度误差
        # 位置误差 (前3列)
        self.theory_pos_errors = np.sqrt(np.sum((theoretical[:, :3] - measured[:, :3])**2, axis=1))
        self.ml_pos_errors = np.sqrt(np.sum((ml_pred[:, :3] - measured[:, :3])**2, axis=1))
        
        # 角度误差 (后3列) - 处理角度包装问题
        self.theory_angle_errors = []
        self.ml_angle_errors = []
        
        for i in range(len(measured)):
            # 理论角度误差
            theory_angle_diff = theoretical[i, 3:6] - measured[i, 3:6]
            theory_angle_diff = self.wrap_angle_differences(theory_angle_diff)
            theory_angle_error = np.sqrt(np.sum(theory_angle_diff**2))
            self.theory_angle_errors.append(theory_angle_error)
            
            # 机器学习角度误差
            ml_angle_diff = ml_pred[i, 3:6] - measured[i, 3:6]
            ml_angle_diff = self.wrap_angle_differences(ml_angle_diff)
            ml_angle_error = np.sqrt(np.sum(ml_angle_diff**2))
            self.ml_angle_errors.append(ml_angle_error)
        
        self.theory_angle_errors = np.array(self.theory_angle_errors)
        self.ml_angle_errors = np.array(self.ml_angle_errors)
        
        # 综合位姿误差
        self.theory_total_errors = np.sqrt(self.theory_pos_errors**2 + self.theory_angle_errors**2)
        self.ml_total_errors = np.sqrt(self.ml_pos_errors**2 + self.ml_angle_errors**2)
        
        # 统计信息
        print("位置误差统计:")
        print(f"理论计算: 平均 {np.mean(self.theory_pos_errors):.3f} mm, 标准差 {np.std(self.theory_pos_errors):.3f} mm")
        print(f"机器学习: 平均 {np.mean(self.ml_pos_errors):.3f} mm, 标准差 {np.std(self.ml_pos_errors):.3f} mm")
        
        print("\n角度误差统计:")
        print(f"理论计算: 平均 {np.mean(self.theory_angle_errors):.3f} 度, 标准差 {np.std(self.theory_angle_errors):.3f} 度")
        print(f"机器学习: 平均 {np.mean(self.ml_angle_errors):.3f} 度, 标准差 {np.std(self.ml_angle_errors):.3f} 度")
        
        return True
    
    def wrap_angle_differences(self, angle_diffs):
        """处理角度差值的包装问题"""
        wrapped_diffs = []
        for diff in angle_diffs:
            # 将角度差值包装到[-180, 180]范围
            while diff > 180:
                diff -= 360
            while diff < -180:
                diff += 360
            wrapped_diffs.append(diff)
        return np.array(wrapped_diffs)
    
    def create_comprehensive_showcase(self, n_samples=20):
        """创建综合样本展示"""
        print(f"\n=== 创建 {n_samples} 个样本展示 ===")
        
        # 选择代表性样本
        indices = np.linspace(0, len(self.joint_data)-1, n_samples, dtype=int)
        
        showcase_data = []
        
        for idx in indices:
            # 实测值
            measured = self.measured_data.iloc[idx].values
            
            # 理论值
            theoretical = self.theoretical_data.iloc[idx].values
            
            # 预测值
            ml_pred = self.ml_predictions[idx]
            
            # 误差
            pos_theory_error = self.theory_pos_errors[idx]
            pos_ml_error = self.ml_pos_errors[idx]
            angle_theory_error = self.theory_angle_errors[idx]
            angle_ml_error = self.ml_angle_errors[idx]
            
            sample = {
                '样本编号': idx,
                '关节角度': f"[{', '.join([f'{self.joint_data.iloc[idx, i]:.1f}' for i in range(6)])}]",
                
                # 位置
                'X_实测': f"{measured[0]:.1f}",
                'Y_实测': f"{measured[1]:.1f}",
                'Z_实测': f"{measured[2]:.1f}",
                'X_理论': f"{theoretical[0]:.1f}",
                'Y_理论': f"{theoretical[1]:.1f}",
                'Z_理论': f"{theoretical[2]:.1f}",
                'X_预测': f"{ml_pred[0]:.1f}",
                'Y_预测': f"{ml_pred[1]:.1f}",
                'Z_预测': f"{ml_pred[2]:.1f}",
                
                # 角度
                'Rx_实测': f"{measured[3]:.1f}",
                'Ry_实测': f"{measured[4]:.1f}",
                'Rz_实测': f"{measured[5]:.1f}",
                'Rx_理论': f"{theoretical[3]:.1f}",
                'Ry_理论': f"{theoretical[4]:.1f}",
                'Rz_理论': f"{theoretical[5]:.1f}",
                'Rx_预测': f"{ml_pred[3]:.1f}",
                'Ry_预测': f"{ml_pred[4]:.1f}",
                'Rz_预测': f"{ml_pred[5]:.1f}",
                
                # 误差
                '位置理论误差': f"{pos_theory_error:.3f}",
                '位置预测误差': f"{pos_ml_error:.3f}",
                '角度理论误差': f"{angle_theory_error:.3f}",
                '角度预测误差': f"{angle_ml_error:.3f}",
                
                '位置最优': '理论' if pos_theory_error < pos_ml_error else '预测',
                '角度最优': '理论' if angle_theory_error < angle_ml_error else '预测'
            }
            
            showcase_data.append(sample)
        
        showcase_df = pd.DataFrame(showcase_data)
        showcase_df.to_excel('最终完整对比展示.xlsx', index=False)
        
        print(f"完整对比展示数据已保存: {len(showcase_df)} 个样本")
        return showcase_df
    
    def plot_comprehensive_comparison(self):
        """绘制综合对比图表"""
        print("\n=== 生成综合对比图表 ===")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 位置误差对比
        axes[0, 0].boxplot([self.theory_pos_errors, self.ml_pos_errors], 
                          labels=['理论计算', '机器学习'])
        axes[0, 0].set_title('位置误差分布对比')
        axes[0, 0].set_ylabel('位置误差 (mm)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 角度误差对比
        axes[0, 1].boxplot([self.theory_angle_errors, self.ml_angle_errors], 
                          labels=['理论计算', '机器学习'])
        axes[0, 1].set_title('角度误差分布对比')
        axes[0, 1].set_ylabel('角度误差 (度)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 位置误差散点图
        axes[0, 2].scatter(self.theory_pos_errors, self.ml_pos_errors, alpha=0.6)
        max_pos_error = max(max(self.theory_pos_errors), max(self.ml_pos_errors))
        axes[0, 2].plot([0, max_pos_error], [0, max_pos_error], 'r--', alpha=0.8)
        axes[0, 2].set_xlabel('理论计算位置误差 (mm)')
        axes[0, 2].set_ylabel('机器学习位置误差 (mm)')
        axes[0, 2].set_title('位置误差对比散点图')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 角度误差散点图
        axes[1, 0].scatter(self.theory_angle_errors, self.ml_angle_errors, alpha=0.6)
        max_angle_error = max(max(self.theory_angle_errors), max(self.ml_angle_errors))
        axes[1, 0].plot([0, max_angle_error], [0, max_angle_error], 'r--', alpha=0.8)
        axes[1, 0].set_xlabel('理论计算角度误差 (度)')
        axes[1, 0].set_ylabel('机器学习角度误差 (度)')
        axes[1, 0].set_title('角度误差对比散点图')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 位置误差直方图
        axes[1, 1].hist(self.theory_pos_errors, bins=30, alpha=0.7, label='理论计算', color='blue')
        axes[1, 1].hist(self.ml_pos_errors, bins=30, alpha=0.7, label='机器学习', color='red')
        axes[1, 1].set_xlabel('位置误差 (mm)')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].set_title('位置误差分布直方图')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        # 角度误差直方图
        axes[1, 2].hist(self.theory_angle_errors, bins=30, alpha=0.7, label='理论计算', color='blue')
        axes[1, 2].hist(self.ml_angle_errors, bins=30, alpha=0.7, label='机器学习', color='red')
        axes[1, 2].set_xlabel('角度误差 (度)')
        axes[1, 2].set_ylabel('频次')
        axes[1, 2].set_title('角度误差分布直方图')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('最终完整三方对比.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_final_report(self):
        """生成最终报告"""
        print("\n" + "="*80)
        print("最终完整三方对比实验报告")
        print("="*80)
        
        # 计算统计指标
        pos_theory_stats = {
            'mean': np.mean(self.theory_pos_errors),
            'std': np.std(self.theory_pos_errors),
            'max': np.max(self.theory_pos_errors),
            'p95': np.percentile(self.theory_pos_errors, 95)
        }
        
        pos_ml_stats = {
            'mean': np.mean(self.ml_pos_errors),
            'std': np.std(self.ml_pos_errors),
            'max': np.max(self.ml_pos_errors),
            'p95': np.percentile(self.ml_pos_errors, 95)
        }
        
        angle_theory_stats = {
            'mean': np.mean(self.theory_angle_errors),
            'std': np.std(self.theory_angle_errors),
            'max': np.max(self.theory_angle_errors),
            'p95': np.percentile(self.theory_angle_errors, 95)
        }
        
        angle_ml_stats = {
            'mean': np.mean(self.ml_angle_errors),
            'std': np.std(self.ml_angle_errors),
            'max': np.max(self.ml_angle_errors),
            'p95': np.percentile(self.ml_angle_errors, 95)
        }
        
        # 优势统计
        pos_theory_wins = np.sum(self.theory_pos_errors < self.ml_pos_errors)
        angle_theory_wins = np.sum(self.theory_angle_errors < self.ml_angle_errors)
        
        print(f"\n📊 位置精度对比:")
        print(f"样本总数: {len(self.theory_pos_errors)}")
        print(f"理论计算优于机器学习: {pos_theory_wins} 个样本 ({pos_theory_wins/len(self.theory_pos_errors)*100:.1f}%)")
        
        print(f"\n📈 位置误差统计:")
        print(f"{'指标':<12} {'理论计算':<12} {'机器学习':<12} {'优势':<8}")
        print("-" * 50)
        print(f"{'平均误差':<12} {pos_theory_stats['mean']:<12.3f} {pos_ml_stats['mean']:<12.3f} {'理论' if pos_theory_stats['mean'] < pos_ml_stats['mean'] else '机器学习':<8}")
        print(f"{'标准差':<12} {pos_theory_stats['std']:<12.3f} {pos_ml_stats['std']:<12.3f} {'理论' if pos_theory_stats['std'] < pos_ml_stats['std'] else '机器学习':<8}")
        print(f"{'最大误差':<12} {pos_theory_stats['max']:<12.3f} {pos_ml_stats['max']:<12.3f} {'理论' if pos_theory_stats['max'] < pos_ml_stats['max'] else '机器学习':<8}")
        
        print(f"\n📊 角度精度对比:")
        print(f"理论计算优于机器学习: {angle_theory_wins} 个样本 ({angle_theory_wins/len(self.theory_angle_errors)*100:.1f}%)")
        
        print(f"\n📈 角度误差统计:")
        print(f"{'指标':<12} {'理论计算':<12} {'机器学习':<12} {'优势':<8}")
        print("-" * 50)
        print(f"{'平均误差':<12} {angle_theory_stats['mean']:<12.3f} {angle_ml_stats['mean']:<12.3f} {'理论' if angle_theory_stats['mean'] < angle_ml_stats['mean'] else '机器学习':<8}")
        print(f"{'标准差':<12} {angle_theory_stats['std']:<12.3f} {angle_ml_stats['std']:<12.3f} {'理论' if angle_theory_stats['std'] < angle_ml_stats['std'] else '机器学习':<8}")
        print(f"{'最大误差':<12} {angle_theory_stats['max']:<12.3f} {angle_ml_stats['max']:<12.3f} {'理论' if angle_theory_stats['max'] < angle_ml_stats['max'] else '机器学习':<8}")
        
        print(f"\n🎯 总体结论:")
        if pos_theory_stats['mean'] < pos_ml_stats['mean'] and angle_theory_stats['mean'] < angle_ml_stats['mean']:
            print("✅ 理论计算在位置和角度预测上均优于机器学习")
        elif pos_theory_stats['mean'] < pos_ml_stats['mean']:
            print("✅ 理论计算在位置预测上优于机器学习，角度预测需要进一步优化")
        else:
            print("⚖️ 两种方法各有优势，建议根据具体需求选择")
        
        print(f"\n📁 生成文件:")
        print("• 最终完整对比展示.xlsx - 详细样本对比")
        print("• 最终完整三方对比.png - 可视化对比图表")

def main():
    """主函数"""
    print("开始最终完整三方对比实验...")
    
    # 创建对比分析器
    comparison = FinalComparison()
    
    # 执行实验流程
    if not comparison.load_data():
        return False
    
    if not comparison.calculate_theoretical_poses():
        return False
    
    if not comparison.train_ml_models():
        return False
    
    if not comparison.calculate_comprehensive_errors():
        return False
    
    # 生成结果
    showcase_df = comparison.create_comprehensive_showcase(20)
    comparison.plot_comprehensive_comparison()
    comparison.generate_final_report()
    
    print("\n✅ 最终完整三方对比实验完成！")
    return True

if __name__ == "__main__":
    main()
