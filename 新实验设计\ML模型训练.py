#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模型训练模块
基于支持向量回归(SVR)训练机器人位姿预测模型
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.svm import SVR
from sklearn.ensemble import RandomForestRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import GridSearchCV, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.multioutput import MultiOutputRegressor
import joblib
import time

class MLModelTrainer:
    """
    机器学习模型训练类
    """
    
    def __init__(self):
        self.models = {}
        self.best_model = None
        self.best_model_name = None
        self.training_history = {}
        
    def create_models(self):
        """
        创建不同的机器学习模型
        """
        print("正在创建机器学习模型...")
        
        # SVR模型 (论文主要使用的模型)
        svr_model = MultiOutputRegressor(
            SVR(kernel='rbf', gamma='scale', C=100, epsilon=0.1)
        )
        
        # 随机森林模型 (对比模型)
        rf_model = RandomForestRegressor(
            n_estimators=100, 
            max_depth=20, 
            random_state=42,
            n_jobs=-1
        )
        
        # 神经网络模型 (对比模型)
        mlp_model = MultiOutputRegressor(
            MLPRegressor(
                hidden_layer_sizes=(100, 50, 25),
                activation='relu',
                solver='adam',
                max_iter=1000,
                random_state=42
            )
        )
        
        self.models = {
            'SVR': svr_model,
            'RandomForest': rf_model,
            'MLP': mlp_model
        }
        
        print(f"创建了 {len(self.models)} 个模型: {list(self.models.keys())}")
        
    def optimize_svr_hyperparameters(self, X_train, y_train):
        """
        优化SVR超参数
        """
        print("正在优化SVR超参数...")
        
        # 定义参数网格
        param_grid = {
            'estimator__C': [10, 50, 100, 200],
            'estimator__gamma': ['scale', 'auto', 0.001, 0.01, 0.1],
            'estimator__epsilon': [0.01, 0.1, 0.2, 0.5]
        }
        
        # 创建基础SVR模型
        base_svr = MultiOutputRegressor(SVR(kernel='rbf'))
        
        # 网格搜索
        grid_search = GridSearchCV(
            base_svr, 
            param_grid, 
            cv=5, 
            scoring='neg_mean_squared_error',
            n_jobs=-1,
            verbose=1
        )
        
        # 训练
        grid_search.fit(X_train, y_train)
        
        # 更新最优SVR模型
        self.models['SVR'] = grid_search.best_estimator_
        
        print(f"最优SVR参数: {grid_search.best_params_}")
        print(f"最优交叉验证得分: {-grid_search.best_score_:.4f}")
        
        return grid_search.best_params_
    
    def train_models(self, X_train, y_train, X_test, y_test, optimize_svr=True):
        """
        训练所有模型
        """
        print("=== 开始训练模型 ===\n")
        
        if not self.models:
            self.create_models()
        
        # 优化SVR超参数
        if optimize_svr:
            self.optimize_svr_hyperparameters(X_train, y_train)
        
        training_results = {}
        
        for name, model in self.models.items():
            print(f"正在训练 {name} 模型...")
            
            start_time = time.time()
            
            # 训练模型
            model.fit(X_train, y_train)
            
            training_time = time.time() - start_time
            
            # 预测
            y_train_pred = model.predict(X_train)
            y_test_pred = model.predict(X_test)
            
            # 计算评估指标
            train_metrics = self.calculate_metrics(y_train, y_train_pred)
            test_metrics = self.calculate_metrics(y_test, y_test_pred)
            
            # 交叉验证
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, 
                                      scoring='neg_mean_squared_error')
            
            training_results[name] = {
                'model': model,
                'training_time': training_time,
                'train_metrics': train_metrics,
                'test_metrics': test_metrics,
                'cv_score_mean': -cv_scores.mean(),
                'cv_score_std': cv_scores.std(),
                'y_test_pred': y_test_pred
            }
            
            print(f"{name} 训练完成:")
            print(f"  训练时间: {training_time:.2f}s")
            print(f"  测试RMSE: {test_metrics['rmse']:.4f}")
            print(f"  测试MAE: {test_metrics['mae']:.4f}")
            print(f"  测试R²: {test_metrics['r2']:.4f}")
            print(f"  交叉验证RMSE: {-cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            print()
        
        self.training_history = training_results
        
        # 选择最佳模型 (基于测试集RMSE)
        best_rmse = float('inf')
        for name, results in training_results.items():
            if results['test_metrics']['rmse'] < best_rmse:
                best_rmse = results['test_metrics']['rmse']
                self.best_model = results['model']
                self.best_model_name = name
        
        print(f"最佳模型: {self.best_model_name} (RMSE: {best_rmse:.4f})")
        
        return training_results
    
    def calculate_metrics(self, y_true, y_pred):
        """
        计算评估指标
        """
        # 位置误差 (欧几里得距离)
        position_errors = np.linalg.norm(y_true - y_pred, axis=1)
        
        metrics = {
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'mae': mean_absolute_error(y_true, y_pred),
            'r2': r2_score(y_true, y_pred),
            'mean_position_error': np.mean(position_errors),
            'max_position_error': np.max(position_errors),
            'std_position_error': np.std(position_errors),
            'rmse_x': np.sqrt(mean_squared_error(y_true[:, 0], y_pred[:, 0])),
            'rmse_y': np.sqrt(mean_squared_error(y_true[:, 1], y_pred[:, 1])),
            'rmse_z': np.sqrt(mean_squared_error(y_true[:, 2], y_pred[:, 2]))
        }
        
        return metrics
    
    def visualize_training_results(self):
        """
        可视化训练结果
        """
        if not self.training_history:
            print("没有训练历史数据可视化")
            return
        
        # 创建图形
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 模型性能对比
        model_names = list(self.training_history.keys())
        test_rmse = [self.training_history[name]['test_metrics']['rmse'] for name in model_names]
        test_mae = [self.training_history[name]['test_metrics']['mae'] for name in model_names]
        test_r2 = [self.training_history[name]['test_metrics']['r2'] for name in model_names]
        
        axes[0, 0].bar(model_names, test_rmse, alpha=0.7)
        axes[0, 0].set_ylabel('RMSE')
        axes[0, 0].set_title('模型RMSE对比')
        axes[0, 0].grid(True)
        
        axes[0, 1].bar(model_names, test_mae, alpha=0.7)
        axes[0, 1].set_ylabel('MAE')
        axes[0, 1].set_title('模型MAE对比')
        axes[0, 1].grid(True)
        
        axes[0, 2].bar(model_names, test_r2, alpha=0.7)
        axes[0, 2].set_ylabel('R²')
        axes[0, 2].set_title('模型R²对比')
        axes[0, 2].grid(True)
        
        # 最佳模型的预测结果可视化
        best_results = self.training_history[self.best_model_name]
        y_test_pred = best_results['y_test_pred']
        
        # 这里需要实际的y_test数据，暂时用预测数据演示
        # 在实际使用时，需要传入真实的y_test
        
        axes[1, 0].text(0.5, 0.5, f'最佳模型: {self.best_model_name}\n'
                                  f'RMSE: {best_results["test_metrics"]["rmse"]:.4f}\n'
                                  f'MAE: {best_results["test_metrics"]["mae"]:.4f}\n'
                                  f'R²: {best_results["test_metrics"]["r2"]:.4f}',
                       ha='center', va='center', transform=axes[1, 0].transAxes,
                       fontsize=12, bbox=dict(boxstyle='round', facecolor='lightblue'))
        axes[1, 0].set_title('最佳模型性能')
        axes[1, 0].axis('off')
        
        # 训练时间对比
        training_times = [self.training_history[name]['training_time'] for name in model_names]
        axes[1, 1].bar(model_names, training_times, alpha=0.7)
        axes[1, 1].set_ylabel('训练时间 (秒)')
        axes[1, 1].set_title('模型训练时间对比')
        axes[1, 1].grid(True)
        
        # 交叉验证结果
        cv_means = [self.training_history[name]['cv_score_mean'] for name in model_names]
        cv_stds = [self.training_history[name]['cv_score_std'] for name in model_names]
        
        axes[1, 2].bar(model_names, cv_means, yerr=cv_stds, alpha=0.7, capsize=5)
        axes[1, 2].set_ylabel('交叉验证RMSE')
        axes[1, 2].set_title('交叉验证结果')
        axes[1, 2].grid(True)
        
        plt.tight_layout()
        plt.savefig('新实验设计/ML模型训练结果.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_models(self):
        """
        保存训练好的模型
        """
        print("正在保存模型...")
        
        for name, model in self.models.items():
            filename = f'新实验设计/{name}_model.joblib'
            joblib.dump(model, filename)
            print(f"已保存 {name} 模型到: {filename}")
        
        # 保存最佳模型
        if self.best_model is not None:
            joblib.dump(self.best_model, '新实验设计/best_model.joblib')
            print(f"已保存最佳模型 ({self.best_model_name}) 到: 新实验设计/best_model.joblib")
    
    def save_training_results(self, filename='新实验设计/ML训练结果.xlsx'):
        """
        保存训练结果
        """
        if not self.training_history:
            print("没有训练结果可保存")
            return
        
        # 创建结果汇总
        summary_data = []
        for name, results in self.training_history.items():
            summary_data.append({
                '模型': name,
                '训练时间(秒)': results['training_time'],
                '训练RMSE': results['train_metrics']['rmse'],
                '测试RMSE': results['test_metrics']['rmse'],
                '训练MAE': results['train_metrics']['mae'],
                '测试MAE': results['test_metrics']['mae'],
                '训练R²': results['train_metrics']['r2'],
                '测试R²': results['test_metrics']['r2'],
                '交叉验证RMSE均值': results['cv_score_mean'],
                '交叉验证RMSE标准差': results['cv_score_std'],
                '平均位置误差': results['test_metrics']['mean_position_error'],
                '最大位置误差': results['test_metrics']['max_position_error']
            })
        
        summary_df = pd.DataFrame(summary_data)
        
        # 保存到Excel
        with pd.ExcelWriter(filename) as writer:
            summary_df.to_excel(writer, sheet_name='模型对比', index=False)
            
            # 保存详细指标
            for name, results in self.training_history.items():
                metrics_df = pd.DataFrame([results['test_metrics']], index=[name])
                metrics_df.to_excel(writer, sheet_name=f'{name}_详细指标')
        
        print(f"训练结果已保存到: {filename}")
        
        return summary_df
    
    def predict(self, X):
        """
        使用最佳模型进行预测
        """
        if self.best_model is None:
            raise ValueError("没有训练好的模型，请先训练模型")
        
        return self.best_model.predict(X)
    
    def load_model(self, model_path):
        """
        加载已保存的模型
        """
        self.best_model = joblib.load(model_path)
        print(f"已加载模型: {model_path}")

def main():
    """
    主函数 - 执行ML模型训练
    """
    print("=== 机器人位姿预测实验 - ML模型训练 ===\n")
    
    # 这里需要从数据预处理模块获取数据
    print("注意: 请先运行数据预处理模块获取训练数据")
    
    return None

if __name__ == "__main__":
    main()
