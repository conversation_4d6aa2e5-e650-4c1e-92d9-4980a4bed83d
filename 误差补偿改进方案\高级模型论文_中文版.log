This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.6.29)  12 JUL 2025 23:57
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**c:/Users/<USER>/Desktop/zytable/误差补偿改进方案/高级模型论文_中文版.tex
(c:/Users/<USER>/Desktop/zytable/误差补偿改进方案/高级模型论文_中文版.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(c:/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(c:/texlive/2025/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (c:/texlive/2025/texmf-dist/tex/latex/ctex/ctex.sty (c:/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (c:/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count270
\l__pdf_internal_box=\box52
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)
 (c:/texlive/2025/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (c:/texlive/2025/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (c:/texlive/2025/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (c:/texlive/2025/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count271
\l__ctex_tmp_box=\box53
\l__ctex_tmp_dim=\dimen142
\g__ctex_section_depth_int=\count272
\g__ctex_font_size_int=\count273
 (c:/texlive/2025/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
) (c:/texlive/2025/texmf-dist/tex/latex/ctex/engine/ctex-engine-pdftex.def
File: ctex-engine-pdftex.def 2022/07/14 v2.5.10 (pdf)LaTeX adapter (CTEX)
 (c:/texlive/2025/texmf-dist/tex/latex/cjk/texinput/CJKutf8.sty
Package: CJKutf8 2021/10/16 4.8.5
 (c:/texlive/2025/texmf-dist/tex/generic/iftex/ifpdf.sty
Package: ifpdf 2019/10/25 v3.4 ifpdf legacy package. Use iftex instead.
 (c:/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)) (c:/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
) (c:/texlive/2025/texmf-dist/tex/latex/cjk/texinput/CJK.sty
Package: CJK 2021/10/16 4.8.5
 (c:/texlive/2025/texmf-dist/tex/latex/cjk/texinput/mule/MULEenc.sty
Package: MULEenc 2021/10/16 4.8.5
) (c:/texlive/2025/texmf-dist/tex/latex/cjk/texinput/CJK.enc
File: CJK.enc 2021/10/16 4.8.5
Now handling font encoding C00 ...
... no UTF-8 mapping file for font encoding C00
Now handling font encoding C05 ...
... no UTF-8 mapping file for font encoding C05
Now handling font encoding C09 ...
... no UTF-8 mapping file for font encoding C09
Now handling font encoding C10 ...
... no UTF-8 mapping file for font encoding C10
Now handling font encoding C20 ...
... no UTF-8 mapping file for font encoding C20
Now handling font encoding C19 ...
... no UTF-8 mapping file for font encoding C19
Now handling font encoding C40 ...
... no UTF-8 mapping file for font encoding C40
Now handling font encoding C42 ...
... no UTF-8 mapping file for font encoding C42
Now handling font encoding C43 ...
... no UTF-8 mapping file for font encoding C43
Now handling font encoding C50 ...
... no UTF-8 mapping file for font encoding C50
Now handling font encoding C52 ...
... no UTF-8 mapping file for font encoding C52
Now handling font encoding C49 ...
... no UTF-8 mapping file for font encoding C49
Now handling font encoding C60 ...
... no UTF-8 mapping file for font encoding C60
Now handling font encoding C61 ...
... no UTF-8 mapping file for font encoding C61
Now handling font encoding C63 ...
... no UTF-8 mapping file for font encoding C63
Now handling font encoding C64 ...
... no UTF-8 mapping file for font encoding C64
Now handling font encoding C65 ...
... no UTF-8 mapping file for font encoding C65
Now handling font encoding C70 ...
... no UTF-8 mapping file for font encoding C70
Now handling font encoding C31 ...
... no UTF-8 mapping file for font encoding C31
Now handling font encoding C32 ...
... no UTF-8 mapping file for font encoding C32
Now handling font encoding C33 ...
... no UTF-8 mapping file for font encoding C33
Now handling font encoding C34 ...
... no UTF-8 mapping file for font encoding C34
Now handling font encoding C35 ...
... no UTF-8 mapping file for font encoding C35
Now handling font encoding C36 ...
... no UTF-8 mapping file for font encoding C36
Now handling font encoding C37 ...
... no UTF-8 mapping file for font encoding C37
Now handling font encoding C80 ...
... no UTF-8 mapping file for font encoding C80
Now handling font encoding C81 ...
... no UTF-8 mapping file for font encoding C81
Now handling font encoding C01 ...
... no UTF-8 mapping file for font encoding C01
Now handling font encoding C11 ...
... no UTF-8 mapping file for font encoding C11
Now handling font encoding C21 ...
... no UTF-8 mapping file for font encoding C21
Now handling font encoding C41 ...
... no UTF-8 mapping file for font encoding C41
Now handling font encoding C62 ...
... no UTF-8 mapping file for font encoding C62
)
\CJK@indent=\box54
) (c:/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)) (c:/texlive/2025/texmf-dist/tex/latex/cjkpunct/CJKpunct.sty
Package: CJKpunct 2016/05/14 4.8.4
\CJKpunct@cnta=\count274
\CJKpunct@cntb=\count275
\CJKpunct@cntc=\count276
\CJKpunct@cntd=\count277
\CJKpunct@cnte=\count278
   defining Unicode char U+2018 (decimal 8216)
   defining Unicode char U+2019 (decimal 8217)
   defining Unicode char U+201C (decimal 8220)
   defining Unicode char U+201D (decimal 8221)
   defining Unicode char U+2014 (decimal 8212)
   defining Unicode char U+2026 (decimal 8230)
 (c:/texlive/2025/texmf-dist/tex/latex/cjkpunct/CJKpunct.spa)) (c:/texlive/2025/texmf-dist/tex/latex/cjk/texinput/CJKspace.sty
Package: CJKspace 2021/10/16 3.8.0
) (c:/texlive/2025/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.bdg
File: UTF8.bdg 2021/10/16 4.8.5
) (c:/texlive/2025/texmf-dist/tex/latex/ctex/ctexspa.def
File: ctexspa.def 2022/07/14 v2.5.10 Space info for CJKpunct (CTEX)
)
\ccwd=\dimen143
\l__ctex_ccglue_skip=\skip51
)
\l__ctex_ziju_dim=\dimen144
 (c:/texlive/2025/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count279
\l__zhnum_tmp_int=\count280
 (c:/texlive/2025/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
)) (c:/texlive/2025/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese.def
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CTEX)
 (c:/texlive/2025/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (c:/texlive/2025/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
) (c:/texlive/2025/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTEX)
)) (c:/texlive/2025/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
) (c:/texlive/2025/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip52

For additional information on amsmath, use the `?' option.
(c:/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen145
)) (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen146
) (c:/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count281
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count282
\leftroot@=\count283
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count284
\DOTSCASE@=\count285
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box55
\strutbox@=\box56
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen147
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count286
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count287
\dotsspace@=\muskip17
\c@parentequation=\count288
\dspbrk@lvl=\count289
\tag@help=\toks20
\row@=\count290
\column@=\count291
\maxfields@=\count292
\andhelp@=\toks21
\eqnshift@=\dimen148
\alignsep@=\dimen149
\tagshift@=\dimen150
\tagwidth@=\dimen151
\totwidth@=\dimen152
\lineht@=\dimen153
\@envbody=\toks22
\multlinegap=\skip53
\multlinetaggap=\skip54
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (c:/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (c:/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (c:/texlive/2025/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (c:/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (c:/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks24
)
\c@ALC@unique=\count293
\c@ALC@line=\count294
\c@ALC@rem=\count295
\c@ALC@depth=\count296
\ALC@tlm=\skip55
\algorithmicindent=\skip56
) (c:/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (c:/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (c:/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen154
\Gin@req@width=\dimen155
) (c:/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
) (c:/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (c:/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen156
\lightrulewidth=\dimen157
\cmidrulewidth=\dimen158
\belowrulesep=\dimen159
\belowbottomsep=\dimen160
\aboverulesep=\dimen161
\abovetopsep=\dimen162
\cmidrulesep=\dimen163
\cmidrulekern=\dimen164
\defaultaddspace=\dimen165
\@cmidla=\count297
\@cmidlb=\count298
\@aboverulesep=\dimen166
\@belowrulesep=\dimen167
\@thisruleclass=\count299
\@lastruleclass=\count300
\@thisrulewidth=\dimen168
) (c:/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip57
\multirow@cntb=\count301
\multirow@dima=\skip58
\bigstrutjot=\dimen169
) (c:/texlive/2025/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (c:/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count302
\float@exts=\toks25
\float@box=\box57
\@float@everytoks=\toks26
\@floatcapt=\box58
)
\@float@every@algorithm=\toks27
\c@algorithm=\count303
) (c:/texlive/2025/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 
 (c:/texlive/2025/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count304
\c@ALG@rem=\count305
\c@ALG@nested=\count306
\ALG@tlm=\skip59
\ALG@thistlm=\skip60
\c@ALG@Lnr=\count307
\c@ALG@blocknr=\count308
\c@ALG@storecount=\count309
\c@ALG@tmpcounter=\count310
\ALG@tmplength=\skip61

c:/texlive/2025/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty:112: LaTeX Error: Command \algorithmic already defined.
               Or name \end... illegal, see p.192 of the manual.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.112    }
          %
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

)
Document Style - pseudocode environments for use with the `algorithmicx' style
) (c:/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (c:/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count311
\Gm@cntv=\count312
\c@Gm@tempcnt=\count313
\Gm@bindingoffset=\dimen170
\Gm@wd@mp=\dimen171
\Gm@odd@mp=\dimen172
\Gm@even@mp=\dimen173
\Gm@layoutwidth=\dimen174
\Gm@layoutheight=\dimen175
\Gm@layouthoffset=\dimen176
\Gm@layoutvoffset=\dimen177
\Gm@dimlist=\toks28
) (c:/texlive/2025/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.enc
File: UTF8.enc 2021/10/16 4.8.5
) (c:/texlive/2025/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.chr
File: UTF8.chr 2021/10/16 4.8.5
) (./高级模型论文_中文版.aux)
\openout1 = `高级模型论文_中文版.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C00/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C05/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C09/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C10/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C20/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C19/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C40/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C42/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C43/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C50/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C52/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C49/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C60/mj/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C61/mj/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C63/mj/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C64/mj/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C65/mj/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C70/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C31/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C32/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C33/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C34/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C35/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C36/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C37/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C80/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C81/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C01/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C11/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C21/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C41/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for C62/song/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
 (c:/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count314
\scratchdimen=\dimen178
\scratchbox=\box59
\nofMPsegments=\count315
\nofMParguments=\count316
\everyMPshowfont=\toks29
\MPscratchCnt=\count317
\MPscratchDim=\dimen179
\MPnumerator=\count318
\makeMPintoPDFobject=\count319
\everyMPtoPDFconversion=\toks30
) (c:/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (c:/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    Trying to load font information for C70+rm on input line 28.
(c:/texlive/2025/texmf-dist/tex/latex/ctex/fd/c70rm.fd
File: c70rm.fd 2022/07/14 v2.5.10 Chinese font definition (CTEX)
)
Package CJKpunct Info: use punctuation spaces for family 'rm' with punctstyle (quanjiao) on input line 28.
LaTeX Font Info:    Trying to load font information for U+msa on input line 28.
 (c:/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 28.
 (c:/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

LaTeX Warning: Citation `robotics_survey_2024' on page 1 undefined on input line 38.


LaTeX Warning: Citation `li2022positioning' on page 1 undefined on input line 40.


LaTeX Warning: Citation `qiao2019svr' on page 1 undefined on input line 40.


LaTeX Warning: Citation `raissi2019physics' on page 1 undefined on input line 42.


LaTeX Warning: Citation `vaswani2017attention' on page 1 undefined on input line 42.


LaTeX Warning: Citation `deb2002fast' on page 1 undefined on input line 42.



(c:/texlive/2025/texmf-dist/tex/generic/ctex/zhmap/ctex-zhmap-windows.tex
File: ctex-zhmap-windows.tex 2022/07/14 v2.5.10 Windows font map loader for pdfTeX and DVIPDFMx (CTEX)
{c:/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{UGBK.sfd}{Unicode.sfd}) [1

{c:/texlive/2025/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}]

LaTeX Warning: Citation `calibration_survey_2023' on page 2 undefined on input line 56.


LaTeX Warning: Citation `li2022positioning' on page 2 undefined on input line 58.


LaTeX Warning: Citation `qiao2019svr' on page 2 undefined on input line 58.


LaTeX Warning: Citation `raissi2019physics' on page 2 undefined on input line 62.


LaTeX Warning: Citation `bensch2024physics' on page 2 undefined on input line 70.


LaTeX Warning: Citation `pinn_robotics_2024' on page 2 undefined on input line 70.


LaTeX Warning: Citation `vaswani2017attention' on page 2 undefined on input line 74.


LaTeX Warning: Citation `transformer_pose_2024' on page 2 undefined on input line 74.


LaTeX Warning: Citation `transformer_planning_2024' on page 2 undefined on input line 74.


LaTeX Warning: Citation `nsga_robot_2024' on page 2 undefined on input line 78.


LaTeX Warning: Citation `deb2002fast' on page 2 undefined on input line 78.


LaTeX Warning: Citation `multi_obj_calib_2024' on page 2 undefined on input line 78.



[2]

[3]

[4]

LaTeX Warning: Citation `qiao2019svr' on page 5 undefined on input line 195.



[5]

LaTeX Warning: Citation `li2022positioning' on page 6 undefined on input line 209.


LaTeX Warning: Citation `qiao2019svr' on page 6 undefined on input line 211.


LaTeX Warning: Reference `tab:results' on page 6 undefined on input line 233.



[6]

[7]

[8]

[9] (./高级模型论文_中文版.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2022/07/14>
 ***********


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 9722 strings out of 473190
 164591 string characters out of 5719979
 558111 words of memory out of 5000000
 32321 multiletter control sequences out of 15000+600000
 660885 words of font info for 410 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 80i,10n,83p,904b,367s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simkai.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi12.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmib10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr6.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmti12.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/cm-super/sfbx1200.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/cm-super/sfrm1200.pfb>
Output written on 高级模型论文_中文版.pdf (9 pages, 778211 bytes).
PDF statistics:
 829 PDF objects out of 1000 (max. 8388607)
 548 compressed objects within 6 object streams
 0 named destinations out of 1000 (max. 500000)
 297 words of extra memory for PDF output out of 10000 (max. 10000000)

