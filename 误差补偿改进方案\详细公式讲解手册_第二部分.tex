\documentclass[12pt,a4paper]{article}
\usepackage[UTF8]{ctex}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{algorithm}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{bm}
\usepackage{hyperref}
\usepackage{xcolor}
\usepackage{framed}

\geometry{left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}

\title{\textbf{PINN机器人误差补偿详细公式讲解手册\\第二部分：NSGA-II算法与特征工程}}

\date{\today}

\begin{document}

\maketitle

\section{第九章：NSGA-II多目标优化算法详解}

\subsection{为什么选择NSGA-II？}
NSGA-II (Non-dominated Sorting Genetic Algorithm II) 是处理多目标优化问题的经典算法。

\textcolor{blue}{\textbf{核心思想}}：
\begin{itemize}
\item 不寻找单一最优解，而是找到一组平衡的解（Pareto前沿）
\item 使用进化算法的思想：选择、交叉、变异
\item 保持解的多样性，避免过早收敛
\end{itemize}

\subsection{非支配排序（Non-dominated Sorting）}

\subsubsection{什么是支配关系？}
对于两个解 $\bm{w}_i$ 和 $\bm{w}_j$，如果：
\begin{itemize}
\item 在所有目标上，$\bm{w}_i$ 都不比 $\bm{w}_j$ 差
\item 在至少一个目标上，$\bm{w}_i$ 比 $\bm{w}_j$ 好
\end{itemize}

则称 $\bm{w}_i$ 支配 $\bm{w}_j$，记作 $\bm{w}_i \prec \bm{w}_j$。

数学表达：
$$\bm{w}_i \prec \bm{w}_j \iff \forall k \in \{1,2,3\}: f_k(\bm{w}_i) \leq f_k(\bm{w}_j) \text{ 且 } \exists k: f_k(\bm{w}_i) < f_k(\bm{w}_j)$$

\subsubsection{前沿分层}
\begin{itemize}
\item \textbf{第一前沿 $\mathcal{F}_1$}：不被任何解支配的解
\item \textbf{第二前沿 $\mathcal{F}_2$}：只被第一前沿解支配的解
\item \textbf{第三前沿 $\mathcal{F}_3$}：只被前两个前沿解支配的解
\item 以此类推...
\end{itemize}

\subsection{拥挤距离（Crowding Distance）}

\subsubsection{为什么需要拥挤距离？}
在同一个前沿内，我们希望解分布得尽可能均匀，避免聚集在某个区域。

\subsubsection{拥挤距离计算}
对于第 $i$ 个解，其拥挤距离为：
$$d_i = \sum_{k=1}^{3} \frac{f_k(\bm{w}_{i+1}) - f_k(\bm{w}_{i-1})}{f_k^{max} - f_k^{min}}$$

\textcolor{blue}{\textbf{物理意义}}：
\begin{itemize}
\item $\bm{w}_{i-1}$ 和 $\bm{w}_{i+1}$：在第 $k$ 个目标上相邻的解
\item $f_k^{max} - f_k^{min}$：第 $k$ 个目标的取值范围（归一化）
\item $d_i$ 越大，说明解 $i$ 周围越"空旷"，多样性贡献越大
\end{itemize}

\textcolor{red}{\textbf{特殊情况}}：边界点的拥挤距离设为无穷大 $d_{boundary} = \infty$，确保它们被保留。

\subsection{选择机制}

\subsubsection{锦标赛选择}
从种群中随机选择几个个体，选择其中最好的作为父代。

比较规则：
\begin{enumerate}
\item 如果两个解在不同前沿，选择前沿等级更好的（数字更小的）
\item 如果两个解在同一前沿，选择拥挤距离更大的
\end{enumerate}

数学表达：
$$\text{select}(\bm{w}_i, \bm{w}_j) = \begin{cases}
\bm{w}_i & \text{if } rank_i < rank_j \\
\bm{w}_j & \text{if } rank_i > rank_j \\
\bm{w}_i & \text{if } rank_i = rank_j \text{ and } d_i > d_j \\
\bm{w}_j & \text{if } rank_i = rank_j \text{ and } d_i \leq d_j
\end{cases}$$

\subsection{交叉操作（Crossover）}

\subsubsection{模拟二进制交叉（SBX）}
对于神经网络权重这样的连续变量，使用SBX交叉：

$$\begin{aligned}
w_{c1}^{(i)} &= 0.5[(1 + \beta_q)w_{p1}^{(i)} + (1 - \beta_q)w_{p2}^{(i)}] \\
w_{c2}^{(i)} &= 0.5[(1 - \beta_q)w_{p1}^{(i)} + (1 + \beta_q)w_{p2}^{(i)}]
\end{aligned}$$

其中 $\beta_q$ 由随机数 $u \in [0,1]$ 和分布指数 $\eta_c$ 决定：
$$\beta_q = \begin{cases}
(2u)^{1/(\eta_c+1)} & \text{if } u \leq 0.5 \\
\left(\frac{1}{2(1-u)}\right)^{1/(\eta_c+1)} & \text{if } u > 0.5
\end{cases}$$

\textcolor{blue}{\textbf{参数含义}}：
\begin{itemize}
\item $w_{p1}, w_{p2}$：父代权重
\item $w_{c1}, w_{c2}$：子代权重
\item $\eta_c$：分布指数（通常取20），控制交叉的"激进程度"
\end{itemize}

\subsection{变异操作（Mutation）}

\subsubsection{多项式变异}
$$w_{new}^{(i)} = w_{old}^{(i)} + \delta_q \cdot (w_{upper}^{(i)} - w_{lower}^{(i)})$$

其中：
$$\delta_q = \begin{cases}
(2r)^{1/(\eta_m+1)} - 1 & \text{if } r < 0.5 \\
1 - [2(1-r)]^{1/(\eta_m+1)} & \text{if } r \geq 0.5
\end{cases}$$

\textcolor{blue}{\textbf{参数含义}}：
\begin{itemize}
\item $r \in [0,1]$：随机数
\item $\eta_m$：变异分布指数（通常取20）
\item $w_{upper}, w_{lower}$：权重的上下界
\end{itemize}

\section{第十章：物理驱动特征工程详解}

\subsection{为什么需要特征工程？}
原始输入只有6个关节角度，但机器人的误差是高度非线性的。我们需要构造能够捕捉这种非线性关系的特征。

\textcolor{red}{\textbf{传统方法}}：随意添加多项式特征，缺乏物理意义。
\textcolor{blue}{\textbf{我们的方法}}：基于机器人学理论，系统性构造物理有意义的特征。

\subsection{140维特征的系统构造}

\subsubsection{1. 运动学特征（42维）}

\textbf{基础角度特征（6维）}：
$$\bm{F}_{basic} = [\theta_1, \theta_2, \theta_3, \theta_4, \theta_5, \theta_6]$$

\textbf{三角函数特征（12维）}：
$$\bm{F}_{trig} = [\sin(\theta_1), \cos(\theta_1), \sin(\theta_2), \cos(\theta_2), \ldots, \sin(\theta_6), \cos(\theta_6)]$$

\textcolor{blue}{\textbf{物理意义}}：DH变换矩阵中直接包含这些项！

\textbf{复合角度特征（24维）}：
$$\bm{F}_{compound} = [\sin(2\theta_i), \cos(2\theta_i), \sin(\theta_i/2), \cos(\theta_i/2)]_{i=1}^6$$

\textcolor{blue}{\textbf{物理意义}}：
\begin{itemize}
\item $\sin(2\theta_i), \cos(2\theta_i)$：双角公式，出现在复合旋转中
\item $\sin(\theta_i/2), \cos(\theta_i/2)$：半角公式，与四元数表示相关
\end{itemize}

\subsubsection{2. 动力学特征（36维）}

\textbf{惯性耦合特征（15维）}：
$$\bm{F}_{inertial} = [\cos(\theta_i - \theta_j)]_{i<j}$$

这里 $i<j$ 表示所有的关节对组合：
$$(1,2), (1,3), (1,4), (1,5), (1,6), (2,3), (2,4), (2,5), (2,6), (3,4), (3,5), (3,6), (4,5), (4,6), (5,6)$$
共15对。

\textcolor{blue}{\textbf{物理意义}}：来自惯性矩阵 $\bm{M}(\bm{\theta})$ 中的耦合项：
$$M_{ij}(\bm{\theta}) = \sum_{k=\max(i,j)}^{6} \text{tr}\left(\frac{\partial \bm{T}_k}{\partial \theta_i} \bm{J}_k \frac{\partial \bm{T}_k^T}{\partial \theta_j}\right) m_k$$

\textbf{科里奥利特征（15维）}：
$$\bm{F}_{coriolis} = [\sin(\theta_i - \theta_j)]_{i<j}$$

\textcolor{blue}{\textbf{物理意义}}：来自科里奥利矩阵 $\bm{C}(\bm{\theta}, \dot{\bm{\theta}})$ 中的耦合项。

\textbf{重力特征（6维）}：
$$\bm{F}_{gravity} = [\sin(\sum_{k=1}^i \theta_k)]_{i=1}^6$$

\textcolor{blue}{\textbf{物理意义}}：重力项 $\bm{G}(\bm{\theta})$ 与累积角度相关：
$$G_i(\bm{\theta}) = -\sum_{k=i}^{6} m_k \bm{g}^T \frac{\partial \bm{r}_{ck}}{\partial \theta_i}$$

\subsubsection{3. 耦合特征（30维）}

\textbf{雅可比特征（18维）}：
$$\bm{F}_{jacobian} = [\frac{\partial x}{\partial \theta_i}, \frac{\partial y}{\partial \theta_i}, \frac{\partial z}{\partial \theta_i}]_{i=1}^6$$

\textcolor{blue}{\textbf{物理意义}}：直接来自雅可比矩阵的位置部分。

\textbf{操作性特征（12维）}：
$$\bm{F}_{manipulability} = [\sqrt{\det(\bm{J}_i \bm{J}_i^T)}, \kappa(\bm{J}_i)]_{i=1}^6$$

其中：
\begin{itemize}
\item $\sqrt{\det(\bm{J}_i \bm{J}_i^T)}$：操作性椭球体积
\item $\kappa(\bm{J}_i) = \frac{\sigma_{max}}{\sigma_{min}}$：条件数（$\sigma$ 是奇异值）
\end{itemize}

\subsubsection{4. 奇异性特征（15维）}

\textbf{边界奇异性（6维）}：
$$\bm{F}_{boundary} = [|\sin(\theta_i)|, |\cos(\theta_i)|]_{i \in \{1,2,3\}}$$

\textbf{内部奇异性（4维）}：
$$\bm{F}_{internal} = [|\sin(\theta_2 + \theta_3)|, |\cos(\theta_2 + \theta_3)|, |\sin(\theta_2 - \theta_3)|, |\cos(\theta_2 - \theta_3)|]$$

\textbf{腕部奇异性（5维）}：
$$\bm{F}_{wrist} = [|\sin(\theta_4)|, |\sin(\theta_5)|, |\sin(\theta_6)|, |\det(\bm{R}_{wrist})|, \text{tr}(\bm{R}_{wrist})]$$

\subsubsection{5. 工作空间特征（17维）}

\textbf{可达性特征（4维）}：
$$\bm{F}_{reach} = [r_{max}, r_{min}, h_{max}, h_{min}]$$

其中：
\begin{itemize}
\item $r_{max}, r_{min}$：最大、最小到达半径
\item $h_{max}, h_{min}$：最大、最小到达高度
\end{itemize}

\textbf{姿态特征（4维）}：
$$\bm{F}_{orientation} = [\alpha, \beta, \gamma, |\alpha|+|\beta|+|\gamma|]$$

\textbf{灵巧性特征（9维）}：
包括椭球体积、表面积、奇异值比值等复杂几何特征。

\subsection{特征降维：从140维到63维}

\subsubsection{主成分分析（PCA）}
计算特征协方差矩阵的特征值：
$$\bm{C} = \frac{1}{N-1} \sum_{i=1}^{N} (\bm{F}_i - \bar{\bm{F}})(\bm{F}_i - \bar{\bm{F}})^T$$

选择前 $k$ 个主成分，使得累积方差贡献率达到95%：
$$\frac{\sum_{i=1}^k \lambda_i}{\sum_{i=1}^{140} \lambda_i} \geq 0.95$$

\subsubsection{互信息筛选}
对于每个特征 $F_i$，计算其与误差 $\bm{\epsilon}$ 的互信息：
$$I(F_i; \bm{\epsilon}) = \sum_{f,e} p(f,e) \log \frac{p(f,e)}{p(f)p(e)}$$

同时考虑特征间的冗余性：
$$\text{Score}(F_i) = I(F_i; \bm{\epsilon}) - \alpha \sum_{j \neq i} I(F_i; F_j)$$

选择得分最高的63个特征。

\section{第十一章：自适应权重调整机制}

\subsection{为什么需要自适应权重？}
PINN的损失函数包含多个项：
$$\mathcal{L}_{PINN} = \mathcal{L}_{data} + \lambda_{physics} \mathcal{L}_{physics} + \lambda_{boundary} \mathcal{L}_{boundary}$$

问题：不同损失项的量级可能差异很大，固定权重可能导致某些项被忽略。

\subsection{自适应调整策略}
$$\lambda_k^{(t+1)} = \lambda_k^{(t)} \cdot \exp\left(\beta \cdot \frac{\mathcal{L}_k^{(t)} - \mathcal{L}_{target,k}}{\mathcal{L}_{target,k}}\right)$$

\textcolor{blue}{\textbf{调整逻辑}}：
\begin{itemize}
\item 如果 $\mathcal{L}_k^{(t)} > \mathcal{L}_{target,k}$（损失太大），增加权重 $\lambda_k$
\item 如果 $\mathcal{L}_k^{(t)} < \mathcal{L}_{target,k}$（损失太小），减少权重 $\lambda_k$
\item $\beta$ 控制调整的激进程度
\end{itemize}

\subsection{目标损失的设定}
$$\mathcal{L}_{target,k} = \alpha_k \cdot \mathcal{L}_{k,initial}$$

其中 $\alpha_k$ 是期望的改进比例（比如0.1表示希望损失降到初始值的10%）。

\section{第十二章：创新点的数学本质}

\subsection{创新点1：多目标优化的数学优势}

\textbf{传统单目标}：
$$\min_{\bm{w}} \mathcal{L}_{total} = w_1 f_1(\bm{w}) + w_2 f_2(\bm{w}) + w_3 f_3(\bm{w})$$

问题：权重 $w_1, w_2, w_3$ 的选择是主观的，可能错过更好的解。

\textbf{我们的多目标}：
$$\min_{\bm{w}} [f_1(\bm{w}), f_2(\bm{w}), f_3(\bm{w})]$$

优势：找到整个Pareto前沿，提供多种平衡选择。

\subsection{创新点2：物理约束的数学作用}

\textbf{正则化效应}：物理约束相当于在损失函数中加入了结构化的正则化项，有助于：
\begin{itemize}
\item 减少过拟合
\item 提高泛化能力
\item 确保解的物理合理性
\end{itemize}

\textbf{损失地形平滑化}：物理约束使得损失函数更加平滑，减少局部最优陷阱。

\subsection{创新点3：确定性初始化的数学原理}

\textbf{最小二乘解}：
$$\bm{w}_{init} = (\bm{J}^T\bm{J} + \alpha\bm{I})^{-1}\bm{J}^T\bm{\epsilon}_{training}$$

这是线性回归问题 $\bm{J}\bm{w} = \bm{\epsilon}$ 的解析解。

\textbf{几何意义}：在权重空间中，这个解是距离原点最近的、能够拟合训练数据的点。

\textbf{优势}：
\begin{itemize}
\item 确定性：每次都得到相同的初始点
\item 物理合理性：基于线性化的物理模型
\item 收敛性：从一个"不错"的起点开始优化
\end{itemize}

\section{第十三章：实验结果的数学分析}

\subsection{性能提升的量化}
\begin{align}
\text{位置误差改进率} &= \frac{0.708 - 0.059}{0.708} \times 100\% = 91.7\% \\
\text{角度误差改进率} &= \frac{0.179 - 0.049}{0.179} \times 100\% = 72.5\%
\end{align}

\subsection{统计显著性检验}
使用t检验验证改进的显著性：
$$t = \frac{\bar{x}_{traditional} - \bar{x}_{ours}}{\sqrt{\frac{s_1^2}{n_1} + \frac{s_2^2}{n_2}}}$$

其中：
\begin{itemize}
\item $\bar{x}$：平均误差
\item $s^2$：方差
\item $n$：样本数量
\end{itemize}

结果：$p < 0.001$，改进高度显著。

\subsection{R²分数的含义}
$$R^2 = 1 - \frac{\sum_{i=1}^{N} (y_i - \hat{y}_i)^2}{\sum_{i=1}^{N} (y_i - \bar{y})^2}$$

\begin{itemize}
\item $R^2 = 0.8174$：模型解释了81.74%的误差变异
\item $R^2 = 1$：完美预测
\item $R^2 = 0$：预测效果等同于使用平均值
\end{itemize}

\section{总结：数学创新的本质}

我们的三大创新点在数学上的本质：

\begin{framed}
\textcolor{red}{\textbf{创新本质}}
\begin{enumerate}
\item \textbf{多目标优化}：从标量优化扩展到向量优化，避免了权重选择的主观性
\item \textbf{物理约束}：将领域知识编码为数学约束，提高了学习的效率和可靠性
\item \textbf{确定性初始化}：用解析解替代随机初始化，提高了优化的稳定性
\end{enumerate}
\end{framed}

这些创新不仅在机器人误差补偿领域有效，其数学思想可以推广到其他工程优化问题中。

\end{document}
