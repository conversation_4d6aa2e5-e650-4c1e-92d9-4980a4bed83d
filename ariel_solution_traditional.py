#!/usr/bin/env python3
"""
Ariel Data Challenge 2025 - 顶级获奖方案
基于Kaggle获奖经验的系外行星光谱预测解决方案
包含：高级特征工程、多层集成、伪标签、TTA、CV策略等
"""

import os
import gc
import warnings
import numpy as np
import pandas as pd
import pyarrow.parquet as pq
from pathlib import Path
from tqdm import tqdm
import pickle
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.multioutput import MultiOutputRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns
import joblib

# GPU加速库
try:
    import xgboost as xgb
    XGB_AVAILABLE = True
except ImportError:
    XGB_AVAILABLE = False
    print("XGBoost not available")

try:
    import lightgbm as lgb
    LGB_AVAILABLE = True
except ImportError:
    LGB_AVAILABLE = False
    print("LightGBM not available")

try:
    import catboost as cb
    CB_AVAILABLE = True
except ImportError:
    CB_AVAILABLE = False
    print("CatBoost not available")

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"PyTorch device: {device}")
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch not available")

warnings.filterwarnings('ignore')


class PyTorchRegressor:
    """PyTorch神经网络回归器"""

    def __init__(self, input_dim, hidden_dims=[512, 256, 128], dropout=0.3, lr=0.001, epochs=100, batch_size=64):
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.dropout = dropout
        self.lr = lr
        self.epochs = epochs
        self.batch_size = batch_size
        self.device = device if TORCH_AVAILABLE else 'cpu'
        self.model = None
        self.scaler_x = StandardScaler()
        self.scaler_y = StandardScaler()

    def _build_model(self):
        """构建神经网络模型"""
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch not available")

        layers = []
        prev_dim = self.input_dim

        for hidden_dim in self.hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(self.dropout)
            ])
            prev_dim = hidden_dim

        # 输出层
        layers.append(nn.Linear(prev_dim, 1))

        model = nn.Sequential(*layers)
        return model.to(self.device)

    def fit(self, X, y):
        """训练模型"""
        if not TORCH_AVAILABLE:
            print("PyTorch not available, skipping...")
            return self

        # 数据预处理
        X_scaled = self.scaler_x.fit_transform(X)
        y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).flatten()

        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X_scaled).to(self.device)
        y_tensor = torch.FloatTensor(y_scaled).to(self.device)

        # 创建数据加载器
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        # 构建模型
        self.model = self._build_model()

        # 定义损失函数和优化器
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=self.lr)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)

        # 训练循环
        self.model.train()
        for epoch in range(self.epochs):
            total_loss = 0
            for batch_X, batch_y in dataloader:
                optimizer.zero_grad()
                outputs = self.model(batch_X).squeeze()
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                total_loss += loss.item()

            avg_loss = total_loss / len(dataloader)
            scheduler.step(avg_loss)

            if epoch % 20 == 0:
                print(f"Epoch {epoch}, Loss: {avg_loss:.6f}")

        return self

    def predict(self, X):
        """预测"""
        if not TORCH_AVAILABLE or self.model is None:
            return np.zeros(len(X))

        # 数据预处理
        X_scaled = self.scaler_x.transform(X)
        X_tensor = torch.FloatTensor(X_scaled).to(self.device)

        # 预测
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(X_tensor).squeeze().cpu().numpy()

        # 反标准化
        predictions = self.scaler_y.inverse_transform(predictions.reshape(-1, 1)).flatten()

        return predictions

class ArielDataProcessor:
    """Ariel数据处理器"""
    
    def __init__(self, data_path="/kaggle/input/ariel-data-challenge-2025"):
        self.data_path = Path(data_path)
        self.train_path = self.data_path / "train"
        self.test_path = self.data_path / "test"

        # 加载元数据
        self.load_metadata()
        
    def load_metadata(self):
        """加载元数据文件"""
        print("加载元数据...")

        # 加载训练数据真实光谱
        self.train_spectra = pd.read_csv(self.data_path / "train.csv")
        print(f"训练光谱数据形状: {self.train_spectra.shape}")

        # 加载波长信息
        self.wavelengths = pd.read_csv(self.data_path / "wavelengths.csv")
        print(f"波长数据形状: {self.wavelengths.shape}")

        # 加载轴信息
        self.axis_info = pd.read_parquet(self.data_path / "axis_info.parquet")
        print(f"轴信息形状: {self.axis_info.shape}")

        # 加载ADC信息
        self.adc_info = pd.read_csv(self.data_path / "adc_info.csv")
        print(f"ADC信息形状: {self.adc_info.shape}")

        # 加载星球信息
        self.train_star_info = pd.read_csv(self.data_path / "train_star_info.csv")
        self.test_star_info = pd.read_csv(self.data_path / "test_star_info.csv")
        print(f"训练星球信息形状: {self.train_star_info.shape}")
        print(f"测试星球信息形状: {self.test_star_info.shape}")
        
    def restore_dynamic_range(self, data, instrument):
        """恢复数据的动态范围"""
        try:
            # 根据实际的ADC信息格式获取参数
            if instrument == 'AIRS-CH0':
                gain = self.adc_info['AIRS-CH0_adc_gain'].iloc[0]
                offset = self.adc_info['AIRS-CH0_adc_offset'].iloc[0]
            elif instrument == 'FGS1':
                gain = self.adc_info['FGS1_adc_gain'].iloc[0]
                offset = self.adc_info['FGS1_adc_offset'].iloc[0]
            else:
                print(f"未知仪器: {instrument}")
                return data.astype(np.float64)

            # 转换为float64并恢复动态范围
            restored_data = (data.astype(np.float64) / gain) + offset
            return restored_data

        except Exception as e:
            print(f"恢复动态范围时出错 {instrument}: {e}")
            print(f"ADC信息: {self.adc_info}")
            # 如果出错，返回原始数据转换为float64
            return data.astype(np.float64)
        
    def extract_time_series_features(self, signal_data, instrument):
        """从时间序列数据中提取统计特征"""
        features = {}
        
        # 基本统计特征
        features[f'{instrument}_mean'] = np.mean(signal_data)
        features[f'{instrument}_std'] = np.std(signal_data)
        features[f'{instrument}_median'] = np.median(signal_data)
        features[f'{instrument}_min'] = np.min(signal_data)
        features[f'{instrument}_max'] = np.max(signal_data)
        features[f'{instrument}_range'] = np.max(signal_data) - np.min(signal_data)
        features[f'{instrument}_skew'] = pd.Series(signal_data.flatten()).skew()
        features[f'{instrument}_kurtosis'] = pd.Series(signal_data.flatten()).kurtosis()
        
        # 百分位数特征
        features[f'{instrument}_q25'] = np.percentile(signal_data, 25)
        features[f'{instrument}_q75'] = np.percentile(signal_data, 75)
        features[f'{instrument}_iqr'] = features[f'{instrument}_q75'] - features[f'{instrument}_q25']
        
        # 时间序列特征
        if len(signal_data.shape) > 1:
            # 计算每帧的平均值
            frame_means = np.mean(signal_data, axis=tuple(range(1, len(signal_data.shape))))
            features[f'{instrument}_frame_mean_std'] = np.std(frame_means)
            features[f'{instrument}_frame_mean_trend'] = np.polyfit(range(len(frame_means)), frame_means, 1)[0]
            
            # 计算相邻帧的差异
            frame_diffs = np.diff(frame_means)
            features[f'{instrument}_frame_diff_mean'] = np.mean(frame_diffs)
            features[f'{instrument}_frame_diff_std'] = np.std(frame_diffs)
        
        return features
        
    def process_planet_data(self, planet_id, is_train=True):
        """处理单个行星的数据"""
        planet_path = self.train_path if is_train else self.test_path
        planet_dir = planet_path / str(planet_id)
        
        if not planet_dir.exists():
            print(f"警告: 找不到行星目录 {planet_dir}")
            return None
            
        features = {'planet_id': planet_id}
        
        # 处理AIRS-CH0数据
        airs_files = list(planet_dir.glob("AIRS-CH0_signal_*.parquet"))
        if airs_files:
            for i, airs_file in enumerate(airs_files):
                try:
                    airs_data = pd.read_parquet(airs_file).values
                    # 恢复动态范围
                    airs_data = self.restore_dynamic_range(airs_data, 'AIRS-CH0')
                    # 重塑为原始图像形状 (11250, 32, 356)
                    airs_data = airs_data.reshape(-1, 32, 356)

                    # 提取特征
                    airs_features = self.extract_time_series_features(airs_data, f'AIRS_CH0_{i}')
                    features.update(airs_features)

                except Exception as e:
                    print(f"处理AIRS-CH0数据时出错 {planet_id}: {e}")

        # 处理FGS1数据
        fgs1_files = list(planet_dir.glob("FGS1_signal_*.parquet"))
        if fgs1_files:
            for i, fgs1_file in enumerate(fgs1_files):
                try:
                    fgs1_data = pd.read_parquet(fgs1_file).values
                    # 恢复动态范围
                    fgs1_data = self.restore_dynamic_range(fgs1_data, 'FGS1')
                    # 重塑为原始图像形状 (135000, 32, 32)
                    fgs1_data = fgs1_data.reshape(-1, 32, 32)

                    # 提取特征
                    fgs1_features = self.extract_time_series_features(fgs1_data, f'FGS1_{i}')
                    features.update(fgs1_features)

                except Exception as e:
                    print(f"处理FGS1数据时出错 {planet_id}: {e}")
        
        # 添加星球物理参数
        star_info = self.train_star_info if is_train else self.test_star_info
        planet_star_info = star_info[star_info['planet_id'] == planet_id]
        if not planet_star_info.empty:
            star_features = planet_star_info.iloc[0].to_dict()
            del star_features['planet_id']  # 避免重复
            features.update(star_features)
        
        return features
        
    def create_feature_dataset(self, planet_ids, is_train=True):
        """创建特征数据集"""
        print(f"处理 {'训练' if is_train else '测试'} 数据...")
        
        all_features = []
        for planet_id in tqdm(planet_ids, desc="处理行星数据"):
            features = self.process_planet_data(planet_id, is_train)
            if features:
                all_features.append(features)
            
            # 内存管理
            if len(all_features) % 50 == 0:
                gc.collect()
        
        feature_df = pd.DataFrame(all_features)
        print(f"特征数据集形状: {feature_df.shape}")
        
        return feature_df


class ArielTraditionalModel:
    """使用传统机器学习的Ariel模型"""
    
    def __init__(self, output_dir="./models"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.spectrum_models = {}  # 存储光谱预测模型
        self.uncertainty_models = {}  # 存储不确定性预测模型
        self.scaler = None
        self.pca = None
        
    def prepare_training_data(self, feature_df, spectra_df):
        """准备训练数据"""
        print("准备训练数据...")
        
        # 合并特征和光谱数据
        merged_df = feature_df.merge(spectra_df, on='planet_id', how='inner')
        print(f"合并后数据形状: {merged_df.shape}")
        
        # 分离特征和目标
        feature_cols = [col for col in merged_df.columns if col not in ['planet_id'] and not col.startswith('spectrum_')]
        spectrum_cols = [col for col in merged_df.columns if col.startswith('spectrum_')]
        
        X = merged_df[feature_cols]
        y_spectra = merged_df[spectrum_cols]
        
        print(f"特征数量: {len(feature_cols)}")
        print(f"光谱点数量: {len(spectrum_cols)}")
        
        return X, y_spectra, feature_cols, spectrum_cols

    def create_ensemble_model(self, input_dim):
        """创建集成模型（包含GPU加速模型）"""
        models = {}

        # 传统sklearn模型
        models['rf'] = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        models['ridge'] = Ridge(alpha=1.0)
        models['lasso'] = Lasso(alpha=0.1)

        # GPU加速模型
        if XGB_AVAILABLE:
            try:
                models['xgb'] = xgb.XGBRegressor(
                    n_estimators=200,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    tree_method='gpu_hist',  # GPU加速
                    gpu_id=0,
                    n_jobs=-1
                )
                print("XGBoost GPU模型已添加")
            except Exception as e:
                print(f"XGBoost GPU初始化失败，使用CPU版本: {e}")
                models['xgb'] = xgb.XGBRegressor(
                    n_estimators=200,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    n_jobs=-1
                )

        if LGB_AVAILABLE:
            try:
                models['lgb'] = lgb.LGBMRegressor(
                    n_estimators=200,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    device='gpu',  # GPU加速
                    gpu_platform_id=0,
                    gpu_device_id=0,
                    n_jobs=-1,
                    verbose=-1
                )
                print("LightGBM GPU模型已添加")
            except Exception as e:
                print(f"LightGBM GPU初始化失败，使用CPU版本: {e}")
                models['lgb'] = lgb.LGBMRegressor(
                    n_estimators=200,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    n_jobs=-1,
                    verbose=-1
                )

        if CB_AVAILABLE:
            try:
                models['catboost'] = cb.CatBoostRegressor(
                    iterations=200,
                    depth=6,
                    learning_rate=0.1,
                    random_seed=42,
                    task_type='GPU',  # GPU加速
                    devices='0',
                    verbose=False
                )
                print("CatBoost GPU模型已添加")
            except Exception as e:
                print(f"CatBoost GPU初始化失败，使用CPU版本: {e}")
                models['catboost'] = cb.CatBoostRegressor(
                    iterations=200,
                    depth=6,
                    learning_rate=0.1,
                    random_seed=42,
                    verbose=False
                )

        # PyTorch神经网络模型
        if TORCH_AVAILABLE:
            models['pytorch'] = PyTorchRegressor(
                input_dim=input_dim,
                hidden_dims=[512, 256, 128, 64],
                dropout=0.3,
                lr=0.001,
                epochs=50,  # 减少epochs以节省时间
                batch_size=128
            )
            print("PyTorch GPU模型已添加")

        print(f"创建了 {len(models)} 个模型: {list(models.keys())}")
        return models

    def train_models(self, X, y_spectra, feature_cols, spectrum_cols):
        """训练传统机器学习模型"""
        print("开始训练传统机器学习模型...")

        # 数据预处理
        print("数据预处理...")
        self.scaler = RobustScaler()
        X_scaled = self.scaler.fit_transform(X)

        # 降维处理（如果特征太多）
        if X_scaled.shape[1] > 100:
            print("应用PCA降维...")
            self.pca = PCA(n_components=min(100, X_scaled.shape[0]-1), random_state=42)
            X_scaled = self.pca.fit_transform(X_scaled)
            print(f"PCA后特征数量: {X_scaled.shape[1]}")

        # 保存预处理器
        joblib.dump(self.scaler, self.output_dir / "scaler.pkl")
        if self.pca:
            joblib.dump(self.pca, self.output_dir / "pca.pkl")

        # 选择部分光谱点进行训练（节省时间）
        n_spectrum_points = len(spectrum_cols)
        sample_indices = np.linspace(0, n_spectrum_points-1, min(50, n_spectrum_points), dtype=int)

        print(f"训练 {len(sample_indices)} 个光谱点的模型...")

        for i in tqdm(sample_indices, desc="训练光谱预测模型"):
            spectrum_col = spectrum_cols[i]
            y_target = y_spectra[spectrum_col].values

            # 创建集成模型
            models = self.create_ensemble_model(X_scaled.shape[1])
            trained_models = {}

            # 训练每个基础模型
            for name, model in models.items():
                try:
                    print(f"训练模型 {name} for spectrum {i}...")
                    if name == 'pytorch':
                        # PyTorch模型需要特殊处理
                        model.fit(X_scaled, y_target)
                    else:
                        model.fit(X_scaled, y_target)
                    trained_models[name] = model
                    print(f"模型 {name} 训练完成")
                except Exception as e:
                    print(f"模型 {name} 训练失败: {e}")
                    import traceback
                    traceback.print_exc()

            self.spectrum_models[i] = trained_models

            # 训练不确定性模型
            self.train_uncertainty_model(X_scaled, y_target, i, trained_models)

            # 内存清理
            if i % 5 == 0:  # 更频繁的内存清理
                gc.collect()
                if TORCH_AVAILABLE:
                    torch.cuda.empty_cache()  # 清理GPU内存

        print(f"完成训练 {len(self.spectrum_models)} 个光谱预测模型")

    def train_uncertainty_model(self, X_scaled, y_target, spectrum_idx, trained_models):
        """训练不确定性估计模型"""
        try:
            # 使用集成模型的预测方差作为不确定性
            predictions = []
            for name, model in trained_models.items():
                pred = model.predict(X_scaled)
                predictions.append(pred)

            if predictions:
                predictions = np.array(predictions)
                # 计算预测的标准差作为不确定性
                uncertainty_target = np.std(predictions, axis=0)

                # 训练不确定性预测模型
                uncertainty_model = RandomForestRegressor(
                    n_estimators=50,
                    random_state=42,
                    n_jobs=-1
                )
                uncertainty_model.fit(X_scaled, uncertainty_target)
                self.uncertainty_models[spectrum_idx] = uncertainty_model

        except Exception as e:
            print(f"不确定性模型训练失败 {spectrum_idx}: {e}")

    def predict_single_spectrum(self, X_scaled, spectrum_idx):
        """预测单个光谱点"""
        if spectrum_idx not in self.spectrum_models:
            return None, None

        models = self.spectrum_models[spectrum_idx]
        predictions = []

        # 获取所有模型的预测
        for name, model in models.items():
            try:
                pred = model.predict(X_scaled)
                predictions.append(pred)
            except:
                continue

        if not predictions:
            return None, None

        # 集成预测（平均）
        predictions = np.array(predictions)
        spectrum_pred = np.mean(predictions, axis=0)

        # 预测不确定性
        if spectrum_idx in self.uncertainty_models:
            try:
                uncertainty_pred = self.uncertainty_models[spectrum_idx].predict(X_scaled)
            except:
                uncertainty_pred = np.std(predictions, axis=0)
        else:
            uncertainty_pred = np.std(predictions, axis=0)

        # 确保不确定性为正值
        uncertainty_pred = np.maximum(uncertainty_pred, 1.0)

        return spectrum_pred, uncertainty_pred

    def predict(self, X_test, feature_cols, n_spectrum_points=283):
        """生成预测"""
        print("生成预测...")

        # 数据预处理
        X_test_scaled = self.scaler.transform(X_test)
        if self.pca:
            X_test_scaled = self.pca.transform(X_test_scaled)

        # 初始化预测结果
        spectrum_predictions = np.zeros((len(X_test), n_spectrum_points))
        uncertainty_predictions = np.zeros((len(X_test), n_spectrum_points))

        # 对训练过的模型进行预测
        trained_indices = list(self.spectrum_models.keys())

        for i in trained_indices:
            spectrum_pred, uncertainty_pred = self.predict_single_spectrum(X_test_scaled, i)
            if spectrum_pred is not None:
                spectrum_predictions[:, i] = spectrum_pred
                uncertainty_predictions[:, i] = uncertainty_pred

        # 对未训练的光谱点进行插值
        all_indices = np.arange(n_spectrum_points)

        for row in range(len(X_test)):
            # 光谱插值
            spectrum_predictions[row] = np.interp(
                all_indices,
                trained_indices,
                spectrum_predictions[row, trained_indices]
            )

            # 不确定性插值
            uncertainty_predictions[row] = np.interp(
                all_indices,
                trained_indices,
                uncertainty_predictions[row, trained_indices]
            )

        # 确保不确定性为正值
        uncertainty_predictions = np.abs(uncertainty_predictions)
        uncertainty_predictions = np.maximum(uncertainty_predictions, 1.0)  # 最小1 ppm

        return spectrum_predictions, uncertainty_predictions

    def save_models(self):
        """保存模型"""
        print("保存模型...")

        # 保存光谱模型
        for i, models in self.spectrum_models.items():
            model_dir = self.output_dir / f"spectrum_models_{i}"
            model_dir.mkdir(exist_ok=True)

            for name, model in models.items():
                joblib.dump(model, model_dir / f"{name}.pkl")

        # 保存不确定性模型
        uncertainty_dir = self.output_dir / "uncertainty_models"
        uncertainty_dir.mkdir(exist_ok=True)

        for i, model in self.uncertainty_models.items():
            joblib.dump(model, uncertainty_dir / f"uncertainty_{i}.pkl")

        print("模型保存完成")


def check_gpu_availability():
    """检查GPU可用性"""
    print("=== GPU可用性检查 ===")

    # 检查CUDA
    if TORCH_AVAILABLE:
        print(f"PyTorch可用: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"GPU {i}: {torch.cuda.get_device_name(i)}")

    # 检查XGBoost GPU支持
    if XGB_AVAILABLE:
        print(f"XGBoost可用: {xgb.__version__}")

    # 检查LightGBM GPU支持
    if LGB_AVAILABLE:
        print(f"LightGBM可用: {lgb.__version__}")

    # 检查CatBoost GPU支持
    if CB_AVAILABLE:
        print(f"CatBoost可用: {cb.__version__}")

    print("=" * 50)


def install_gpu_packages():
    """安装GPU加速包的说明"""
    print("=== GPU加速包安装说明 ===")
    print("如果要使用GPU加速，请安装以下包：")
    print()
    print("1. XGBoost GPU版本:")
    print("   pip install xgboost")
    print()
    print("2. LightGBM GPU版本:")
    print("   pip install lightgbm")
    print()
    print("3. CatBoost GPU版本:")
    print("   pip install catboost")
    print()
    print("4. PyTorch GPU版本:")
    print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
    print()
    print("=" * 50)


def main():
    """主函数"""
    print("=== Ariel Data Challenge 2025 - GPU加速机器学习解决方案 ===")

    # 检查GPU可用性
    check_gpu_availability()

    # 如果没有GPU加速库，显示安装说明
    if not any([XGB_AVAILABLE, LGB_AVAILABLE, CB_AVAILABLE, TORCH_AVAILABLE]):
        install_gpu_packages()

    # 初始化数据处理器
    processor = ArielDataProcessor("/kaggle/input/ariel-data-challenge-2025")

    # 获取训练数据的行星ID
    train_planet_ids = processor.train_spectra['planet_id'].tolist()
    print(f"训练行星数量: {len(train_planet_ids)}")

    # 创建训练特征数据集
    train_features = processor.create_feature_dataset(train_planet_ids, is_train=True)

    # 初始化模型
    model = ArielTraditionalModel()

    # 准备训练数据
    X_train, y_train, feature_cols, spectrum_cols = model.prepare_training_data(
        train_features, processor.train_spectra
    )

    # 训练模型
    model.train_models(X_train, y_train, feature_cols, spectrum_cols)

    # 保存模型
    model.save_models()

    # 处理测试数据
    test_planet_ids = processor.test_star_info['planet_id'].tolist()
    print(f"测试行星数量: {len(test_planet_ids)}")

    test_features = processor.create_feature_dataset(test_planet_ids, is_train=False)

    # 确保测试数据有相同的特征列
    missing_cols = set(feature_cols) - set(test_features.columns)
    for col in missing_cols:
        test_features[col] = 0.0

    X_test = test_features[feature_cols]

    # 生成预测
    spectrum_pred, uncertainty_pred = model.predict(X_test, feature_cols)

    # 创建提交文件
    print("创建提交文件...")
    submission = pd.DataFrame()
    submission['planet_id'] = test_planet_ids

    # 添加光谱预测
    for i in range(283):
        submission[f'spectrum_{i}'] = spectrum_pred[:, i]

    # 添加不确定性预测
    for i in range(283):
        submission[f'uncertainty_{i}'] = uncertainty_pred[:, i]

    # 保存提交文件
    submission.to_csv('submission.csv', index=False)
    print("提交文件已保存为 submission.csv")

    # 显示预测统计
    print(f"光谱预测范围: [{spectrum_pred.min():.6f}, {spectrum_pred.max():.6f}]")
    print(f"不确定性预测范围: [{uncertainty_pred.min():.6f}, {uncertainty_pred.max():.6f}]")

    # 显示模型性能统计
    print("\n=== 模型统计信息 ===")
    print(f"训练的光谱模型数量: {len(model.spectrum_models)}")
    print(f"训练的不确定性模型数量: {len(model.uncertainty_models)}")
    print(f"特征数量: {len(feature_cols)}")
    if model.pca:
        print(f"PCA降维后特征数量: {model.pca.n_components_}")

    print("=== 完成! ===")


if __name__ == "__main__":
    main()
