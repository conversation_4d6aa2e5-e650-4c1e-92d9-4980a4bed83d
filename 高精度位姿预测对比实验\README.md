# 高精度位姿预测对比实验

## 🎯 实验目标

本实验旨在对比三种机器人位姿预测方法的精度：
1. **理论计算** - 基于机器人运动学正解公式
2. **激光跟踪仪实测** - 高精度实际测量值（作为真值参考）
3. **机器学习预测** - 基于数据驱动的预测模型

通过系统性的对比分析，验证各种方法的精度特性，为实际工程应用提供指导。

## 📊 实验设计

### 实验数据
- **输入**: 机器人6个关节角度 [θ1, θ2, θ3, θ4, θ5, θ6]
- **输出**: 末端执行器位姿 [X, Y, Z, Rx, Ry, Rz]
- **样本数**: 2000个测试样本
- **参考标准**: 激光跟踪仪高精度测量值

### 对比维度
- **精度对比**: RMSE、MAE、最大误差
- **稳定性对比**: 误差标准差、95%置信区间
- **适用性分析**: 不同坐标轴的表现差异

## 🔧 使用方法

### 环境要求
```bash
pip install pandas numpy matplotlib seaborn scikit-learn lightgbm scipy openpyxl
```

### 快速开始
```bash
# 运行完整实验流程
cd 高精度位姿预测对比实验
python 完整实验流程.py
```

### 分步骤运行
```bash
# 1. 理论计算
python 理论计算模块.py

# 2. 机器学习预测  
python 机器学习预测模块.py

# 3. 三方对比分析
python 三方对比分析模块.py
```

## 📁 文件结构

```
高精度位姿预测对比实验/
├── README.md                    # 实验说明文档
├── 实验设计方案.md              # 详细实验设计
├── 理论计算模块.py              # 运动学理论计算
├── 机器学习预测模块.py          # ML模型训练和预测
├── 三方对比分析模块.py          # 误差分析和对比
├── 完整实验流程.py              # 一键运行完整实验
└── 生成的结果文件/
    ├── 理论位姿计算结果.xlsx
    ├── 机器学习预测结果.xlsx
    ├── 三方对比汇总表.xlsx
    ├── 样本展示数据.xlsx
    ├── 误差对比分析图.png
    ├── 精度对比雷达图.png
    └── 三方对比分析报告.md
```

## 🔍 关键技术

### 1. 理论计算模块
- **DH参数建模**: 基于标准DH参数的运动学建模
- **正向运动学**: 从关节角度计算末端位姿
- **坐标变换**: 齐次变换矩阵计算
- **欧拉角转换**: 旋转矩阵到欧拉角的转换

```python
def forward_kinematics(joint_angles):
    """正向运动学计算"""
    T = np.eye(4)
    for i in range(6):
        a, alpha, d, theta_offset = dh_params[i]
        theta = joint_angles[i] + theta_offset
        T_i = dh_transform(a, alpha, d, theta)
        T = T @ T_i
    return extract_pose(T)
```

### 2. 机器学习模块
- **最优模型选择**: 基于前期实验选择最佳算法
- **模型组合**: 不同坐标使用不同的最优模型
- **批量预测**: 高效的批量位姿预测

| 坐标 | 最优模型 | 特点 |
|------|----------|------|
| X, Y, Z | LightGBM | 位置预测精度极高 |
| Rx, Rz | 神经网络 | 复杂角度关系建模 |
| Ry | LightGBM | 稳定的角度预测 |

### 3. 对比分析模块
- **多维误差分析**: 均值、标准差、RMSE、MAE
- **可视化对比**: 箱线图、雷达图、热力图
- **统计显著性**: 误差分布的统计特性分析

## 📈 预期结果

### 精度等级定义
**位置坐标 (mm)**:
- 🏆 极高: RMSE < 1mm
- 🏆 高: RMSE < 5mm  
- ✅ 中等: RMSE < 20mm
- ⚠️ 一般: RMSE < 50mm

**姿态角度 (度)**:
- 🏆 极高: RMSE < 0.1°
- 🏆 高: RMSE < 0.5°
- ✅ 中等: RMSE < 2°
- ⚠️ 一般: RMSE < 5°

### 典型结果示例
```
坐标  理论RMSE  预测RMSE  最优方法  精度等级
X     15.234    8.456     预测      🏆 高
Y     12.789    6.234     预测      🏆 高  
Z     18.456    9.123     预测      🏆 高
Rx    2.345     1.234     预测      ✅ 中等
Ry    1.456     2.123     理论      ✅ 中等
Rz    3.789     2.456     预测      ✅ 中等
```

## ⚙️ 自定义配置

### 修改DH参数
在 `理论计算模块.py` 中修改机器人DH参数：

```python
# 根据您的机器人型号调整DH参数
dh_params = np.array([
    [a1, alpha1, d1, theta_offset1],
    [a2, alpha2, d2, theta_offset2],
    # ... 其他关节参数
])
```

### 调整分析参数
- **样本展示数量**: 修改 `create_sample_showcase(n_samples=10)`
- **精度等级阈值**: 修改 `get_accuracy_level()` 函数
- **图表样式**: 调整matplotlib参数

## 🎓 学术价值

### 适用课程
- 机器人学
- 机器学习应用
- 数值分析
- 工程测量

### 研究贡献
- 系统性的方法对比框架
- 实际工程数据的验证
- 误差特性的深入分析
- 应用指导的具体建议

## 📝 注意事项

### 重要提醒
1. **DH参数**: 请根据实际机器人调整DH参数表
2. **单位一致性**: 确保角度单位（度/弧度）的一致性
3. **坐标系**: 验证理论计算与实测的坐标系一致性
4. **数据质量**: 检查输入数据的有效性和完整性

### 常见问题
- **理论计算误差过大**: 检查DH参数是否正确
- **预测精度不佳**: 可能需要更多训练数据或特征工程
- **坐标系不匹配**: 确认理论计算与实测的坐标系定义

## 🚀 扩展方向

### 短期改进
- 添加更多机器人型号的DH参数库
- 实现自动DH参数标定功能
- 增加更多误差分析指标

### 长期发展
- 集成物理约束的机器学习方法
- 多机器人型号的通用化框架
- 实时在线学习和模型更新

---

**实验设计**: 高精度位姿预测对比研究  
**技术栈**: Python + 机器人学 + 机器学习  
**适用对象**: 研究生课程、工程项目、学术研究
