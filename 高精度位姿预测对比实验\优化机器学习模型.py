#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的机器学习模型 - 基于误差补偿方法
参考MATLAB代码的成功经验，采用误差补偿而非直接预测
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
# 使用scikit-learn替代TensorFlow
from 理论计算模块 import RobotKinematics
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ErrorCompensationModel:
    """误差补偿模型 - 学习理论值与实测值之间的误差"""
    
    def __init__(self):
        self.robot = RobotKinematics()
        self.joint_scaler = MinMaxScaler(feature_range=(-1, 1))
        self.error_scaler = MinMaxScaler(feature_range=(-1, 1))
        self.models = {}
        
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("=== 加载并准备数据 ===")
        
        # 加载原始数据
        self.joint_data = pd.read_excel('../theta2000.xlsx', header=None)
        self.joint_data.columns = [f'theta_{i+1}' for i in range(6)]
        self.measured_data = pd.read_excel('../real2000.xlsx')
        
        print(f"数据形状: 关节角度 {self.joint_data.shape}, 实测位姿 {self.measured_data.shape}")
        
        # 计算理论位姿
        print("计算理论位姿...")
        theoretical_poses = []
        for i in range(len(self.joint_data)):
            joint_angles = self.joint_data.iloc[i].values
            pose = self.robot.forward_kinematics(joint_angles)
            theoretical_poses.append(pose)
        
        self.theoretical_data = np.array(theoretical_poses)
        
        # 计算误差 (关键：学习误差而非绝对位姿)
        self.pose_errors = self.measured_data.values - self.theoretical_data
        
        print(f"误差统计:")
        print(f"位置误差范围: X[{self.pose_errors[:, 0].min():.3f}, {self.pose_errors[:, 0].max():.3f}]")
        print(f"角度误差范围: Rx[{self.pose_errors[:, 3].min():.3f}, {self.pose_errors[:, 3].max():.3f}]")
        
        return True
    
    def create_enhanced_features(self, joint_angles):
        """创建增强特征 - 参考MATLAB代码的特征工程"""
        # 原始关节角度
        features = joint_angles.copy()
        
        # 添加三角函数特征 (机器人运动学的核心)
        sin_features = np.sin(np.deg2rad(joint_angles))
        cos_features = np.cos(np.deg2rad(joint_angles))
        
        # 添加关节角度的交互项
        interaction_features = []
        for i in range(joint_angles.shape[1]):
            for j in range(i+1, joint_angles.shape[1]):
                interaction_features.append(joint_angles[:, i] * joint_angles[:, j])
        
        if interaction_features:
            interaction_features = np.column_stack(interaction_features)
        else:
            interaction_features = np.empty((joint_angles.shape[0], 0))
        
        # 组合所有特征
        enhanced_features = np.column_stack([
            features,           # 原始角度
            sin_features,       # sin(θ)
            cos_features,       # cos(θ)
            interaction_features # 交互项
        ])
        
        return enhanced_features
    
    def build_mlp_model(self, input_dim, output_dim):
        """构建MLP模型 - 使用scikit-learn"""
        # 使用MLPRegressor，参数类似MATLAB代码
        model = MLPRegressor(
            hidden_layer_sizes=(100, 50),  # 两层隐藏层
            activation='relu',
            solver='adam',
            learning_rate_init=0.0001,    # 小学习率
            max_iter=2000,
            early_stopping=True,
            validation_fraction=0.2,
            n_iter_no_change=100,
            random_state=42
        )

        return model
    
    def train_error_compensation_models(self):
        """训练误差补偿模型"""
        print("\n=== 训练误差补偿模型 ===")
        
        # 创建增强特征
        enhanced_features = self.create_enhanced_features(self.joint_data.values)
        print(f"增强特征维度: {enhanced_features.shape}")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            enhanced_features, self.pose_errors, 
            test_size=0.2, random_state=42
        )
        
        # 特征标准化
        X_train_scaled = self.joint_scaler.fit_transform(X_train)
        X_test_scaled = self.joint_scaler.transform(X_test)
        
        # 误差标准化
        y_train_scaled = self.error_scaler.fit_transform(y_train)
        y_test_scaled = self.error_scaler.transform(y_test)
        
        # 训练模型
        print("训练神经网络模型...")
        model = self.build_mlp_model(X_train_scaled.shape[1], y_train_scaled.shape[1])

        # 训练
        print("开始训练...")
        model.fit(X_train_scaled, y_train_scaled)

        self.model = model

        # 预测测试集
        y_pred_scaled = model.predict(X_test_scaled)
        y_pred = self.error_scaler.inverse_transform(y_pred_scaled)

        # 记录训练信息
        self.training_score = model.score(X_train_scaled, y_train_scaled)
        self.test_score = model.score(X_test_scaled, y_test_scaled)
        print(f"训练集得分: {self.training_score:.4f}")
        print(f"测试集得分: {self.test_score:.4f}")
        
        # 评估误差预测性能
        print(f"\n误差预测性能评估:")
        for i, coord in enumerate(['X', 'Y', 'Z', 'Rx', 'Ry', 'Rz']):
            r2 = r2_score(y_test[:, i], y_pred[:, i])
            rmse = np.sqrt(mean_squared_error(y_test[:, i], y_pred[:, i]))
            print(f"{coord} 误差预测: R² = {r2:.4f}, RMSE = {rmse:.4f}")
        
        return True
    
    def predict_compensated_poses(self, joint_angles):
        """预测补偿后的位姿"""
        # 计算理论位姿
        theoretical_poses = []
        for i in range(len(joint_angles)):
            pose = self.robot.forward_kinematics(joint_angles[i])
            theoretical_poses.append(pose)
        theoretical_poses = np.array(theoretical_poses)
        
        # 创建增强特征并预测误差
        enhanced_features = self.create_enhanced_features(joint_angles)
        features_scaled = self.joint_scaler.transform(enhanced_features)
        
        # 预测误差
        predicted_errors_scaled = self.model.predict(features_scaled)
        predicted_errors = self.error_scaler.inverse_transform(predicted_errors_scaled)
        
        # 补偿位姿 = 理论位姿 + 预测误差
        compensated_poses = theoretical_poses + predicted_errors
        
        return compensated_poses, theoretical_poses, predicted_errors
    
    def comprehensive_evaluation(self):
        """综合评估"""
        print("\n=== 综合评估 ===")
        
        # 对所有数据进行预测
        compensated_poses, theoretical_poses, predicted_errors = self.predict_compensated_poses(
            self.joint_data.values
        )
        
        measured_poses = self.measured_data.values
        
        # 计算各种误差
        # 1. 理论误差 (理论值 vs 实测值)
        theory_errors = measured_poses - theoretical_poses
        theory_pos_errors = np.sqrt(np.sum(theory_errors[:, :3]**2, axis=1))
        theory_angle_errors = np.sqrt(np.sum(theory_errors[:, 3:]**2, axis=1))
        
        # 2. 补偿后误差 (补偿值 vs 实测值)
        compensated_errors = measured_poses - compensated_poses
        compensated_pos_errors = np.sqrt(np.sum(compensated_errors[:, :3]**2, axis=1))
        compensated_angle_errors = np.sqrt(np.sum(compensated_errors[:, 3:]**2, axis=1))
        
        # 统计结果
        print(f"位置误差对比:")
        print(f"理论计算: 平均 {np.mean(theory_pos_errors):.3f} mm, 标准差 {np.std(theory_pos_errors):.3f} mm")
        print(f"误差补偿: 平均 {np.mean(compensated_pos_errors):.3f} mm, 标准差 {np.std(compensated_pos_errors):.3f} mm")
        print(f"改进幅度: {(1 - np.mean(compensated_pos_errors)/np.mean(theory_pos_errors))*100:.1f}%")
        
        print(f"\n角度误差对比:")
        print(f"理论计算: 平均 {np.mean(theory_angle_errors):.3f} 度, 标准差 {np.std(theory_angle_errors):.3f} 度")
        print(f"误差补偿: 平均 {np.mean(compensated_angle_errors):.3f} 度, 标准差 {np.std(compensated_angle_errors):.3f} 度")
        print(f"改进幅度: {(1 - np.mean(compensated_angle_errors)/np.mean(theory_angle_errors))*100:.1f}%")
        
        # 保存结果
        results = {
            'theory_pos_errors': theory_pos_errors,
            'theory_angle_errors': theory_angle_errors,
            'compensated_pos_errors': compensated_pos_errors,
            'compensated_angle_errors': compensated_angle_errors,
            'compensated_poses': compensated_poses,
            'theoretical_poses': theoretical_poses,
            'predicted_errors': predicted_errors
        }
        
        return results
    
    def plot_results(self, results):
        """绘制结果对比"""
        print("\n=== 生成对比图表 ===")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 位置误差对比
        axes[0, 0].boxplot([results['theory_pos_errors'], results['compensated_pos_errors']], 
                          labels=['理论计算', '误差补偿'])
        axes[0, 0].set_title('位置误差分布对比')
        axes[0, 0].set_ylabel('位置误差 (mm)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 角度误差对比
        axes[0, 1].boxplot([results['theory_angle_errors'], results['compensated_angle_errors']], 
                          labels=['理论计算', '误差补偿'])
        axes[0, 1].set_title('角度误差分布对比')
        axes[0, 1].set_ylabel('角度误差 (度)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 训练性能展示
        axes[0, 2].bar(['训练集', '测试集'], [self.training_score, self.test_score])
        axes[0, 2].set_title('模型性能对比')
        axes[0, 2].set_ylabel('R² Score')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 位置误差散点图
        axes[1, 0].scatter(results['theory_pos_errors'], results['compensated_pos_errors'], alpha=0.6)
        max_pos = max(max(results['theory_pos_errors']), max(results['compensated_pos_errors']))
        axes[1, 0].plot([0, max_pos], [0, max_pos], 'r--', alpha=0.8)
        axes[1, 0].set_xlabel('理论计算位置误差 (mm)')
        axes[1, 0].set_ylabel('误差补偿位置误差 (mm)')
        axes[1, 0].set_title('位置误差改进效果')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 角度误差散点图
        axes[1, 1].scatter(results['theory_angle_errors'], results['compensated_angle_errors'], alpha=0.6)
        max_angle = max(max(results['theory_angle_errors']), max(results['compensated_angle_errors']))
        axes[1, 1].plot([0, max_angle], [0, max_angle], 'r--', alpha=0.8)
        axes[1, 1].set_xlabel('理论计算角度误差 (度)')
        axes[1, 1].set_ylabel('误差补偿角度误差 (度)')
        axes[1, 1].set_title('角度误差改进效果')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 样本误差对比
        sample_indices = range(0, min(200, len(results['theory_pos_errors'])))
        axes[1, 2].plot(sample_indices, results['theory_pos_errors'][:len(sample_indices)], 
                       'o-', label='理论计算', markersize=3)
        axes[1, 2].plot(sample_indices, results['compensated_pos_errors'][:len(sample_indices)], 
                       'd-', label='误差补偿', markersize=3)
        axes[1, 2].set_xlabel('样本编号')
        axes[1, 2].set_ylabel('位置误差 (mm)')
        axes[1, 2].set_title('样本位置误差对比')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('误差补偿模型对比.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_sample_showcase(self, n_samples=10):
        """创建样本展示"""
        print(f"\n=== 创建 {n_samples} 个样本展示 ===")
        
        # 预测所有样本
        compensated_poses, theoretical_poses, predicted_errors = self.predict_compensated_poses(
            self.joint_data.values
        )
        
        # 选择代表性样本
        indices = np.linspace(0, len(self.joint_data)-1, n_samples, dtype=int)
        
        showcase_data = []
        
        for idx in indices:
            measured = self.measured_data.iloc[idx].values
            theoretical = theoretical_poses[idx]
            compensated = compensated_poses[idx]
            predicted_error = predicted_errors[idx]
            actual_error = self.pose_errors[idx]
            
            # 计算误差
            theory_pos_error = np.sqrt(np.sum((theoretical[:3] - measured[:3])**2))
            compensated_pos_error = np.sqrt(np.sum((compensated[:3] - measured[:3])**2))
            
            sample = {
                '样本编号': idx,
                '关节角度': f"[{', '.join([f'{self.joint_data.iloc[idx, i]:.1f}' for i in range(6)])}]",
                
                # 位置对比
                'X_实测': f"{measured[0]:.1f}",
                'X_理论': f"{theoretical[0]:.1f}",
                'X_补偿': f"{compensated[0]:.1f}",
                
                'Y_实测': f"{measured[1]:.1f}",
                'Y_理论': f"{theoretical[1]:.1f}",
                'Y_补偿': f"{compensated[1]:.1f}",
                
                'Z_实测': f"{measured[2]:.1f}",
                'Z_理论': f"{theoretical[2]:.1f}",
                'Z_补偿': f"{compensated[2]:.1f}",
                
                # 误差对比
                '理论位置误差': f"{theory_pos_error:.3f}",
                '补偿位置误差': f"{compensated_pos_error:.3f}",
                '改进幅度': f"{(1-compensated_pos_error/theory_pos_error)*100:.1f}%",
                
                # 预测的误差 vs 实际误差
                'X误差_实际': f"{actual_error[0]:.3f}",
                'X误差_预测': f"{predicted_error[0]:.3f}",
            }
            
            showcase_data.append(sample)
        
        showcase_df = pd.DataFrame(showcase_data)
        showcase_df.to_excel('误差补偿模型展示.xlsx', index=False)
        
        print(f"误差补偿模型展示数据已保存: {len(showcase_df)} 个样本")
        return showcase_df

def main():
    """主函数"""
    print("开始优化机器学习模型训练...")
    
    # 创建误差补偿模型
    model = ErrorCompensationModel()
    
    # 加载和准备数据
    if not model.load_and_prepare_data():
        return False
    
    # 训练误差补偿模型
    if not model.train_error_compensation_models():
        return False
    
    # 综合评估
    results = model.comprehensive_evaluation()
    
    # 绘制结果
    model.plot_results(results)
    
    # 创建样本展示
    showcase_df = model.create_sample_showcase(10)
    
    print("\n✅ 优化机器学习模型训练完成！")
    print("主要改进:")
    print("1. 采用误差补偿方法而非直接预测")
    print("2. 增强特征工程（三角函数、交互项）")
    print("3. 优化网络结构和训练策略")
    
    return True

if __name__ == "__main__":
    main()
