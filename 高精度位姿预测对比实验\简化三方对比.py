#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化三方对比实验
重点对比位置精度：理论值 vs 实测值 vs 机器学习预测值
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
import lightgbm as lgb
from 理论计算模块 import RobotKinematics
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SimplifiedComparison:
    """简化的三方对比分析"""
    
    def __init__(self):
        self.robot = RobotKinematics()
        
    def load_data(self):
        """加载数据"""
        print("=== 加载数据 ===")
        
        # 加载关节角度数据
        self.joint_data = pd.read_excel('../theta2000.xlsx', header=None)
        self.joint_data.columns = [f'theta_{i+1}' for i in range(6)]
        
        # 加载实测数据
        self.measured_data = pd.read_excel('../real2000.xlsx')
        
        print(f"关节角度数据: {self.joint_data.shape}")
        print(f"实测数据: {self.measured_data.shape}")
        
        return True
    
    def calculate_theoretical_poses(self):
        """计算理论位姿"""
        print("\n=== 计算理论位姿 ===")
        
        theoretical_poses = []
        for i in range(len(self.joint_data)):
            joint_angles = self.joint_data.iloc[i].values
            pose = self.robot.forward_kinematics(joint_angles)
            theoretical_poses.append(pose)
        
        self.theoretical_data = pd.DataFrame(theoretical_poses, 
                                           columns=['X_theory', 'Y_theory', 'Z_theory',
                                                   'Rx_theory', 'Ry_theory', 'Rz_theory'])
        
        print(f"理论位姿计算完成: {self.theoretical_data.shape}")
        return True
    
    def train_ml_models(self):
        """训练机器学习模型（仅位置坐标）"""
        print("\n=== 训练机器学习模型 ===")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            self.joint_data, self.measured_data.iloc[:, :3],  # 只预测位置坐标
            test_size=0.2, random_state=42
        )
        
        # 训练LightGBM模型预测位置坐标
        self.ml_models = {}
        self.ml_predictions = np.zeros((len(self.joint_data), 3))
        
        coord_names = ['X', 'Y', 'Z']
        for i, coord in enumerate(coord_names):
            print(f"训练 {coord} 坐标预测模型...")
            
            model = lgb.LGBMRegressor(n_estimators=100, random_state=42, verbose=-1)
            model.fit(X_train, y_train.iloc[:, i])
            
            # 预测所有样本
            predictions = model.predict(self.joint_data)
            self.ml_predictions[:, i] = predictions
            
            self.ml_models[coord] = model
            
            # 评估性能
            test_pred = model.predict(X_test)
            rmse = np.sqrt(np.mean((test_pred - y_test.iloc[:, i])**2))
            print(f"  {coord} 坐标 RMSE: {rmse:.3f} mm")
        
        self.ml_data = pd.DataFrame(self.ml_predictions, 
                                   columns=['X_ml', 'Y_ml', 'Z_ml'])
        
        print("机器学习模型训练完成")
        return True
    
    def calculate_position_errors(self):
        """计算位置误差"""
        print("\n=== 计算位置误差 ===")
        
        # 提取位置数据
        measured_pos = self.measured_data.iloc[:, :3].values
        theoretical_pos = self.theoretical_data.iloc[:, :3].values
        ml_pos = self.ml_predictions
        
        # 计算位置误差
        self.theory_errors = np.sqrt(np.sum((theoretical_pos - measured_pos)**2, axis=1))
        self.ml_errors = np.sqrt(np.sum((ml_pos - measured_pos)**2, axis=1))
        
        # 统计信息
        print("位置误差统计:")
        print(f"理论计算:")
        print(f"  平均误差: {np.mean(self.theory_errors):.3f} mm")
        print(f"  标准差:   {np.std(self.theory_errors):.3f} mm")
        print(f"  最大误差: {np.max(self.theory_errors):.3f} mm")
        
        print(f"机器学习:")
        print(f"  平均误差: {np.mean(self.ml_errors):.3f} mm")
        print(f"  标准差:   {np.std(self.ml_errors):.3f} mm")
        print(f"  最大误差: {np.max(self.ml_errors):.3f} mm")
        
        return True
    
    def create_sample_showcase(self, n_samples=20):
        """创建样本展示"""
        print(f"\n=== 创建 {n_samples} 个样本展示 ===")
        
        # 选择代表性样本
        indices = np.linspace(0, len(self.joint_data)-1, n_samples, dtype=int)
        
        showcase_data = []
        
        for idx in indices:
            # 实测值
            measured = self.measured_data.iloc[idx, :3].values
            
            # 理论值
            theoretical = self.theoretical_data.iloc[idx, :3].values
            
            # 预测值
            ml_pred = self.ml_predictions[idx]
            
            # 误差
            theory_error = self.theory_errors[idx]
            ml_error = self.ml_errors[idx]
            
            sample = {
                '样本编号': idx,
                '关节角度': f"[{', '.join([f'{self.joint_data.iloc[idx, i]:.1f}' for i in range(6)])}]",
                
                'X_实测': f"{measured[0]:.1f}",
                'Y_实测': f"{measured[1]:.1f}",
                'Z_实测': f"{measured[2]:.1f}",
                
                'X_理论': f"{theoretical[0]:.1f}",
                'Y_理论': f"{theoretical[1]:.1f}",
                'Z_理论': f"{theoretical[2]:.1f}",
                
                'X_预测': f"{ml_pred[0]:.1f}",
                'Y_预测': f"{ml_pred[1]:.1f}",
                'Z_预测': f"{ml_pred[2]:.1f}",
                
                '理论误差': f"{theory_error:.3f}",
                '预测误差': f"{ml_error:.3f}",
                
                '最优方法': '理论' if theory_error < ml_error else '预测'
            }
            
            showcase_data.append(sample)
        
        showcase_df = pd.DataFrame(showcase_data)
        showcase_df.to_excel('位置预测样本展示.xlsx', index=False)
        
        print(f"样本展示数据已保存: {len(showcase_df)} 个样本")
        return showcase_df
    
    def plot_comparison(self):
        """绘制对比图表"""
        print("\n=== 生成对比图表 ===")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 误差分布对比
        axes[0, 0].boxplot([self.theory_errors, self.ml_errors], 
                          labels=['理论计算', '机器学习'])
        axes[0, 0].set_title('位置误差分布对比')
        axes[0, 0].set_ylabel('位置误差 (mm)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 误差散点图
        axes[0, 1].scatter(self.theory_errors, self.ml_errors, alpha=0.6)
        axes[0, 1].plot([0, max(max(self.theory_errors), max(self.ml_errors))], 
                       [0, max(max(self.theory_errors), max(self.ml_errors))], 
                       'r--', alpha=0.8)
        axes[0, 1].set_xlabel('理论计算误差 (mm)')
        axes[0, 1].set_ylabel('机器学习误差 (mm)')
        axes[0, 1].set_title('误差对比散点图')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 误差直方图
        axes[1, 0].hist(self.theory_errors, bins=30, alpha=0.7, label='理论计算', color='blue')
        axes[1, 0].hist(self.ml_errors, bins=30, alpha=0.7, label='机器学习', color='red')
        axes[1, 0].set_xlabel('位置误差 (mm)')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title('误差分布直方图')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 累积分布
        sorted_theory = np.sort(self.theory_errors)
        sorted_ml = np.sort(self.ml_errors)
        y = np.arange(1, len(sorted_theory) + 1) / len(sorted_theory)
        
        axes[1, 1].plot(sorted_theory, y, label='理论计算', linewidth=2)
        axes[1, 1].plot(sorted_ml, y, label='机器学习', linewidth=2)
        axes[1, 1].set_xlabel('位置误差 (mm)')
        axes[1, 1].set_ylabel('累积概率')
        axes[1, 1].set_title('误差累积分布函数')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('位置预测三方对比.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_summary_report(self):
        """生成总结报告"""
        print("\n=== 生成总结报告 ===")
        
        # 计算统计指标
        theory_stats = {
            'mean': np.mean(self.theory_errors),
            'std': np.std(self.theory_errors),
            'max': np.max(self.theory_errors),
            'min': np.min(self.theory_errors),
            'p95': np.percentile(self.theory_errors, 95)
        }
        
        ml_stats = {
            'mean': np.mean(self.ml_errors),
            'std': np.std(self.ml_errors),
            'max': np.max(self.ml_errors),
            'min': np.min(self.ml_errors),
            'p95': np.percentile(self.ml_errors, 95)
        }
        
        # 确定最优方法
        theory_wins = np.sum(self.theory_errors < self.ml_errors)
        ml_wins = np.sum(self.ml_errors < self.theory_errors)
        
        print("="*60)
        print("位置预测三方对比实验总结报告")
        print("="*60)
        
        print(f"\n📊 整体性能对比:")
        print(f"样本总数: {len(self.theory_errors)}")
        print(f"理论计算优于机器学习: {theory_wins} 个样本 ({theory_wins/len(self.theory_errors)*100:.1f}%)")
        print(f"机器学习优于理论计算: {ml_wins} 个样本 ({ml_wins/len(self.theory_errors)*100:.1f}%)")
        
        print(f"\n📈 误差统计对比:")
        print(f"{'指标':<12} {'理论计算':<12} {'机器学习':<12} {'优势':<8}")
        print("-" * 50)
        print(f"{'平均误差':<12} {theory_stats['mean']:<12.3f} {ml_stats['mean']:<12.3f} {'理论' if theory_stats['mean'] < ml_stats['mean'] else '机器学习':<8}")
        print(f"{'标准差':<12} {theory_stats['std']:<12.3f} {ml_stats['std']:<12.3f} {'理论' if theory_stats['std'] < ml_stats['std'] else '机器学习':<8}")
        print(f"{'最大误差':<12} {theory_stats['max']:<12.3f} {ml_stats['max']:<12.3f} {'理论' if theory_stats['max'] < ml_stats['max'] else '机器学习':<8}")
        print(f"{'95%分位数':<12} {theory_stats['p95']:<12.3f} {ml_stats['p95']:<12.3f} {'理论' if theory_stats['p95'] < ml_stats['p95'] else '机器学习':<8}")
        
        print(f"\n🎯 结论:")
        if theory_stats['mean'] < ml_stats['mean']:
            print("✅ 理论计算在位置预测上整体表现更优")
            print(f"   平均误差降低: {((ml_stats['mean'] - theory_stats['mean'])/ml_stats['mean']*100):.1f}%")
        else:
            print("✅ 机器学习在位置预测上整体表现更优")
            print(f"   平均误差降低: {((theory_stats['mean'] - ml_stats['mean'])/theory_stats['mean']*100):.1f}%")
        
        print(f"\n💡 应用建议:")
        if theory_stats['mean'] < 1.0:
            print("• 理论计算精度已达到亚毫米级，可直接用于工业应用")
        if ml_stats['mean'] < 1.0:
            print("• 机器学习预测精度已达到亚毫米级，可用于实时预测")
        
        print(f"\n📁 生成文件:")
        print("• 位置预测样本展示.xlsx - 详细样本对比")
        print("• 位置预测三方对比.png - 可视化对比图表")

def main():
    """主函数"""
    print("开始简化三方对比实验...")
    
    # 创建对比分析器
    comparison = SimplifiedComparison()
    
    # 执行实验流程
    if not comparison.load_data():
        return False
    
    if not comparison.calculate_theoretical_poses():
        return False
    
    if not comparison.train_ml_models():
        return False
    
    if not comparison.calculate_position_errors():
        return False
    
    # 生成结果
    showcase_df = comparison.create_sample_showcase(20)
    comparison.plot_comparison()
    comparison.generate_summary_report()
    
    print("\n✅ 简化三方对比实验完成！")
    return True

if __name__ == "__main__":
    main()
