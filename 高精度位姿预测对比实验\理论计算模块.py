#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器人运动学理论计算模块
用于计算基于DH参数的理论位姿值
"""

import numpy as np
import pandas as pd
from scipy.spatial.transform import Rotation as R

class RobotKinematics:
    """机器人运动学计算类"""
    
    def __init__(self, dh_params=None):
        """
        初始化机器人运动学参数
        
        Parameters:
        dh_params: DH参数表 [[a, alpha, d, theta_offset], ...]
                   如果为None，将使用通用6轴机器人参数
        """
        if dh_params is None:
            # Staubli TX60机器人的M-DH参数 (基于论文中的表1)
            # 格式: [a(连杆长度), alpha(连杆扭转角), d(连杆偏距), theta_offset(关节角偏移), beta(Y轴旋转)]
            # 单位: 长度(mm), 角度(rad)
            # 注意：论文使用修正DH参数，包含额外的beta参数
            self.dh_params = np.array([
                [0,   np.pi/2,        0,   np.pi,     0],      # 关节1: θ=π, d=0, a=0, α=π/2
                [290, 0,        0,   np.pi/2,   0],      # 关节2: θ=π/2, d=0, a=290, α=0
                [0,   np.pi/2,  20,  np.pi/2,   0],      # 关节3: θ=π/2, d=20, a=0, α=π/2
                [0,   np.pi/2,  310, np.pi,     0],      # 关节4: θ=π, d=310, a=0, α=π/2
                [0,   np.pi/2,  0,   np.pi,     0],      # 关节5: θ=π, d=0, a=0, α=π/2
                [0,   0,        70,  0,         0]       # 关节6: θ=0, d=70, a=0, α=0
            ])
            print("使用Staubli TX60机器人的M-DH参数（基于论文）")
        else:
            self.dh_params = np.array(dh_params)
    
    def mdh_transform(self, a, alpha, d, theta, beta=0):
        """
        计算修正DH变换矩阵 (M-DH)
        基于论文中的公式(1): A_i = Rot(Z_i,θ_i)Trans(Z_i,d_i)Trans(X_i,a_i)Rot(X_i,α_i)Rot(Y_i,β_i)

        Parameters:
        a: 连杆长度 (mm)
        alpha: 连杆扭转角 (rad)
        d: 连杆偏距 (mm)
        theta: 关节角 (rad)
        beta: Y轴旋转角 (rad)

        Returns:
        4x4变换矩阵
        """
        # 转换单位：mm -> m
        a_m = a / 1000.0
        d_m = d / 1000.0

        ct = np.cos(theta)
        st = np.sin(theta)
        ca = np.cos(alpha)
        sa = np.sin(alpha)
        cb = np.cos(beta)
        sb = np.sin(beta)

        # 根据论文公式(1)的M-DH变换矩阵
        T = np.array([
            [ct*cb - st*sa*sb,  -st*ca,  ct*sb + st*sa*cb,  a_m*ct],
            [st*cb + ct*sa*sb,   ct*ca,  st*sb - ct*sa*cb,  a_m*st],
            [-ca*sb,             sa,     ca*cb,             d_m],
            [0,                  0,      0,                 1]
        ])

        return T
    
    def forward_kinematics(self, joint_angles):
        """
        Staubli TX60正向运动学计算
        基于论文中的M-DH参数和变换矩阵

        Parameters:
        joint_angles: 关节角度数组 [θ1, θ2, θ3, θ4, θ5, θ6] (度)

        Returns:
        pose: [X, Y, Z, Rx, Ry, Rz] 位姿参数 (位置单位:mm, 角度单位:度)
        """
        # 确保输入是numpy数组，并转换为弧度
        joint_angles = np.array(joint_angles)
        joint_angles_rad = np.deg2rad(joint_angles)

        # 初始化变换矩阵为单位矩阵
        T = np.eye(4)

        # 逐个关节计算变换矩阵
        for i in range(6):
            a, alpha, d, theta_offset, beta = self.dh_params[i]
            theta = joint_angles_rad[i] + theta_offset

            # 计算当前关节的M-DH变换矩阵
            T_i = self.mdh_transform(a, alpha, d, theta, beta)

            # 累积变换
            T = T @ T_i

        # 提取位置 (转换为mm)
        position = T[:3, 3] * 1000.0  # m -> mm

        # 提取旋转矩阵并转换为欧拉角
        rotation_matrix = T[:3, :3]

        # 使用最佳方法：XYZ外旋欧拉角 (测试证明误差最小)
        from scipy.spatial.transform import Rotation as Rot
        r = Rot.from_matrix(rotation_matrix)
        euler_angles = r.as_euler('XYZ', degrees=True)  # 外旋，与激光跟踪仪匹配

        # 组合位姿
        pose = np.concatenate([position, euler_angles])

        return pose

    def rotation_matrix_to_euler_xyz(self, R):
        """
        将旋转矩阵转换为XYZ顺序的欧拉角
        基于标准的旋转矩阵到欧拉角转换公式

        Parameters:
        R: 3x3旋转矩阵

        Returns:
        euler_angles: [Rx, Ry, Rz] 欧拉角 (度)
        """
        # 方法1: 标准XYZ内旋欧拉角公式
        # 参考: https://www.learnopencv.com/rotation-matrix-to-euler-angles/

        # 检查奇异性
        sy = np.sqrt(R[0, 0] * R[0, 0] + R[1, 0] * R[1, 0])

        singular = sy < 1e-6

        if not singular:
            # 非奇异情况
            x = np.arctan2(R[2, 1], R[2, 2])  # Rx (绕X轴旋转)
            y = np.arctan2(-R[2, 0], sy)      # Ry (绕Y轴旋转)
            z = np.arctan2(R[1, 0], R[0, 0])  # Rz (绕Z轴旋转)
        else:
            # 奇异情况处理
            x = np.arctan2(-R[1, 2], R[1, 1])
            y = np.arctan2(-R[2, 0], sy)
            z = 0

        # 转换为度并返回
        return np.array([np.rad2deg(x), np.rad2deg(y), np.rad2deg(z)])

    def rotation_matrix_to_euler_zyx(self, R):
        """
        将旋转矩阵转换为ZYX顺序的欧拉角 (RPY角)
        这是机器人学中常用的表示方法

        Parameters:
        R: 3x3旋转矩阵

        Returns:
        euler_angles: [Roll, Pitch, Yaw] 欧拉角 (度)
        """
        # ZYX欧拉角 (Roll-Pitch-Yaw)
        # Roll (绕X轴)
        roll = np.arctan2(R[2, 1], R[2, 2])

        # Pitch (绕Y轴)
        pitch = np.arctan2(-R[2, 0], np.sqrt(R[2, 1]**2 + R[2, 2]**2))

        # Yaw (绕Z轴)
        yaw = np.arctan2(R[1, 0], R[0, 0])

        return np.array([np.rad2deg(roll), np.rad2deg(pitch), np.rad2deg(yaw)])

    def test_euler_conversion_methods(self, R):
        """
        测试不同的欧拉角转换方法
        """
        # 方法1: XYZ内旋
        xyz_intrinsic = self.rotation_matrix_to_euler_xyz(R)

        # 方法2: ZYX欧拉角 (RPY)
        zyx_rpy = self.rotation_matrix_to_euler_zyx(R)

        # 方法3: SciPy库的不同顺序
        from scipy.spatial.transform import Rotation as Rot
        r = Rot.from_matrix(R)
        xyz_scipy = r.as_euler('xyz', degrees=True)
        zyx_scipy = r.as_euler('zyx', degrees=True)

        return {
            'xyz_intrinsic': xyz_intrinsic,
            'zyx_rpy': zyx_rpy,
            'xyz_scipy': xyz_scipy,
            'zyx_scipy': zyx_scipy
        }
    
    def batch_forward_kinematics(self, joint_angles_batch):
        """
        批量正向运动学计算
        
        Parameters:
        joint_angles_batch: 关节角度批量数据 (N, 6)
        
        Returns:
        poses: 位姿批量数据 (N, 6)
        """
        poses = []
        
        for joint_angles in joint_angles_batch:
            pose = self.forward_kinematics(joint_angles)
            poses.append(pose)
        
        return np.array(poses)

def load_and_calculate_theoretical_poses():
    """
    加载数据并计算理论位姿值
    """
    print("=== 理论位姿计算 ===")
    
    # 加载关节角度数据
    joint_data = pd.read_excel('../theta2000.xlsx', header=None)
    joint_data.columns = [f'theta_{i+1}' for i in range(6)]
    
    print(f"加载关节角度数据: {joint_data.shape}")
    print("关节角度范围:")
    print(joint_data.describe())
    
    # 创建运动学计算器
    robot = RobotKinematics()

    # 计算理论位姿 (输入已经是度，函数内部会处理转换)
    print("\n开始计算理论位姿...")
    theoretical_poses = robot.batch_forward_kinematics(joint_data.values)
    
    # 创建结果DataFrame
    pose_columns = ['X_theory', 'Y_theory', 'Z_theory', 
                   'Rx_theory', 'Ry_theory', 'Rz_theory']
    theoretical_df = pd.DataFrame(theoretical_poses, columns=pose_columns)
    
    print(f"理论位姿计算完成: {theoretical_df.shape}")
    print("理论位姿统计:")
    print(theoretical_df.describe())
    
    # 保存结果
    theoretical_df.to_excel('理论位姿计算结果.xlsx', index=False)
    print("\n理论位姿已保存到: 理论位姿计算结果.xlsx")
    
    return theoretical_df, joint_data

def validate_theoretical_calculation():
    """
    验证理论计算的合理性
    """
    print("\n=== 理论计算验证 ===")
    
    # 测试单个样本
    robot = RobotKinematics()
    
    # 测试零位姿
    zero_angles = np.zeros(6)
    zero_pose = robot.forward_kinematics(zero_angles)
    print(f"零位姿计算结果: {zero_pose}")
    
    # 测试已知位姿
    test_angles = np.array([0, np.pi/4, -np.pi/4, 0, np.pi/4, 0])
    test_pose = robot.forward_kinematics(test_angles)
    print(f"测试位姿计算结果: {test_pose}")
    
    # 检查计算的连续性
    angles1 = np.array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1])
    angles2 = np.array([0.11, 0.11, 0.11, 0.11, 0.11, 0.11])
    
    pose1 = robot.forward_kinematics(angles1)
    pose2 = robot.forward_kinematics(angles2)
    
    diff = np.abs(pose2 - pose1)
    print(f"微小角度变化的位姿差异: {diff}")
    print(f"最大位姿差异: {np.max(diff)}")

def create_sample_comparison():
    """
    创建样本对比数据用于展示
    """
    print("\n=== 创建样本对比数据 ===")
    
    # 加载实际测量数据
    measured_data = pd.read_excel('../real2000.xlsx')
    
    # 计算理论数据
    theoretical_df, joint_data = load_and_calculate_theoretical_poses()
    
    # 选择代表性样本 (每100个选1个)
    sample_indices = range(0, len(joint_data), 100)
    
    # 创建对比数据
    comparison_data = []
    
    for idx in sample_indices:
        sample = {
            'sample_id': idx,
            'theta_1': joint_data.iloc[idx, 0],
            'theta_2': joint_data.iloc[idx, 1], 
            'theta_3': joint_data.iloc[idx, 2],
            'theta_4': joint_data.iloc[idx, 3],
            'theta_5': joint_data.iloc[idx, 4],
            'theta_6': joint_data.iloc[idx, 5],
            
            # 理论值
            'X_theory': theoretical_df.iloc[idx, 0],
            'Y_theory': theoretical_df.iloc[idx, 1],
            'Z_theory': theoretical_df.iloc[idx, 2],
            'Rx_theory': theoretical_df.iloc[idx, 3],
            'Ry_theory': theoretical_df.iloc[idx, 4],
            'Rz_theory': theoretical_df.iloc[idx, 5],
            
            # 实测值
            'X_measured': measured_data.iloc[idx, 0],
            'Y_measured': measured_data.iloc[idx, 1],
            'Z_measured': measured_data.iloc[idx, 2],
            'Rx_measured': measured_data.iloc[idx, 3],
            'Ry_measured': measured_data.iloc[idx, 4],
            'Rz_measured': measured_data.iloc[idx, 5],
        }
        comparison_data.append(sample)
    
    comparison_df = pd.DataFrame(comparison_data)
    
    # 计算理论值与实测值的误差
    for coord in ['X', 'Y', 'Z', 'Rx', 'Ry', 'Rz']:
        comparison_df[f'{coord}_error'] = (comparison_df[f'{coord}_theory'] - 
                                         comparison_df[f'{coord}_measured'])
    
    # 保存对比数据
    comparison_df.to_excel('样本对比数据.xlsx', index=False)
    print(f"样本对比数据已保存: {len(comparison_df)} 个样本")
    
    return comparison_df

if __name__ == "__main__":
    # 验证理论计算
    validate_theoretical_calculation()
    
    # 计算所有样本的理论位姿
    theoretical_df, joint_data = load_and_calculate_theoretical_poses()
    
    # 创建样本对比数据
    comparison_df = create_sample_comparison()
    
    print("\n理论计算模块运行完成！")
    print("生成文件:")
    print("- 理论位姿计算结果.xlsx")
    print("- 样本对比数据.xlsx")
