#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simplified PINN Implementation for Robot Pose Error Compensation
Avoiding encoding issues and focusing on core functionality

Author: <PERSON>
Date: 2025-07-20
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Configure matplotlib for better compatibility
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class RobotKinematics:
    """Robot kinematics calculation"""
    
    def __init__(self):
        # Staubli TX60 M-DH parameters [a, alpha, d, theta_offset, beta]
        self.dh_params = np.array([
            [0,   np.pi/2,  0,   np.pi,     0],      # Joint 1
            [290, 0,        0,   np.pi/2,   0],      # Joint 2  
            [0,   np.pi/2,  20,  np.pi/2,   0],      # Joint 3
            [0,   np.pi/2,  310, np.pi,     0],      # Joint 4
            [0,   np.pi/2,  0,   np.pi,     0],      # Joint 5
            [0,   0,        70,  0,         0]       # Joint 6
        ])
        
    def mdh_transform(self, a, alpha, d, theta, beta=0):
        """Modified DH transformation matrix"""
        a_m = a / 1000.0  # mm to m
        d_m = d / 1000.0
        
        ct, st = np.cos(theta), np.sin(theta)
        ca, sa = np.cos(alpha), np.sin(alpha)
        cb, sb = np.cos(beta), np.sin(beta)
        
        T = np.array([
            [ct*cb - st*sa*sb,  -st*ca,  ct*sb + st*sa*cb,  a_m*ct],
            [st*cb + ct*sa*sb,   ct*ca,  st*sb - ct*sa*cb,  a_m*st],
            [-ca*sb,             sa,     ca*cb,             d_m],
            [0,                  0,      0,                 1]
        ])
        return T
    
    def forward_kinematics(self, joint_angles):
        """Forward kinematics calculation"""
        joint_angles_rad = np.deg2rad(joint_angles)
        T = np.eye(4)
        
        for i in range(6):
            a, alpha, d, theta_offset, beta = self.dh_params[i]
            theta = joint_angles_rad[i] + theta_offset
            T_i = self.mdh_transform(a, alpha, d, theta, beta)
            T = T @ T_i
        
        # Extract position and orientation
        position = T[:3, 3] * 1000.0  # m to mm
        rotation_matrix = T[:3, :3]
        
        # Convert to Euler angles using scipy
        from scipy.spatial.transform import Rotation as R
        r = R.from_matrix(rotation_matrix)
        euler_angles = r.as_euler('XYZ', degrees=True)
        
        return np.concatenate([position, euler_angles])

class SimplePINN(nn.Module):
    """Simplified Physics-Informed Neural Network"""
    
    def __init__(self, input_dim=63, hidden_dim=128, output_dim=6):
        super(SimplePINN, self).__init__()
        
        self.robot = RobotKinematics()
        
        # Network architecture
        self.layers = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.ReLU(),
            nn.Linear(hidden_dim//2, output_dim)
        )
        
    def forward(self, x):
        return self.layers(x)
    
    def physics_loss(self, predictions):
        """Simplified physics constraint loss"""
        # Magnitude constraint
        magnitude_loss = torch.mean(torch.abs(predictions)) * 0.01
        
        # Continuity constraint
        if predictions.size(0) > 1:
            continuity_loss = torch.mean(torch.abs(predictions[1:] - predictions[:-1])) * 0.001
        else:
            continuity_loss = 0
        
        return magnitude_loss + continuity_loss

def create_enhanced_features(joint_angles):
    """Create 63-dimensional enhanced features"""
    features = []
    angles_rad = np.deg2rad(joint_angles)
    
    # 1. Original features (6 dim)
    features.append(joint_angles)
    
    # 2. Trigonometric features (24 dim)
    features.extend([
        np.sin(angles_rad), np.cos(angles_rad),
        np.sin(2 * angles_rad), np.cos(2 * angles_rad)
    ])
    
    # 3. Polynomial features (12 dim)
    features.extend([joint_angles ** 2, joint_angles ** 3])
    
    # 4. Joint interaction features (15 dim)
    interactions = []
    for i in range(6):
        for j in range(i+1, 6):
            interactions.append((joint_angles[:, i] * joint_angles[:, j]).reshape(-1, 1))
    features.append(np.column_stack(interactions))
    
    # 5. Workspace features (3 dim)
    workspace_x = (np.cos(angles_rad[:, 0]) * np.cos(angles_rad[:, 1])).reshape(-1, 1)
    workspace_y = (np.sin(angles_rad[:, 0]) * np.cos(angles_rad[:, 1])).reshape(-1, 1)
    workspace_z = np.sin(angles_rad[:, 1]).reshape(-1, 1)
    features.append(np.column_stack([workspace_x, workspace_y, workspace_z]))
    
    # 6. Singularity features (3 dim)
    wrist_sing = np.abs(np.sin(angles_rad[:, 4])).reshape(-1, 1)
    shoulder_sing = np.abs(np.sin(angles_rad[:, 1]) * np.sin(angles_rad[:, 2])).reshape(-1, 1)
    elbow_sing = np.abs(np.cos(angles_rad[:, 2])).reshape(-1, 1)
    features.append(np.column_stack([wrist_sing, shoulder_sing, elbow_sing]))
    
    return np.column_stack(features)

def main():
    """Main function"""
    print("=" * 60)
    print("Simplified PINN for Robot Pose Error Compensation")
    print("=" * 60)
    
    # Set random seeds
    np.random.seed(42)
    torch.manual_seed(42)
    
    try:
        # 1. Load data
        print("Loading data...")
        joint_data_df = pd.read_excel('../theta2000.xlsx', header=None)
        joint_data_df.columns = [f'theta_{i+1}' for i in range(6)]
        joint_data = joint_data_df.values
        
        measured_data = pd.read_excel('../real2000.xlsx').values
        
        # 2. Calculate theoretical poses
        print("Calculating theoretical poses...")
        robot = RobotKinematics()
        theoretical_poses = []
        for joints in joint_data:
            pose = robot.forward_kinematics(joints)
            theoretical_poses.append(pose)
        theoretical_poses = np.array(theoretical_poses)
        
        # 3. Calculate errors
        errors = measured_data - theoretical_poses
        
        # Fix angle continuity
        for i in range(errors.shape[0]):
            for j in range(3, 6):
                error = errors[i, j]
                candidates = [error, error + 360, error - 360]
                errors[i, j] = min(candidates, key=abs)
        
        # 4. Data split
        test_indices = list(range(400))
        train_indices = list(range(400, 2000))
        
        X_train = joint_data[train_indices]
        X_test = joint_data[test_indices]
        y_train = errors[train_indices]
        y_test = errors[test_indices]
        
        # 5. Feature engineering
        print("Feature engineering...")
        X_train_enhanced = create_enhanced_features(X_train)
        X_test_enhanced = create_enhanced_features(X_test)
        
        # 6. Standardization
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_enhanced)
        X_test_scaled = scaler.transform(X_test_enhanced)
        
        # 7. Create data loaders
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train_scaled),
            torch.FloatTensor(y_train)
        )
        test_dataset = TensorDataset(
            torch.FloatTensor(X_test_scaled),
            torch.FloatTensor(y_test)
        )
        
        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
        
        print(f"Data prepared: Train{X_train_scaled.shape}, Test{X_test_scaled.shape}")
        
        # 8. Calculate baseline errors
        pos_errors = np.sqrt(np.sum(errors[:, :3]**2, axis=1))
        angle_errors = np.abs(errors[:, 3:])
        baseline_pos_error = np.mean(pos_errors)
        baseline_angle_error = np.median(angle_errors)
        
        print(f"Baseline errors: Position {baseline_pos_error:.6f}mm, Angle {baseline_angle_error:.6f}deg")
        
        # 9. Create and train model
        print("Training PINN model...")
        model = SimplePINN(input_dim=63, hidden_dim=128, output_dim=6)
        optimizer = optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)
        
        best_val_loss = float('inf')
        patience_counter = 0
        epochs = 100
        physics_weight = 0.1
        
        for epoch in range(epochs):
            # Training
            model.train()
            train_loss = 0.0
            
            for features, targets in train_loader:
                optimizer.zero_grad()
                
                predictions = model(features)
                
                # Weighted loss
                pos_loss = nn.MSELoss()(predictions[:, :3], targets[:, :3])
                angle_loss = nn.MSELoss()(predictions[:, 3:], targets[:, 3:])
                data_loss = 0.7 * pos_loss + 0.3 * angle_loss
                
                # Physics constraint loss
                physics_loss = model.physics_loss(predictions)
                
                total_loss = data_loss + physics_weight * physics_loss
                
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += total_loss.item()
            
            # Validation
            model.eval()
            val_loss = 0.0
            all_predictions = []
            all_targets = []
            
            with torch.no_grad():
                for features, targets in test_loader:
                    predictions = model(features)
                    
                    pos_loss = nn.MSELoss()(predictions[:, :3], targets[:, :3])
                    angle_loss = nn.MSELoss()(predictions[:, 3:], targets[:, 3:])
                    loss = 0.7 * pos_loss + 0.3 * angle_loss
                    
                    val_loss += loss.item()
                    
                    all_predictions.append(predictions.cpu().numpy())
                    all_targets.append(targets.cpu().numpy())
            
            scheduler.step(val_loss)
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
            else:
                patience_counter += 1
            
            if epoch % 20 == 0:
                predictions = np.vstack(all_predictions)
                targets = np.vstack(all_targets)
                
                residual_errors = targets - predictions
                pos_errors = np.sqrt(np.sum(residual_errors[:, :3]**2, axis=1))
                angle_errors_raw = residual_errors[:, 3:]
                
                pos_error = np.mean(pos_errors)
                angle_error = np.median(np.abs(angle_errors_raw))
                
                print(f"Epoch {epoch}: Train={train_loss/len(train_loader):.6f}, "
                      f"Val={val_loss/len(test_loader):.6f}, "
                      f"Pos={pos_error:.6f}mm, Angle={angle_error:.6f}deg")
            
            if patience_counter >= 25:
                print(f"Early stopping at epoch {epoch}")
                break
        
        # 10. Final results
        print("\n" + "="*60)
        print("Final Results")
        print("="*60)
        
        predictions = np.vstack(all_predictions)
        targets = np.vstack(all_targets)
        
        residual_errors = targets - predictions
        pos_errors = np.sqrt(np.sum(residual_errors[:, :3]**2, axis=1))
        angle_errors_raw = residual_errors[:, 3:]
        
        final_pos_error = np.mean(pos_errors)
        final_angle_error = np.median(np.abs(angle_errors_raw))
        r2 = r2_score(targets, predictions)
        
        pos_improvement = (baseline_pos_error - final_pos_error) / baseline_pos_error * 100
        angle_improvement = (baseline_angle_error - final_angle_error) / baseline_angle_error * 100
        
        print(f"Error Comparison:")
        print(f"  Original position error: {baseline_pos_error:.6f} mm")
        print(f"  Compensated position error: {final_pos_error:.6f} mm")
        print(f"  Position accuracy improvement: {pos_improvement:.2f}%")
        print()
        print(f"  Original angle error: {baseline_angle_error:.6f} deg")
        print(f"  Compensated angle error: {final_angle_error:.6f} deg")
        print(f"  Angle accuracy improvement: {angle_improvement:.2f}%")
        print()
        print(f"Model Performance:")
        print(f"  R² score: {r2:.4f}")
        print(f"  Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        print(f"\nTechnical Features:")
        print(f"  ✓ Physics-Informed Neural Networks (PINN)")
        print(f"  ✓ 63-dimensional enhanced feature engineering")
        print(f"  ✓ Weighted loss function (position 70%, angle 30%)")
        print(f"  ✓ Physics constraint regularization")
        print(f"  ✓ Early stopping and gradient clipping")
        
        # Save results
        import os
        os.makedirs("输出结果", exist_ok=True)
        
        results = {
            'baseline_pos_error': baseline_pos_error,
            'final_pos_error': final_pos_error,
            'pos_improvement': pos_improvement,
            'baseline_angle_error': baseline_angle_error,
            'final_angle_error': final_angle_error,
            'angle_improvement': angle_improvement,
            'r2_score': r2,
            'model_parameters': sum(p.numel() for p in model.parameters())
        }
        
        results_df = pd.DataFrame([results])
        results_df.to_excel('输出结果/简化PINN实验结果.xlsx', index=False)
        
        print(f"\nResults saved: 输出结果/简化PINN实验结果.xlsx")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\nExperiment completed successfully!")
    else:
        print("\nExperiment failed. Please check the error messages.")
