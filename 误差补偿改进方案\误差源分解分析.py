#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器人误差源分解分析系统
分析减速机传动误差、连杆柔性误差等各误差源的贡献

作者: AI助手  
日期: 2025年
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import signal
from scipy.fft import fft, fftfreq
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

from 理论计算模块 import RobotKinematics

class ErrorSourceDecomposition:
    """误差源分解分析系统"""
    
    def __init__(self):
        self.robot = RobotKinematics()
        
        # Staubli TX60 参数
        self.gear_ratios = [160, 160, 160, 72, 72, 72]  # 减速比
        self.link_lengths = [0.32, 0.225, 0.035, 0.225, 0, 0.065]  # 连杆长度 (m)
        self.link_masses = [15, 8, 6, 4, 2, 1]  # 连杆质量 (kg)
        
    def load_experimental_data(self):
        """加载实验数据"""
        print("=== 加载实验数据进行误差源分析 ===")
        
        # 加载数据
        joint_data_df = pd.read_excel('../theta2000.xlsx', header=None)
        joint_data_df.columns = [f'theta_{i+1}' for i in range(6)]
        self.joint_data = joint_data_df.values
        
        measured_data = pd.read_excel('../real2000.xlsx').values
        
        # 计算理论位姿和总误差
        theoretical_poses = []
        for joints in self.joint_data:
            pose = self.robot.forward_kinematics(joints)
            theoretical_poses.append(pose)
        
        theoretical_poses = np.array(theoretical_poses)
        
        # 计算总误差并修复角度连续性
        self.total_errors = measured_data - theoretical_poses
        for i in range(self.total_errors.shape[0]):
            for j in range(3, 6):
                error = self.total_errors[i, j]
                candidates = [error, error + 360, error - 360]
                self.total_errors[i, j] = min(candidates, key=abs)
        
        print(f"数据加载完成: {self.joint_data.shape[0]} 个测量点")
        
    def analyze_gear_transmission_errors(self):
        """分析减速机传动误差"""
        print("\n=== 减速机传动误差分析 ===")
        
        gear_analysis = {}
        
        for joint_idx in range(6):
            joint_angles = self.joint_data[:, joint_idx]
            
            # 1. 周期性分析
            periodicity = self.analyze_periodicity(joint_angles, self.total_errors, joint_idx)
            
            # 2. 齿轮啮合频率分析
            mesh_frequency_analysis = self.analyze_mesh_frequency(joint_angles, joint_idx)
            
            # 3. 估计减速机误差幅值
            estimated_gear_error = self.estimate_gear_error_amplitude(joint_angles, self.total_errors, joint_idx)
            
            gear_analysis[f'joint_{joint_idx+1}'] = {
                'periodicity': periodicity,
                'mesh_frequency': mesh_frequency_analysis,
                'estimated_error_amplitude': estimated_gear_error,
                'gear_ratio': self.gear_ratios[joint_idx]
            }
            
            print(f"关节 {joint_idx+1}:")
            print(f"  减速比: {self.gear_ratios[joint_idx]}")
            print(f"  估计传动误差幅值: {estimated_gear_error:.4f} mm")
            print(f"  主要周期性频率: {periodicity['dominant_frequency']:.2f}")
        
        self.gear_analysis = gear_analysis
        return gear_analysis
    
    def analyze_periodicity(self, joint_angles, total_errors, joint_idx):
        """分析角度与误差的周期性关系"""
        
        # 位置误差的周期性分析
        pos_errors = np.sqrt(np.sum(total_errors[:, :3]**2, axis=1))
        
        # 计算角度与位置误差的相关性
        angle_rad = np.deg2rad(joint_angles)
        
        # 一阶谐波相关性
        sin_corr = np.abs(np.corrcoef(np.sin(angle_rad), pos_errors)[0, 1])
        cos_corr = np.abs(np.corrcoef(np.cos(angle_rad), pos_errors)[0, 1])
        
        # 二阶谐波相关性（齿轮啮合）
        sin2_corr = np.abs(np.corrcoef(np.sin(2*angle_rad), pos_errors)[0, 1])
        cos2_corr = np.abs(np.corrcoef(np.cos(2*angle_rad), pos_errors)[0, 1])
        
        # 主导频率
        correlations = [sin_corr, cos_corr, sin2_corr, cos2_corr]
        dominant_frequency = np.argmax(correlations) + 1
        
        return {
            'sin_correlation': sin_corr,
            'cos_correlation': cos_corr,
            'sin2_correlation': sin2_corr,
            'cos2_correlation': cos2_corr,
            'dominant_frequency': dominant_frequency,
            'max_correlation': max(correlations)
        }
    
    def analyze_mesh_frequency(self, joint_angles, joint_idx):
        """分析齿轮啮合频率"""
        
        gear_ratio = self.gear_ratios[joint_idx]
        
        # FFT分析
        angle_rad = np.deg2rad(joint_angles)
        pos_errors = np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1))
        
        # 计算功率谱
        freqs = fftfreq(len(angle_rad), d=1.0)
        angle_fft = fft(angle_rad)
        error_fft = fft(pos_errors)
        
        # 寻找齿轮啮合频率的特征
        mesh_freq_theoretical = gear_ratio / (2 * np.pi)  # 理论啮合频率
        
        # 在理论频率附近寻找峰值
        freq_range = np.where((freqs > 0) & (freqs < mesh_freq_theoretical * 2))
        power_spectrum = np.abs(error_fft[freq_range])
        
        if len(power_spectrum) > 0:
            peak_idx = np.argmax(power_spectrum)
            peak_frequency = freqs[freq_range][peak_idx]
        else:
            peak_frequency = 0
        
        return {
            'theoretical_mesh_frequency': mesh_freq_theoretical,
            'detected_peak_frequency': peak_frequency,
            'frequency_match_ratio': peak_frequency / mesh_freq_theoretical if mesh_freq_theoretical > 0 else 0
        }
    
    def estimate_gear_error_amplitude(self, joint_angles, total_errors, joint_idx):
        """估计减速机误差幅值"""
        
        angle_rad = np.deg2rad(joint_angles)
        pos_errors = np.sqrt(np.sum(total_errors[:, :3]**2, axis=1))
        
        # 拟合周期性误差模型
        # E = A*sin(θ) + B*cos(θ) + C*sin(2θ) + D*cos(2θ)
        
        design_matrix = np.column_stack([
            np.sin(angle_rad),
            np.cos(angle_rad), 
            np.sin(2*angle_rad),
            np.cos(2*angle_rad),
            np.ones(len(angle_rad))  # 常数项
        ])
        
        # 最小二乘拟合
        coeffs, residuals, rank, s = np.linalg.lstsq(design_matrix, pos_errors, rcond=None)
        
        # 计算周期性误差的幅值
        A, B, C, D, E = coeffs
        amplitude_1st = np.sqrt(A**2 + B**2)  # 一阶谐波幅值
        amplitude_2nd = np.sqrt(C**2 + D**2)  # 二阶谐波幅值
        
        total_periodic_amplitude = amplitude_1st + amplitude_2nd
        
        return total_periodic_amplitude
    
    def analyze_link_flexibility_errors(self):
        """分析连杆柔性误差"""
        print("\n=== 连杆柔性误差分析 ===")
        
        flexibility_analysis = {}
        
        for i, joints in enumerate(self.joint_data):
            # 计算重力载荷
            gravity_loads = self.calculate_gravity_loads(joints)
            
            # 估计柔性变形
            flexibility_deformation = self.estimate_flexibility_deformation(joints, gravity_loads)
            
            if i == 0:  # 只显示第一个点的详细信息
                print(f"示例分析 (第1个测量点):")
                print(f"  关节角度: {joints}")
                print(f"  重力载荷: {gravity_loads}")
                print(f"  估计柔性变形: {flexibility_deformation}")
        
        # 统计分析
        all_gravity_loads = []
        all_flexibility_errors = []
        
        for joints in self.joint_data:
            gravity_loads = self.calculate_gravity_loads(joints)
            flexibility_error = self.estimate_flexibility_deformation(joints, gravity_loads)
            
            all_gravity_loads.append(gravity_loads)
            all_flexibility_errors.append(flexibility_error)
        
        all_gravity_loads = np.array(all_gravity_loads)
        all_flexibility_errors = np.array(all_flexibility_errors)
        
        # 计算柔性误差的统计特征
        flexibility_analysis = {
            'mean_gravity_loads': np.mean(all_gravity_loads, axis=0),
            'std_gravity_loads': np.std(all_gravity_loads, axis=0),
            'mean_flexibility_errors': np.mean(all_flexibility_errors, axis=0),
            'std_flexibility_errors': np.std(all_flexibility_errors, axis=0),
            'max_flexibility_errors': np.max(np.abs(all_flexibility_errors), axis=0),
            'load_error_correlation': [np.corrcoef(all_gravity_loads[:, i], all_flexibility_errors[:, i])[0,1] 
                                     for i in range(6)]
        }
        
        print(f"\n柔性误差统计:")
        print(f"  平均柔性误差: {flexibility_analysis['mean_flexibility_errors']}")
        print(f"  最大柔性误差: {flexibility_analysis['max_flexibility_errors']}")
        print(f"  载荷-误差相关性: {flexibility_analysis['load_error_correlation']}")
        
        self.flexibility_analysis = flexibility_analysis
        return flexibility_analysis
    
    def calculate_gravity_loads(self, joint_angles):
        """计算重力载荷"""
        
        # 简化的重力载荷计算
        angles_rad = np.deg2rad(joint_angles)
        gravity_loads = np.zeros(6)
        
        # 重力加速度
        g = 9.81  # m/s²
        
        # 计算各关节的重力矩（简化模型）
        for i in range(6):
            # 累积质量（该关节及后续关节的质量）
            cumulative_mass = sum(self.link_masses[i:])
            
            # 重力臂（简化计算）
            if i < 3:  # 前三个关节主要受重力影响
                gravity_arm = self.link_lengths[i] * np.cos(angles_rad[i])
                gravity_loads[i] = cumulative_mass * g * gravity_arm
            else:  # 后三个关节重力影响较小
                gravity_loads[i] = cumulative_mass * g * 0.1
        
        return gravity_loads
    
    def estimate_flexibility_deformation(self, joint_angles, gravity_loads):
        """估计柔性变形"""
        
        # 简化的柔性模型：变形 = 载荷 / 刚度
        # 连杆刚度（经验值，单位：N⋅m/rad）
        link_stiffness = [1e6, 8e5, 6e5, 2e5, 1e5, 5e4]
        
        # 计算角度变形
        angular_deformation = gravity_loads / link_stiffness
        
        # 转换为末端位姿误差（简化）
        # 这里使用简化的雅可比矩阵近似
        position_error = np.sum(angular_deformation[:3]) * 0.5  # mm
        orientation_error = np.sum(angular_deformation[3:]) * 0.1  # 度
        
        return np.array([position_error, position_error*0.5, position_error*0.3, 
                        orientation_error, orientation_error*0.8, orientation_error*0.6])
    
    def decompose_total_errors(self):
        """分解总误差为各误差源"""
        print("\n=== 误差源分解 ===")
        
        # 估计各误差源的贡献
        estimated_gear_errors = np.zeros_like(self.total_errors)
        estimated_flexibility_errors = np.zeros_like(self.total_errors)
        
        for i, joints in enumerate(self.joint_data):
            # 减速机误差估计
            for j in range(6):
                angle_rad = np.deg2rad(joints[j])
                gear_error_amplitude = self.gear_analysis[f'joint_{j+1}']['estimated_error_amplitude']
                
                # 周期性误差模型
                gear_error = gear_error_amplitude * (np.sin(angle_rad) + 0.3*np.sin(2*angle_rad))
                estimated_gear_errors[i, :3] += gear_error / 6  # 分配到位置误差
            
            # 柔性误差估计
            gravity_loads = self.calculate_gravity_loads(joints)
            flexibility_error = self.estimate_flexibility_deformation(joints, gravity_loads)
            estimated_flexibility_errors[i] = flexibility_error
        
        # 几何误差（剩余误差）
        estimated_geometric_errors = (self.total_errors - 
                                    estimated_gear_errors - 
                                    estimated_flexibility_errors)
        
        # 计算各误差源的贡献比例
        total_error_magnitude = np.mean(np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1)))
        gear_error_magnitude = np.mean(np.sqrt(np.sum(estimated_gear_errors[:, :3]**2, axis=1)))
        flexibility_error_magnitude = np.mean(np.sqrt(np.sum(estimated_flexibility_errors[:, :3]**2, axis=1)))
        geometric_error_magnitude = np.mean(np.sqrt(np.sum(estimated_geometric_errors[:, :3]**2, axis=1)))
        
        error_decomposition = {
            'total_error': total_error_magnitude,
            'gear_error': gear_error_magnitude,
            'flexibility_error': flexibility_error_magnitude,
            'geometric_error': geometric_error_magnitude,
            'gear_contribution': gear_error_magnitude / total_error_magnitude * 100,
            'flexibility_contribution': flexibility_error_magnitude / total_error_magnitude * 100,
            'geometric_contribution': geometric_error_magnitude / total_error_magnitude * 100
        }
        
        print(f"误差源分解结果:")
        print(f"  总误差: {total_error_magnitude:.4f} mm")
        print(f"  减速机传动误差: {gear_error_magnitude:.4f} mm ({error_decomposition['gear_contribution']:.1f}%)")
        print(f"  连杆柔性误差: {flexibility_error_magnitude:.4f} mm ({error_decomposition['flexibility_contribution']:.1f}%)")
        print(f"  几何误差: {geometric_error_magnitude:.4f} mm ({error_decomposition['geometric_contribution']:.1f}%)")
        
        self.error_decomposition = error_decomposition
        return error_decomposition
    
    def create_error_source_visualizations(self):
        """创建误差源分析可视化"""
        print("\n=== 生成误差源分析图表 ===")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. 减速机误差周期性分析
        for i in range(6):
            row = i // 3
            col = i % 3
            
            joint_angles = self.joint_data[:, i]
            pos_errors = np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1))
            
            axes[row, col].scatter(joint_angles, pos_errors, alpha=0.6, s=1)
            axes[row, col].set_xlabel(f'关节{i+1}角度 (度)')
            axes[row, col].set_ylabel('位置误差 (mm)')
            axes[row, col].set_title(f'关节{i+1}角度vs位置误差')
            axes[row, col].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('输出结果/误差源周期性分析.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 2. 误差源贡献饼图
        plt.figure(figsize=(10, 8))
        
        labels = ['减速机传动误差', '连杆柔性误差', '几何误差']
        sizes = [
            self.error_decomposition['gear_contribution'],
            self.error_decomposition['flexibility_contribution'], 
            self.error_decomposition['geometric_contribution']
        ]
        colors = ['#ff9999', '#66b3ff', '#99ff99']
        
        plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        plt.title('机器人误差源贡献分析', fontsize=16, fontweight='bold')
        plt.axis('equal')
        
        plt.savefig('输出结果/误差源贡献分析.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 误差源分析图表已生成")
    
    def run_complete_analysis(self):
        """运行完整的误差源分析"""
        print("="*80)
        print("🔍 机器人误差源分解分析系统")
        print("="*80)
        
        # 1. 加载数据
        self.load_experimental_data()
        
        # 2. 分析减速机传动误差
        self.analyze_gear_transmission_errors()
        
        # 3. 分析连杆柔性误差
        self.analyze_link_flexibility_errors()
        
        # 4. 分解总误差
        self.decompose_total_errors()
        
        # 5. 生成可视化
        self.create_error_source_visualizations()
        
        print("\n✅ 误差源分解分析完成!")
        return True

def main():
    """主函数"""
    import os
    os.makedirs("输出结果", exist_ok=True)
    
    # 创建误差源分解分析系统
    analyzer = ErrorSourceDecomposition()
    
    # 运行完整分析
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
