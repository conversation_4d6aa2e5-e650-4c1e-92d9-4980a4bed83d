#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
误差分离表格生成系统
生成包含原始数据和各种误差理论值的完整表格

作者: AI助手
日期: 2025年
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

from 理论计算模块 import RobotKinematics

class ErrorSeparationTable:
    """误差分离表格生成器"""
    
    def __init__(self):
        self.robot = RobotKinematics()
        
        # Staubli TX60 参数
        self.gear_ratios = [160, 160, 160, 72, 72, 72]
        self.link_masses = [15.0, 8.0, 6.0, 4.0, 2.0, 1.0]  # kg
        self.link_lengths = [0.32, 0.225, 0.035, 0.225, 0.0, 0.065]  # m
        
        # 基于文献的误差参数
        self.gear_error_params = {
            'amplitude_1st': [0.015, 0.012, 0.010, 0.008, 0.006, 0.004],  # 一阶谐波 (度)
            'amplitude_2nd': [0.008, 0.006, 0.005, 0.004, 0.003, 0.002],  # 二阶谐波 (度)
            'phase_1st': [0.2, 0.3, 0.1, 0.4, 0.2, 0.1],  # 一阶相位 (弧度)
            'phase_2nd': [0.1, 0.2, 0.3, 0.1, 0.4, 0.2],  # 二阶相位 (弧度)
            'backlash': [0.02, 0.015, 0.012, 0.010, 0.008, 0.006]  # 间隙 (度)
        }
        
        # 连杆刚度参数 (基于材料力学估算)
        self.link_stiffness = [2e6, 1.5e6, 1.2e6, 8e5, 6e5, 4e5]  # N⋅m/rad
        
    def load_original_data(self):
        """加载原始数据"""
        print("=== 加载原始数据 ===")
        
        # 加载关节角度
        theta_df = pd.read_excel('../theta2000.xlsx', header=None)
        theta_df.columns = [f'theta_{i+1}' for i in range(6)]
        self.theta_command = theta_df.values
        
        # 加载实测位姿
        real_df = pd.read_excel('../real2000.xlsx')
        self.real_poses = real_df.values
        
        # 计算理论位姿
        self.theoretical_poses = []
        for joints in self.theta_command:
            pose = self.robot.forward_kinematics(joints)
            self.theoretical_poses.append(pose)
        self.theoretical_poses = np.array(self.theoretical_poses)
        
        # 计算总误差
        self.total_errors = self.real_poses - self.theoretical_poses
        
        # 修复角度连续性
        for i in range(self.total_errors.shape[0]):
            for j in range(3, 6):
                error = self.total_errors[i, j]
                candidates = [error, error + 360, error - 360]
                self.total_errors[i, j] = min(candidates, key=abs)
        
        print(f"数据加载完成: {self.theta_command.shape[0]} 个数据点")
        
    def calculate_gear_transmission_errors(self):
        """计算减速机传动误差（基于理论模型）"""
        print("=== 计算减速机传动误差 ===")
        
        self.gear_errors_joint = np.zeros_like(self.theta_command)  # 关节空间误差
        self.gear_errors_cartesian = np.zeros_like(self.total_errors)  # 笛卡尔空间误差
        
        for i, joint_angles in enumerate(self.theta_command):
            
            # 计算各关节的传动误差
            joint_gear_errors = np.zeros(6)
            
            for j in range(6):
                theta = joint_angles[j]
                theta_rad = np.deg2rad(theta)
                
                # 周期性误差建模
                A1 = self.gear_error_params['amplitude_1st'][j]
                A2 = self.gear_error_params['amplitude_2nd'][j]
                phi1 = self.gear_error_params['phase_1st'][j]
                phi2 = self.gear_error_params['phase_2nd'][j]
                
                # 一阶和二阶谐波
                harmonic_1 = A1 * np.sin(theta_rad + phi1)
                harmonic_2 = A2 * np.sin(2 * theta_rad + phi2)
                
                # 间隙误差（简化模型）
                if i > 0:
                    angle_diff = joint_angles[j] - self.theta_command[i-1, j]
                    backlash = self.gear_error_params['backlash'][j] * np.tanh(angle_diff * 5)
                else:
                    backlash = 0
                
                # 总传动误差
                joint_gear_errors[j] = harmonic_1 + harmonic_2 + backlash
            
            self.gear_errors_joint[i] = joint_gear_errors
            
            # 转换为笛卡尔空间误差
            jacobian = self.calculate_jacobian_numerical(joint_angles)
            gear_errors_rad = np.deg2rad(joint_gear_errors)
            cartesian_error = jacobian @ gear_errors_rad
            self.gear_errors_cartesian[i] = cartesian_error
        
        print("减速机传动误差计算完成")
        
    def calculate_link_flexibility_errors(self):
        """计算连杆柔性误差（基于重力载荷）"""
        print("=== 计算连杆柔性误差 ===")
        
        self.flexibility_errors_joint = np.zeros_like(self.theta_command)  # 关节空间误差
        self.flexibility_errors_cartesian = np.zeros_like(self.total_errors)  # 笛卡尔空间误差
        
        for i, joint_angles in enumerate(self.theta_command):
            
            # 计算重力载荷
            gravity_torques = self.calculate_gravity_torques(joint_angles)
            
            # 计算角度变形
            angular_deformation = gravity_torques / self.link_stiffness
            self.flexibility_errors_joint[i] = np.rad2deg(angular_deformation)
            
            # 转换为笛卡尔空间误差
            jacobian = self.calculate_jacobian_numerical(joint_angles)
            cartesian_error = jacobian @ angular_deformation
            self.flexibility_errors_cartesian[i] = cartesian_error
        
        print("连杆柔性误差计算完成")
        
    def calculate_gravity_torques(self, joint_angles):
        """计算重力载荷"""
        angles_rad = np.deg2rad(joint_angles)
        gravity_torques = np.zeros(6)
        g = 9.81
        
        # 简化的重力矩计算
        for i in range(6):
            for j in range(i, 6):
                if i <= 2:  # 前三个关节主要受重力影响
                    if j == i:
                        gravity_arm = self.link_lengths[j] * 0.5 * np.cos(angles_rad[i])
                    else:
                        gravity_arm = self.link_lengths[j] * np.cos(sum(angles_rad[i:j+1]))
                else:
                    gravity_arm = self.link_lengths[j] * 0.1
                
                gravity_torques[i] += self.link_masses[j] * g * gravity_arm
        
        return gravity_torques
    
    def calculate_jacobian_numerical(self, joint_angles):
        """数值计算雅可比矩阵"""
        jacobian = np.zeros((6, 6))
        delta = 1e-6
        
        current_pose = self.robot.forward_kinematics(joint_angles)
        
        for i in range(6):
            perturbed_angles = joint_angles.copy()
            perturbed_angles[i] += delta
            perturbed_pose = self.robot.forward_kinematics(perturbed_angles)
            jacobian[:, i] = (perturbed_pose - current_pose) / delta
        
        return jacobian
    
    def calculate_thermal_errors(self):
        """计算热误差（基于简化模型）"""
        print("=== 计算热误差 ===")
        
        self.thermal_errors = np.zeros_like(self.total_errors)
        
        # 简化的热误差模型：假设与运动量相关
        for i in range(len(self.theta_command)):
            # 计算累积运动量（简化的热效应指标）
            if i > 0:
                motion_intensity = np.sum(np.abs(self.theta_command[i] - self.theta_command[i-1]))
            else:
                motion_intensity = 0
            
            # 热膨胀系数（简化）
            thermal_coeff = 1e-5  # mm/度/运动强度
            
            # 热误差主要影响位置
            thermal_effect = thermal_coeff * motion_intensity * np.array([1.0, 0.8, 0.6, 0.1, 0.1, 0.1])
            self.thermal_errors[i] = thermal_effect
        
        print("热误差计算完成")
    
    def calculate_geometric_errors(self):
        """计算几何误差（剩余误差）"""
        print("=== 计算几何误差 ===")
        
        # 几何误差 = 总误差 - 传动误差 - 柔性误差 - 热误差
        self.geometric_errors = (self.total_errors - 
                               self.gear_errors_cartesian - 
                               self.flexibility_errors_cartesian - 
                               self.thermal_errors)
        
        print("几何误差计算完成")
    
    def calculate_error_statistics(self):
        """计算误差统计"""
        print("=== 计算误差统计 ===")
        
        # 位置误差统计
        total_pos_error = np.mean(np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1)))
        gear_pos_error = np.mean(np.sqrt(np.sum(self.gear_errors_cartesian[:, :3]**2, axis=1)))
        flex_pos_error = np.mean(np.sqrt(np.sum(self.flexibility_errors_cartesian[:, :3]**2, axis=1)))
        thermal_pos_error = np.mean(np.sqrt(np.sum(self.thermal_errors[:, :3]**2, axis=1)))
        geom_pos_error = np.mean(np.sqrt(np.sum(self.geometric_errors[:, :3]**2, axis=1)))
        
        # 角度误差统计
        total_angle_error = np.median(np.abs(self.total_errors[:, 3:]))
        gear_angle_error = np.median(np.abs(self.gear_errors_cartesian[:, 3:]))
        flex_angle_error = np.median(np.abs(self.flexibility_errors_cartesian[:, 3:]))
        thermal_angle_error = np.median(np.abs(self.thermal_errors[:, 3:]))
        geom_angle_error = np.median(np.abs(self.geometric_errors[:, 3:]))
        
        self.error_statistics = {
            'total_pos_error': total_pos_error,
            'gear_pos_error': gear_pos_error,
            'flexibility_pos_error': flex_pos_error,
            'thermal_pos_error': thermal_pos_error,
            'geometric_pos_error': geom_pos_error,
            'total_angle_error': total_angle_error,
            'gear_angle_error': gear_angle_error,
            'flexibility_angle_error': flex_angle_error,
            'thermal_angle_error': thermal_angle_error,
            'geometric_angle_error': geom_angle_error
        }
        
        print(f"误差统计:")
        print(f"  总位置误差: {total_pos_error:.6f} mm")
        print(f"  减速机位置误差: {gear_pos_error:.6f} mm ({gear_pos_error/total_pos_error*100:.1f}%)")
        print(f"  柔性位置误差: {flex_pos_error:.6f} mm ({flex_pos_error/total_pos_error*100:.1f}%)")
        print(f"  热位置误差: {thermal_pos_error:.6f} mm ({thermal_pos_error/total_pos_error*100:.1f}%)")
        print(f"  几何位置误差: {geom_pos_error:.6f} mm ({geom_pos_error/total_pos_error*100:.1f}%)")
    
    def generate_complete_table(self):
        """生成完整的误差分离表格"""
        print("=== 生成完整误差分离表格 ===")
        
        # 创建完整的数据表格
        data_dict = {}
        
        # 前12列：原始数据
        for i in range(6):
            data_dict[f'theta_{i+1}_cmd'] = self.theta_command[:, i]
        
        for i in range(6):
            if i < 3:
                data_dict[f'real_pos_{["X", "Y", "Z"][i]}'] = self.real_poses[:, i]
            else:
                data_dict[f'real_ori_{["Rx", "Ry", "Rz"][i-3]}'] = self.real_poses[:, i]
        
        # 理论位姿
        for i in range(6):
            if i < 3:
                data_dict[f'theo_pos_{["X", "Y", "Z"][i]}'] = self.theoretical_poses[:, i]
            else:
                data_dict[f'theo_ori_{["Rx", "Ry", "Rz"][i-3]}'] = self.theoretical_poses[:, i]
        
        # 总误差
        for i in range(6):
            if i < 3:
                data_dict[f'total_err_{["X", "Y", "Z"][i]}'] = self.total_errors[:, i]
            else:
                data_dict[f'total_err_{["Rx", "Ry", "Rz"][i-3]}'] = self.total_errors[:, i]
        
        # 减速机传动误差（关节空间）
        for i in range(6):
            data_dict[f'gear_err_joint_{i+1}'] = self.gear_errors_joint[:, i]
        
        # 减速机传动误差（笛卡尔空间）
        for i in range(6):
            if i < 3:
                data_dict[f'gear_err_{["X", "Y", "Z"][i]}'] = self.gear_errors_cartesian[:, i]
            else:
                data_dict[f'gear_err_{["Rx", "Ry", "Rz"][i-3]}'] = self.gear_errors_cartesian[:, i]
        
        # 连杆柔性误差（关节空间）
        for i in range(6):
            data_dict[f'flex_err_joint_{i+1}'] = self.flexibility_errors_joint[:, i]
        
        # 连杆柔性误差（笛卡尔空间）
        for i in range(6):
            if i < 3:
                data_dict[f'flex_err_{["X", "Y", "Z"][i]}'] = self.flexibility_errors_cartesian[:, i]
            else:
                data_dict[f'flex_err_{["Rx", "Ry", "Rz"][i-3]}'] = self.flexibility_errors_cartesian[:, i]
        
        # 热误差
        for i in range(6):
            if i < 3:
                data_dict[f'thermal_err_{["X", "Y", "Z"][i]}'] = self.thermal_errors[:, i]
            else:
                data_dict[f'thermal_err_{["Rx", "Ry", "Rz"][i-3]}'] = self.thermal_errors[:, i]
        
        # 几何误差
        for i in range(6):
            if i < 3:
                data_dict[f'geom_err_{["X", "Y", "Z"][i]}'] = self.geometric_errors[:, i]
            else:
                data_dict[f'geom_err_{["Rx", "Ry", "Rz"][i-3]}'] = self.geometric_errors[:, i]
        
        # 综合误差指标
        data_dict['total_pos_error'] = np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1))
        data_dict['total_angle_error'] = np.sqrt(np.sum(self.total_errors[:, 3:]**2, axis=1))
        data_dict['gear_pos_error'] = np.sqrt(np.sum(self.gear_errors_cartesian[:, :3]**2, axis=1))
        data_dict['gear_angle_error'] = np.sqrt(np.sum(self.gear_errors_cartesian[:, 3:]**2, axis=1))
        data_dict['flex_pos_error'] = np.sqrt(np.sum(self.flexibility_errors_cartesian[:, :3]**2, axis=1))
        data_dict['flex_angle_error'] = np.sqrt(np.sum(self.flexibility_errors_cartesian[:, 3:]**2, axis=1))
        
        # 创建DataFrame
        self.complete_table = pd.DataFrame(data_dict)
        
        # 保存为Excel文件
        self.complete_table.to_excel('输出结果/完整误差分离表格.xlsx', index=False)
        
        # 保存前100行作为示例
        sample_table = self.complete_table.head(100)
        sample_table.to_excel('输出结果/误差分离表格_示例100行.xlsx', index=False)
        
        print(f"✅ 完整误差分离表格已生成:")
        print(f"  - 完整表格: 输出结果/完整误差分离表格.xlsx ({self.complete_table.shape[0]} 行 × {self.complete_table.shape[1]} 列)")
        print(f"  - 示例表格: 输出结果/误差分离表格_示例100行.xlsx (100 行 × {sample_table.shape[1]} 列)")
        
        return self.complete_table
    
    def create_summary_table(self):
        """创建误差统计汇总表"""
        print("=== 创建误差统计汇总表 ===")
        
        summary_data = {
            '误差类型': ['总误差', '减速机传动误差', '连杆柔性误差', '热误差', '几何误差'],
            '位置误差(mm)': [
                self.error_statistics['total_pos_error'],
                self.error_statistics['gear_pos_error'],
                self.error_statistics['flexibility_pos_error'],
                self.error_statistics['thermal_pos_error'],
                self.error_statistics['geometric_pos_error']
            ],
            '角度误差(度)': [
                self.error_statistics['total_angle_error'],
                self.error_statistics['gear_angle_error'],
                self.error_statistics['flexibility_angle_error'],
                self.error_statistics['thermal_angle_error'],
                self.error_statistics['geometric_angle_error']
            ]
        }
        
        # 计算贡献百分比
        total_pos = self.error_statistics['total_pos_error']
        total_angle = self.error_statistics['total_angle_error']
        
        summary_data['位置贡献(%)'] = [
            100.0,
            self.error_statistics['gear_pos_error'] / total_pos * 100,
            self.error_statistics['flexibility_pos_error'] / total_pos * 100,
            self.error_statistics['thermal_pos_error'] / total_pos * 100,
            self.error_statistics['geometric_pos_error'] / total_pos * 100
        ]
        
        summary_data['角度贡献(%)'] = [
            100.0,
            self.error_statistics['gear_angle_error'] / total_angle * 100,
            self.error_statistics['flexibility_angle_error'] / total_angle * 100,
            self.error_statistics['thermal_angle_error'] / total_angle * 100,
            self.error_statistics['geometric_angle_error'] / total_angle * 100
        ]
        
        summary_table = pd.DataFrame(summary_data)
        summary_table.to_excel('输出结果/误差统计汇总表.xlsx', index=False)
        
        print("✅ 误差统计汇总表已生成: 输出结果/误差统计汇总表.xlsx")
        print("\n误差贡献分析:")
        for i, error_type in enumerate(summary_data['误差类型']):
            if i > 0:  # 跳过总误差
                print(f"  {error_type}: 位置{summary_data['位置贡献(%)'][i]:.1f}%, 角度{summary_data['角度贡献(%)'][i]:.1f}%")
        
        return summary_table
    
    def run_complete_analysis(self):
        """运行完整的误差分离分析"""
        print("="*80)
        print("📊 机器人误差分离表格生成系统")
        print("="*80)
        
        # 1. 加载原始数据
        self.load_original_data()
        
        # 2. 计算各种误差
        self.calculate_gear_transmission_errors()
        self.calculate_link_flexibility_errors()
        self.calculate_thermal_errors()
        self.calculate_geometric_errors()
        
        # 3. 计算统计
        self.calculate_error_statistics()
        
        # 4. 生成完整表格
        complete_table = self.generate_complete_table()
        
        # 5. 生成汇总表
        summary_table = self.create_summary_table()
        
        print("\n✅ 误差分离分析完成!")
        print("📁 生成的文件:")
        print("  - 输出结果/完整误差分离表格.xlsx")
        print("  - 输出结果/误差分离表格_示例100行.xlsx")
        print("  - 输出结果/误差统计汇总表.xlsx")
        
        return complete_table, summary_table

def main():
    """主函数"""
    import os
    os.makedirs("输出结果", exist_ok=True)
    
    # 创建误差分离表格生成器
    generator = ErrorSeparationTable()
    
    # 运行完整分析
    complete_table, summary_table = generator.run_complete_analysis()
    
    # 显示表格信息
    print(f"\n📋 表格结构:")
    print(f"完整表格形状: {complete_table.shape}")
    print(f"列名示例: {list(complete_table.columns[:15])}...")

if __name__ == "__main__":
    main()
