#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度位姿预测对比实验 - 完整流程
整合理论计算、机器学习预测、三方对比分析
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 导入各个模块
from 理论计算模块 import RobotKinematics, load_and_calculate_theoretical_poses
from 机器学习预测模块 import MLPosePredictor, generate_ml_predictions
from 三方对比分析模块 import ThreeWayComparison

class CompleteExperiment:
    """完整实验流程管理器"""
    
    def __init__(self):
        self.experiment_name = "高精度位姿预测对比实验"
        self.results = {}
        
    def setup_experiment(self):
        """实验环境设置"""
        print("="*80)
        print(f"{self.experiment_name}")
        print("="*80)
        print("实验目标:")
        print("1. 基于机器人运动学理论计算位姿")
        print("2. 使用机器学习方法预测位姿")
        print("3. 与激光跟踪仪实测值进行三方对比")
        print("4. 分析各方法的精度和误差特性")
        print("-"*80)
        
        # 检查数据文件
        required_files = ['../theta2000.xlsx', '../real2000.xlsx']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print("❌ 缺少必要的数据文件:")
            for file in missing_files:
                print(f"   - {file}")
            return False
        
        print("✅ 数据文件检查完成")
        return True
    
    def run_theoretical_calculation(self):
        """运行理论计算"""
        print("\n" + "="*50)
        print("步骤 1: 理论位姿计算")
        print("="*50)
        
        try:
            # 运行理论计算
            theoretical_df, joint_data = load_and_calculate_theoretical_poses()
            
            self.results['theoretical'] = theoretical_df
            self.results['joint_data'] = joint_data
            
            print("✅ 理论计算完成")
            return True
            
        except Exception as e:
            print(f"❌ 理论计算失败: {str(e)}")
            return False
    
    def run_ml_prediction(self):
        """运行机器学习预测"""
        print("\n" + "="*50)
        print("步骤 2: 机器学习位姿预测")
        print("="*50)
        
        try:
            # 运行机器学习预测
            predicted_df, predictor = generate_ml_predictions()
            
            self.results['predicted'] = predicted_df
            self.results['predictor'] = predictor
            
            print("✅ 机器学习预测完成")
            return True
            
        except Exception as e:
            print(f"❌ 机器学习预测失败: {str(e)}")
            return False
    
    def run_comparison_analysis(self):
        """运行三方对比分析"""
        print("\n" + "="*50)
        print("步骤 3: 三方对比分析")
        print("="*50)
        
        try:
            # 创建分析器
            analyzer = ThreeWayComparison()
            
            # 加载数据
            if not analyzer.load_all_data():
                print("❌ 数据加载失败")
                return False
            
            # 执行分析
            analyzer.calculate_errors()
            analyzer.calculate_statistics()
            
            # 生成结果
            summary_df = analyzer.generate_summary_table()
            analyzer.plot_error_comparison()
            analyzer.plot_accuracy_comparison()
            showcase_df = analyzer.create_sample_showcase(10)
            analyzer.generate_final_report()
            
            self.results['analyzer'] = analyzer
            self.results['summary'] = summary_df
            self.results['showcase'] = showcase_df
            
            print("✅ 三方对比分析完成")
            return True
            
        except Exception as e:
            print(f"❌ 三方对比分析失败: {str(e)}")
            return False
    
    def generate_executive_summary(self):
        """生成执行摘要"""
        print("\n" + "="*50)
        print("步骤 4: 生成执行摘要")
        print("="*50)
        
        if 'summary' not in self.results:
            print("❌ 缺少分析结果，无法生成摘要")
            return False
        
        summary_df = self.results['summary']
        
        print("\n🎯 实验结果摘要:")
        print("-"*30)
        
        # 统计最优方法
        ml_wins = len(summary_df[summary_df['最优方法'] == '预测'])
        theory_wins = len(summary_df[summary_df['最优方法'] == '理论'])
        
        print(f"📊 精度对比结果:")
        print(f"   机器学习优于理论计算: {ml_wins}/6 个坐标")
        print(f"   理论计算优于机器学习: {theory_wins}/6 个坐标")
        
        # 显示各坐标的最佳方法
        print(f"\n📈 各坐标最优方法:")
        for _, row in summary_df.iterrows():
            coord = row['坐标']
            best_method = row['最优方法']
            if best_method == '预测':
                rmse = row['预测RMSE']
                level = row['预测精度等级']
            else:
                rmse = row['理论RMSE']
                level = row['理论精度等级']
            
            print(f"   {coord}: {best_method} (RMSE: {rmse}, {level})")
        
        # 关键发现
        print(f"\n🔍 关键发现:")
        
        # 位置坐标分析
        position_coords = ['X', 'Y', 'Z']
        position_summary = summary_df[summary_df['坐标'].isin(position_coords)]
        position_ml_wins = len(position_summary[position_summary['最优方法'] == '预测'])
        
        if position_ml_wins >= 2:
            print("   ✅ 机器学习在位置坐标预测上表现优异")
        else:
            print("   ⚠️ 理论计算在位置坐标上更准确")
        
        # 姿态角度分析
        angle_coords = ['Rx', 'Ry', 'Rz']
        angle_summary = summary_df[summary_df['坐标'].isin(angle_coords)]
        angle_ml_wins = len(angle_summary[angle_summary['最优方法'] == '预测'])
        
        if angle_ml_wins >= 2:
            print("   ✅ 机器学习在姿态角度预测上表现优异")
        else:
            print("   ⚠️ 理论计算在姿态角度上更准确")
        
        # 应用建议
        print(f"\n💡 应用建议:")
        
        best_overall = '机器学习' if ml_wins > theory_wins else '理论计算'
        if ml_wins > theory_wins:
            print("   🚀 推荐使用机器学习方法进行位姿预测")
            print("   📝 建议在实际应用中部署训练好的模型")
        elif theory_wins > ml_wins:
            print("   🔧 推荐使用理论计算方法进行位姿预测")
            print("   📝 建议优化DH参数以提高理论计算精度")
        else:
            print("   ⚖️ 两种方法各有优势，建议混合使用")
            print("   📝 可根据具体坐标选择最优方法")
        
        return True
    
    def save_complete_results(self):
        """保存完整实验结果"""
        print("\n" + "="*50)
        print("步骤 5: 保存完整结果")
        print("="*50)
        
        # 创建完整结果汇总
        complete_results = {
            '实验名称': self.experiment_name,
            '数据样本数': len(self.results['joint_data']) if 'joint_data' in self.results else 0,
            '对比方法数': 3,  # 理论、实测、预测
            '分析坐标数': 6,  # X,Y,Z,Rx,Ry,Rz
        }
        
        # 保存实验配置
        config_df = pd.DataFrame([complete_results])
        config_df.to_excel('实验配置信息.xlsx', index=False)
        
        print("✅ 完整实验结果已保存")
        
        # 列出所有生成的文件
        generated_files = [
            '理论位姿计算结果.xlsx',
            '机器学习预测结果.xlsx', 
            '三方对比汇总表.xlsx',
            '样本展示数据.xlsx',
            '误差对比分析图.png',
            '精度对比雷达图.png',
            '三方对比分析报告.md',
            '实验配置信息.xlsx'
        ]
        
        print("\n📁 生成的文件列表:")
        for i, file in enumerate(generated_files, 1):
            if os.path.exists(file):
                print(f"   {i}. ✅ {file}")
            else:
                print(f"   {i}. ❌ {file} (未生成)")
        
        return True
    
    def run_complete_experiment(self):
        """运行完整实验流程"""
        print("开始运行完整实验流程...\n")
        
        # 步骤1: 实验设置
        if not self.setup_experiment():
            print("❌ 实验设置失败，终止实验")
            return False
        
        # 步骤2: 理论计算
        if not self.run_theoretical_calculation():
            print("❌ 理论计算失败，终止实验")
            return False
        
        # 步骤3: 机器学习预测
        if not self.run_ml_prediction():
            print("❌ 机器学习预测失败，终止实验")
            return False
        
        # 步骤4: 三方对比分析
        if not self.run_comparison_analysis():
            print("❌ 三方对比分析失败，终止实验")
            return False
        
        # 步骤5: 生成执行摘要
        if not self.generate_executive_summary():
            print("❌ 执行摘要生成失败")
            return False
        
        # 步骤6: 保存完整结果
        if not self.save_complete_results():
            print("❌ 结果保存失败")
            return False
        
        print("\n" + "="*80)
        print("🎉 完整实验流程执行成功！")
        print("="*80)
        print("实验已完成，所有结果文件已生成。")
        print("请查看生成的Excel文件、图表和分析报告。")
        
        return True

def main():
    """主函数"""
    # 创建实验管理器
    experiment = CompleteExperiment()
    
    # 运行完整实验
    success = experiment.run_complete_experiment()
    
    if success:
        print("\n✅ 实验成功完成！")
        print("📊 请查看生成的分析报告和图表")
        print("📁 所有结果文件已保存在当前目录")
    else:
        print("\n❌ 实验执行失败！")
        print("🔧 请检查错误信息并重新运行")

if __name__ == "__main__":
    main()
