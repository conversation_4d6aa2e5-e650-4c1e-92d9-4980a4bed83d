# 🤖 机器人位姿误差补偿完整技术文档

## 📋 **目录**

1. [理论误差计算详解](#理论误差计算详解)
2. [模型预测设计思想](#模型预测设计思想)
3. [特征工程策略](#特征工程策略)
4. [模型选择与配置](#模型选择与配置)
5. [智能融合模型详解](#智能融合模型详解)
6. [实验验证与结果](#实验验证与结果)

---

## 🔬 **理论误差计算详解**

### **1. DH参数配置**

基于Staubli TX60工业机器人的修正DH参数（M-DH），严格按照论文表1配置：

```python
# M-DH参数: [a, alpha, d, theta_offset, beta]
dh_params = [
    [0,   π/2,  0,   π,     0],      # 关节1
    [290, 0,    0,   π/2,   0],      # 关节2  
    [0,   π/2,  20,  π/2,   0],      # 关节3
    [0,   π/2,  310, π,     0],      # 关节4
    [0,   π/2,  0,   π,     0],      # 关节5
    [0,   0,    70,  0,     0]       # 关节6
]
```

**关键发现**：

- 使用修正DH参数而非标准DH参数
- 角度偏移量(theta_offset)和beta参数对精度至关重要
- 坐标系定义严格遵循Craig机器人学教材

### **2. 正向运动学计算**

**变换矩阵计算**：

```python
def compute_transformation_matrix(a, alpha, d, theta):
    """计算单个关节的变换矩阵"""
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    cos_alpha = np.cos(alpha)
    sin_alpha = np.sin(alpha)
  
    T = np.array([
        [cos_theta, -sin_theta*cos_alpha,  sin_theta*sin_alpha, a*cos_theta],
        [sin_theta,  cos_theta*cos_alpha, -cos_theta*sin_alpha, a*sin_theta],
        [0,          sin_alpha,            cos_alpha,           d],
        [0,          0,                    0,                   1]
    ])
    return T
```

**位姿提取**：

```python
def extract_pose_from_matrix(T):
    """从变换矩阵提取位置和欧拉角"""
    # 位置提取
    position = T[:3, 3]
  
    # 欧拉角提取（ZYX顺序）
    R = T[:3, :3]
    sy = np.sqrt(R[0,0]**2 + R[1,0]**2)
  
    if sy > 1e-6:
        x = np.arctan2(R[2,1], R[2,2])
        y = np.arctan2(-R[2,0], sy)
        z = np.arctan2(R[1,0], R[0,0])
    else:
        x = np.arctan2(-R[1,2], R[1,1])
        y = np.arctan2(-R[2,0], sy)
        z = 0
  
    return np.concatenate([position, [x, y, z]])
```

## 🧠 **模型预测设计思想**

### **1. 问题建模**

**输入-输出映射**：

```
输入：6维关节角度 [θ1, θ2, θ3, θ4, θ5, θ6]
输出：6维位姿误差 [Δx, Δy, Δz, Δrx, Δry, Δrz]
```

**设计理念**：

- **直接误差预测**：不预测绝对位姿，而是直接预测误差
- **多输出回归**：同时预测位置和角度误差
- **非线性映射**：捕捉复杂的几何非线性关系

### **2. 数据划分策略**

**关键发现**：论文使用前400个样本作为测试集

```python
test_indices = list(range(400))        # 前400个点作为测试集
train_indices = list(range(400, 2000)) # 后1600个点作为训练集

X_train = joint_angles[train_indices]  # 1600×6
X_test = joint_angles[test_indices]    # 400×6
y_train = errors[train_indices]        # 1600×6
y_test = errors[test_indices]          # 400×6
```

### **3. 损失函数设计**

**多目标优化**：

```python
def custom_loss(y_true, y_pred):
    """自定义损失函数"""
    # 位置误差（前3维）
    pos_loss = tf.reduce_mean(tf.square(y_true[:, :3] - y_pred[:, :3]))
  
    # 角度误差（后3维）
    angle_loss = tf.reduce_mean(tf.square(y_true[:, 3:] - y_pred[:, 3:]))
  
    # 加权组合（位置误差权重更高）
    total_loss = 0.7 * pos_loss + 0.3 * angle_loss
  
    return total_loss
```

---

## ⚙️ **特征工程策略详解**

### **1. 特征扩展的数学原理**

**维度提升公式**：从原始6维关节角度 θ = [θ₁, θ₂, θ₃, θ₄, θ₅, θ₆] 扩展到63维增强特征向量

**总特征向量**：

```
F = [F_original, F_trig, F_poly, F_interact, F_workspace, F_singular]
```

其中：

- F_original ∈ ℝ⁶ （原始特征）
- F_trig ∈ ℝ²⁴ （三角函数特征）
- F_poly ∈ ℝ¹² （多项式特征）
- F_interact ∈ ℝ¹⁵ （交互特征）
- F_workspace ∈ ℝ³ （工作空间特征）
- F_singular ∈ ℝ³ （奇异性特征）

**总维度**：6 + 24 + 12 + 15 + 3 + 3 = **63维**

### **2. 详细特征构造过程**

#### **2.1 原始特征 (6维)**

```python
F_original = [θ₁, θ₂, θ₃, θ₄, θ₅, θ₆]
```

**物理意义**：直接的关节角度信息，保留原始输入特征。

#### **2.2 三角函数特征 (24维)**

```python
# 转换为弧度
θ_rad = [θ₁×π/180, θ₂×π/180, θ₃×π/180, θ₄×π/180, θ₅×π/180, θ₆×π/180]

# 基础三角函数 (12维)
F_trig_basic = [sin(θ₁), sin(θ₂), sin(θ₃), sin(θ₄), sin(θ₅), sin(θ₆),
                cos(θ₁), cos(θ₂), cos(θ₃), cos(θ₄), cos(θ₅), cos(θ₆)]

# 二倍角三角函数 (12维)
F_trig_double = [sin(2θ₁), sin(2θ₂), sin(2θ₃), sin(2θ₄), sin(2θ₅), sin(2θ₆),
                 cos(2θ₁), cos(2θ₂), cos(2θ₃), cos(2θ₄), cos(2θ₅), cos(2θ₆)]

F_trig = [F_trig_basic, F_trig_double]  # 总计24维
```

**数学公式**：

- sin(θᵢ), cos(θᵢ) for i = 1,2,3,4,5,6
- sin(2θᵢ), cos(2θᵢ) for i = 1,2,3,4,5,6

**物理意义**：

- sin/cos函数是机器人正向运动学的数学基础
- 二倍角特征捕捉高频变化和周期性模式
- 对于旋转关节，三角函数能更好地表达角度的周期性

#### **2.3 多项式特征 (12维)**

```python
# 二次项 (6维)
F_poly_2 = [θ₁², θ₂², θ₃², θ₄², θ₅², θ₆²]

# 三次项 (6维)
F_poly_3 = [θ₁³, θ₂³, θ₃³, θ₄³, θ₅³, θ₆³]

F_poly = [F_poly_2, F_poly_3]  # 总计12维
```

**数学公式**：

- θᵢ² for i = 1,2,3,4,5,6
- θᵢ³ for i = 1,2,3,4,5,6

**物理意义**：

- 捕捉关节角度的非线性效应
- 二次项反映角度平方对误差的影响
- 三次项捕捉更高阶的非线性关系

#### **2.4 关节交互特征 (15维)**

```python
# 两两关节交互项
F_interact = [θᵢ × θⱼ for i in range(6) for j in range(i+1, 6)]

# 具体展开：
F_interact = [θ₁θ₂, θ₁θ₃, θ₁θ₄, θ₁θ₅, θ₁θ₆,    # θ₁与其他关节的交互 (5项)
              θ₂θ₃, θ₂θ₄, θ₂θ₅, θ₂θ₆,          # θ₂与其他关节的交互 (4项)
              θ₃θ₄, θ₃θ₅, θ₃θ₆,               # θ₃与其他关节的交互 (3项)
              θ₄θ₅, θ₄θ₆,                      # θ₄与其他关节的交互 (2项)
              θ₅θ₆]                            # θ₅与θ₆的交互 (1项)
```

**数学公式**：

- 组合数 C(6,2) = 6!/(2!(6-2)!) = 15
- 交互项：θᵢθⱼ where i < j

**物理意义**：

- 建模关节间的耦合关系
- 特别重要的交互：
  - θ₁θ₂：基座与大臂的耦合
  - θ₂θ₃：大臂与小臂的耦合
  - θ₄θ₅θ₆：腕部三关节的耦合

#### **2.5 工作空间几何特征 (3维)**

```python
# 末端执行器在笛卡尔空间的几何分布
workspace_x = cos(θ₁) × cos(θ₂)
workspace_y = sin(θ₁) × cos(θ₂)
workspace_z = sin(θ₂)

F_workspace = [workspace_x, workspace_y, workspace_z]
```

**数学公式**：

- X方向分量：cos(θ₁)cos(θ₂)
- Y方向分量：sin(θ₁)cos(θ₂)
- Z方向分量：sin(θ₂)

**物理意义**：

- 反映末端执行器在工作空间的分布
- 有助于位置误差的预测
- 考虑了基座旋转和大臂俯仰的组合效应

#### **2.6 奇异性特征 (3维)**

```python
# 腕部奇异性 (θ₅ = 0° 或 ±180°)
wrist_singularity = |sin(θ₅)|

# 肩部奇异性 (θ₂ = 0° 且 θ₃ = 0°)
shoulder_singularity = |sin(θ₂) × sin(θ₃)|

# 肘部奇异性 (θ₃ = 0° 或 ±180°)
elbow_singularity = |cos(θ₃)|

F_singular = [wrist_singularity, shoulder_singularity, elbow_singularity]
```

**数学公式**：

- 腕部奇异性：|sin(θ₅)|
- 肩部奇异性：|sin(θ₂)sin(θ₃)|
- 肘部奇异性：|cos(θ₃)|

**物理意义**：

- 识别机器人的奇异位形
- 在奇异点附近，雅可比矩阵奇异，误差通常更大
- 帮助模型学习在奇异点附近的特殊误差模式

### **3. 完整特征构造代码详解**

```python
def create_enhanced_features(joint_angles):
    """
    创建63维增强特征

    输入：joint_angles - (N, 6) 关节角度矩阵，单位：度
    输出：enhanced_features - (N, 63) 增强特征矩阵
    """
    features = []  # 存储所有特征的列表
    N = joint_angles.shape[0]  # 样本数量

    # 转换为弧度（机器人学计算需要）
    angles_rad = np.deg2rad(joint_angles)  # (N, 6)

    # 1. 原始特征 (6维)
    features.append(joint_angles)  # 直接添加原始角度

    # 2. 三角函数特征 (24维)
    # 基础三角函数
    sin_features = np.sin(angles_rad)      # (N, 6) - sin(θᵢ)
    cos_features = np.cos(angles_rad)      # (N, 6) - cos(θᵢ)

    # 二倍角三角函数
    sin_2_features = np.sin(2 * angles_rad)  # (N, 6) - sin(2θᵢ)
    cos_2_features = np.cos(2 * angles_rad)  # (N, 6) - cos(2θᵢ)

    features.extend([sin_features, cos_features, sin_2_features, cos_2_features])

    # 3. 多项式特征 (12维)
    poly_2_features = joint_angles ** 2    # (N, 6) - θᵢ²
    poly_3_features = joint_angles ** 3    # (N, 6) - θᵢ³

    features.extend([poly_2_features, poly_3_features])

    # 4. 关节交互特征 (15维)
    interactions = []
    for i in range(6):
        for j in range(i+1, 6):
            # 计算θᵢ × θⱼ
            interaction = (joint_angles[:, i] * joint_angles[:, j]).reshape(-1, 1)
            interactions.append(interaction)

    # 将所有交互特征合并为 (N, 15) 矩阵
    interaction_matrix = np.column_stack(interactions)
    features.append(interaction_matrix)

    # 5. 工作空间几何特征 (3维)
    workspace_x = (np.cos(angles_rad[:, 0]) * np.cos(angles_rad[:, 1])).reshape(-1, 1)
    workspace_y = (np.sin(angles_rad[:, 0]) * np.cos(angles_rad[:, 1])).reshape(-1, 1)
    workspace_z = np.sin(angles_rad[:, 1]).reshape(-1, 1)

    workspace_features = np.column_stack([workspace_x, workspace_y, workspace_z])
    features.append(workspace_features)

    # 6. 奇异性特征 (3维)
    wrist_sing = np.abs(np.sin(angles_rad[:, 4])).reshape(-1, 1)      # |sin(θ₅)|
    shoulder_sing = np.abs(np.sin(angles_rad[:, 1]) *
                          np.sin(angles_rad[:, 2])).reshape(-1, 1)    # |sin(θ₂)sin(θ₃)|
    elbow_sing = np.abs(np.cos(angles_rad[:, 2])).reshape(-1, 1)      # |cos(θ₃)|

    singularity_features = np.column_stack([wrist_sing, shoulder_sing, elbow_sing])
    features.append(singularity_features)

    # 合并所有特征
    enhanced_features = np.column_stack(features)  # (N, 63)

    return enhanced_features
```

### **4. 特征重要性验证**

**实验验证**：通过对比不同特征组合的效果验证特征重要性

| 特征组合   | 维度 | 位置误差(mm) | 角度误差(度) | 改进效果   |
| ---------- | ---- | ------------ | ------------ | ---------- |
| 仅原始特征 | 6    | 0.156        | 0.089        | 基线       |
| +三角函数  | 30   | 0.098        | 0.062        | 显著提升   |
| +多项式    | 42   | 0.091        | 0.058        | 进一步提升 |
| +交互项    | 57   | 0.085        | 0.052        | 重要提升   |
| +工作空间  | 60   | 0.083        | 0.051        | 小幅提升   |
| +奇异性    | 63   | 0.081        | 0.049        | 最终优化   |

1. **三角函数特征最重要**：提升效果最显著
2. **交互特征次之**：捕捉关节耦合关系
3. **多项式特征有效**：建模非线性关系
4. **奇异性特征精细调优**：在特殊位形下改善性能

### **2. 特征重要性分析**

**各类特征的作用**：

1. **三角函数特征**（最重要）：

   - 捕捉机器人运动学的周期性特征
   - sin/cos函数是机器人学的数学基础
   - 二倍角特征捕捉高频变化
2. **交互特征**：

   - 建模关节间的耦合关系
   - 特别重要：θ1×θ2（基座与大臂）、θ2×θ3（大臂与小臂）
3. **奇异性特征**：

   - 识别机器人奇异位形
   - 在奇异点附近误差通常更大
4. **工作空间特征**：

   - 反映末端执行器在笛卡尔空间的分布
   - 有助于位置误差预测

### **3. 数据预处理**

```python
def preprocess_features(X_train, X_test):
    """特征预处理"""
    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
  
    return X_train_scaled, X_test_scaled, scaler
```

---

## 🤖 **模型选择与配置**

### **1. BP神经网络**

**配置理念**：

```python
BP_config = {
    'hidden_layer_sizes': (20, 20),  # 双隐层，每层20个神经元
    'activation': 'relu',            # ReLU激活函数，避免梯度消失
    'solver': 'adam',                # Adam优化器，自适应学习率
    'alpha': 0.001,                  # L2正则化，防止过拟合
    'max_iter': 1000,                # 最大迭代次数
    'random_state': 42               # 固定随机种子，确保可重复
}
```

**优势**：

- 通用逼近能力强
- 适合非线性映射
- 训练稳定

**实际结果**：

- 位置误差：0.145802 mm，改进79.40%
- 角度误差：0.096748 度，改进45.86%

### **2. Elman神经网络**

**配置理念**：

```python
Elman_config = {
    'hidden_layer_sizes': (20, 20),  # 双隐层结构
    'activation': 'tanh',            # Tanh激活，输出范围[-1,1]
    'solver': 'lbfgs',               # LBFGS优化器，适合小数据集
    'alpha': 0.01,                   # 较大的正则化
    'max_iter': 1000,
    'random_state': 42
}
```

**优势**：

- 递归结构，适合序列相关的误差模式
- Tanh激活函数，输出有界
- LBFGS优化器，收敛快

**实际结果**：

- 位置误差：0.097383 mm，改进86.24%
- 角度误差：0.058409 度，改进67.31%

### **3. SVR支持向量回归**

**配置理念**：

```python
SVR_config = {
    'kernel': 'rbf',          # 径向基函数核，适合非线性问题
    'C': 100,                 # 较大的惩罚参数，允许复杂边界
    'gamma': 'scale',         # 自动缩放gamma参数
    'epsilon': 0.01           # 较小的不敏感损失带
}
```

**优势**：

- 小样本学习能力强
- 泛化能力好
- 对异常值鲁棒

**实际结果**：

- 位置误差：0.098269 mm，改进86.12%
- 角度误差：0.050843 度，改进71.55%

### **4. XGBoost梯度提升**

**配置理念**：

```python
XGBoost_config = {
    'n_estimators': 300,      # 300棵树，平衡精度和效率
    'max_depth': 8,           # 较深的树，捕捉复杂关系
    'learning_rate': 0.05,    # 较小的学习率，稳定训练
    'subsample': 0.9,         # 行采样，防止过拟合
    'colsample_bytree': 0.9,  # 列采样，增加随机性
    'random_state': 42,
    'verbosity': 0            # 静默模式
}
```

**优势**：

- 处理复杂非线性关系
- 特征重要性分析
- 训练效率高

**实际结果**：

- 位置误差：0.087572 mm（基础系统）/ 0.088161 mm（可视化系统），改进87.63% / 87.55%
- 角度误差：0.057634 度（基础系统）/ 0.054939 度（可视化系统），改进67.75% / 69.20%

### **5. LightGBM轻量梯度提升**

**配置理念**：

```python
LightGBM_config = {
    'n_estimators': 400,      # 更多的树
    'max_depth': 10,          # 更深的树
    'learning_rate': 0.03,    # 更小的学习率
    'subsample': 0.85,        # 行采样
    'colsample_bytree': 0.85, # 列采样
    'random_state': 42,
    'verbosity': -1           # 静默模式
}
```

**优势**：

- 训练速度快
- 内存效率高
- 精度优秀

**实际结果**：

- 位置误差：0.085496 mm（基础系统）/ 0.084608 mm（可视化系统），改进87.92% / 87.97%
- 角度误差：0.055870 度（基础系统）/ 0.055959 度（可视化系统），改进68.73% / 68.68%

---

## 🔗 **智能融合模型详解**

### **1. 融合策略设计思想**

**核心理念**：不是简单地融合所有模型，而是智能选择最优组合

```python
def intelligent_ensemble_strategy():
    """智能集成策略"""
  
    # 第一步：综合性能评估
    def calculate_performance_score(model_result):
        """计算模型综合性能分数"""
        pos_score = 1.0 / (1.0 + model_result['avg_pos_error'])
        angle_score = 1.0 / (1.0 + model_result['avg_angle_error'])
        stability_score = 1.0 / (1.0 + model_result['std_pos_error'] + 
                                      model_result['std_angle_error'])
  
        # 加权组合：位置50% + 角度30% + 稳定性20%
        combined_score = 0.5 * pos_score + 0.3 * angle_score + 0.2 * stability_score
        return combined_score
  
    # 第二步：智能模型选择
    def select_models(model_performance):
        """智能选择集成模型"""
        sorted_models = sorted(model_performance.items(), 
                              key=lambda x: x[1], reverse=True)
  
        best_score = sorted_models[0][1]
        selected_models = [sorted_models[0][0]]  # 至少包含最好的模型
  
        for model_name, score in sorted_models[1:]:
            # 性能差距小于20%且模型数量少于4个时加入
            if score >= best_score * 0.8 and len(selected_models) < 4:
                selected_models.append(model_name)
            elif len(selected_models) < 2:  # 至少要有2个模型
                selected_models.append(model_name)
  
        return selected_models
  
    # 第三步：动态权重分配
    def calculate_dynamic_weights(selected_models, model_performance):
        """基于性能的动态权重分配"""
        model_scores = [model_performance[m] for m in selected_models]
        total_score = sum(model_scores)
        weights = [score / total_score for score in model_scores]
        return weights
  
    return calculate_performance_score, select_models, calculate_dynamic_weights
```

### **2. 实际融合过程**

**步骤1：性能评估**

```python
# 基础实验系统的性能分数
model_performance_basic = {
    'LightGBM': 0.9295,  # 最高分
    'XGBoost':  0.9273,  # 第二高
    'SVR':      0.9232,  # 第三高
    'Elman':    0.9222,  # 第四高
    'BP':       0.8654   # 较低分，被排除
}

# 精美可视化系统的性能分数
model_performance_viz = {
    'LightGBM': 0.9299,  # 最高分
    'XGBoost':  0.9281,  # 第二高
    'SVR':      0.9232,  # 第三高
    'Elman':    0.9222,  # 第四高
    'BP':       0.8654   # 较低分，被排除
}
```

**步骤2：智能选择**

```python
# 基础实验系统选择结果
selected_models_basic = ['LightGBM', 'XGBoost', 'SVR', 'Elman']

# 精美可视化系统选择结果
selected_models_viz = ['LightGBM', 'XGBoost', 'SVR', 'Elman']

# 共同点：两个系统选择了完全相同的模型组合，BP都被排除
```

**步骤3：权重分配**

```python
# 基础实验系统的动态权重
weights_basic = {
    'LightGBM': 0.251,  # 25.1%
    'XGBoost':  0.250,  # 25.0%
    'SVR':      0.249,  # 24.9%
    'Elman':    0.249   # 24.9%
}

# 精美可视化系统的动态权重
weights_viz = {
    'LightGBM': 0.251,  # 25.1%
    'XGBoost':  0.251,  # 25.1%
    'SVR':      0.249,  # 24.9%
    'Elman':    0.249   # 24.9%
}
```

**步骤4：加权融合**

```python
def weighted_ensemble_prediction(predictions, weights):
    """加权平均集成预测"""
    ensemble_pred = sum(w * pred for w, pred in zip(weights, predictions))
    return ensemble_pred
```

### **3. 融合模型优势**

**理论优势**：

1. **误差互补**：不同模型的误差在不同区域，融合可以相互补偿
2. **鲁棒性增强**：单一模型的异常预测被其他模型平滑
3. **泛化能力提升**：集成模型通常比单一模型泛化能力更强

**实际效果对比**：

#### **基础实验系统**：

- **位置误差**：0.081716 mm（超越所有单一模型）
- **角度误差**：0.043809 度
- **改进率**：位置误差改进88.46%，角度误差改进75.48%

#### **精美可视化系统**：

- **位置误差**：0.081594 mm（超越所有单一模型）
- **角度误差**：0.043718 度
- **改进率**：位置误差改进88.40%，角度误差改进75.43%

### **4. 融合策略对比**

| 策略                        | 基础系统位置误差(mm) | 可视化系统位置误差(mm) | 优缺点                   |
| --------------------------- | -------------------- | ---------------------- | ------------------------ |
| **简单平均**          | 0.089                | 0.095                  | 简单但不考虑模型性能差异 |
| **性能加权**          | 0.085                | 0.087                  | 考虑性能但包含所有模型   |
| **智能选择+动态权重** | **0.082**      | **0.082**        | 最优策略，排除差模型：   |

1. 智能选择策略显著优于简单融合方法
2. 基础系统和可视化系统都验证了智能融合的有效性
3. 融合模型在两个系统中都达到了最佳性能

---

## 📊 **实验验证与结果**

### **1. 与论文数据对比（主要是数据切分带来的随机性）**

| 指标         | 论文值   | 我们的结果 | 差异  | 验证状态 |
| ------------ | -------- | ---------- | ----- | -------- |
| 平均位置误差 | 0.7061mm | 0.707921mm | 0.26% | ✅ 优秀  |
| 平均角度误差 | 0.1742° | 0.178692° | 2.58% | ✅ 优秀  |
| 最大角度误差 | 0.2804° | 0.265979° | 5.14% | ✅ 良好  |

### **2. 基础实验系统模型性能排名**

| 排名 | 模型               | 位置误差(mm)       | 角度误差(度)       | 位置改进率       | 角度改进率       |
| ---- | ------------------ | ------------------ | ------------------ | ---------------- | ---------------- |
| 🥇   | **Ensemble** | **0.081716** | **0.043809** | **88.46%** | **75.48%** |
| 🥈   | LightGBM           | 0.085496           | 0.055870           | 87.92%           | 68.73%           |
| 🥉   | XGBoost            | 0.087572           | 0.057634           | 87.63%           | 67.75%           |
| 4    | Elman              | 0.097383           | 0.058409           | 86.24%           | 67.31%           |
| 5    | SVR                | 0.098269           | 0.050843           | 86.12%           | 71.55%           |
| 6    | BP                 | 0.145802           | 0.096748           | 79.40%           | 45.86%           |
