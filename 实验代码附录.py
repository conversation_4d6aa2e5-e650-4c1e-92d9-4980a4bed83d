#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器人位姿预测实验 - 完整代码
实验目的：基于6个关节角度预测机器人末端执行器的6个位姿参数
作者：[学生姓名]
日期：2025-06-18
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class RobotPosePredictor:
    """机器人位姿预测器"""
    
    def __init__(self):
        self.models = {}
        self.results = {}
        self.scaler_X = StandardScaler()
        
    def load_data(self):
        """
        数据加载函数
        重要：theta2000.xlsx没有表头，需要按无表头方式读取
        """
        print("=== 数据加载 ===")
        
        # 加载目标变量 (有表头)
        self.y_data = pd.read_excel('real2000.xlsx')
        print(f"目标变量形状: {self.y_data.shape}")
        print(f"目标变量列名: {self.y_data.columns.tolist()}")
        
        # 加载特征变量 (无表头，第一行是数据)
        self.X_data = pd.read_excel('theta2000.xlsx', header=None)
        self.X_data.columns = [f'theta_{i+1}' for i in range(6)]
        print(f"特征变量形状: {self.X_data.shape}")
        
        # 验证数据完整性
        print(f"特征缺失值: {self.X_data.isnull().sum().sum()}")
        print(f"目标缺失值: {self.y_data.isnull().sum().sum()}")
        
        return self.X_data, self.y_data
    
    def explore_data(self):
        """数据探索性分析"""
        print("\n=== 数据探索 ===")
        
        # 基本统计信息
        print("特征变量统计:")
        print(self.X_data.describe())
        print("\n目标变量统计:")
        print(self.y_data.describe())
        
        # 可视化数据分布
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 特征相关性
        sns.heatmap(self.X_data.corr(), annot=True, cmap='coolwarm', 
                   center=0, ax=axes[0,0])
        axes[0,0].set_title('特征变量相关性矩阵')
        
        # 目标相关性
        sns.heatmap(self.y_data.corr(), annot=True, cmap='coolwarm', 
                   center=0, ax=axes[0,1])
        axes[0,1].set_title('目标变量相关性矩阵')
        
        # 特征分布
        self.X_data.hist(bins=30, alpha=0.7, ax=axes[1,0])
        axes[1,0].set_title('特征变量分布')
        
        # 目标分布  
        self.y_data.hist(bins=30, alpha=0.7, ax=axes[1,1])
        axes[1,1].set_title('目标变量分布')
        
        plt.tight_layout()
        plt.savefig('数据探索分析.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def prepare_data(self):
        """数据预处理"""
        print("\n=== 数据预处理 ===")
        
        # 分割训练集和测试集
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            self.X_data, self.y_data, test_size=0.2, random_state=42
        )
        
        # 标准化特征（用于SVM和神经网络）
        self.X_train_scaled = self.scaler_X.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler_X.transform(self.X_test)
        
        print(f"训练集形状: X={self.X_train.shape}, y={self.y_train.shape}")
        print(f"测试集形状: X={self.X_test.shape}, y={self.y_test.shape}")
    
    def initialize_models(self):
        """初始化机器学习模型"""
        print("\n=== 模型初始化 ===")
        
        self.models = {
            '线性回归': LinearRegression(),
            '岭回归': Ridge(alpha=1.0),
            'Lasso回归': Lasso(alpha=1.0),
            '随机森林': RandomForestRegressor(n_estimators=100, random_state=42),
            '梯度提升': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'XGBoost': xgb.XGBRegressor(n_estimators=100, random_state=42),
            'LightGBM': lgb.LGBMRegressor(n_estimators=100, random_state=42, verbose=-1),
            'SVM': SVR(kernel='rbf'),
            '神经网络': MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=500, random_state=42)
        }
        
        print(f"初始化了 {len(self.models)} 个模型")
    
    def train_and_evaluate(self):
        """训练和评估所有模型"""
        print("\n=== 模型训练与评估 ===")
        
        self.results = {}
        target_names = self.y_data.columns.tolist()
        
        # 为每个目标变量分别训练模型
        for target_idx, target_name in enumerate(target_names):
            print(f"\n正在预测: {target_name}")
            print("-" * 50)
            
            y_train_single = self.y_train.iloc[:, target_idx]
            y_test_single = self.y_test.iloc[:, target_idx]
            
            target_results = {}
            
            for model_name, model in self.models.items():
                try:
                    from sklearn.base import clone
                    model_copy = clone(model)
                    
                    # 选择合适的数据
                    if model_name in ['SVM', '神经网络']:
                        X_train_use = self.X_train_scaled
                        X_test_use = self.X_test_scaled
                    else:
                        X_train_use = self.X_train
                        X_test_use = self.X_test
                    
                    # 训练模型
                    model_copy.fit(X_train_use, y_train_single)
                    y_pred = model_copy.predict(X_test_use)
                    
                    # 计算评估指标
                    r2 = r2_score(y_test_single, y_pred)
                    rmse = np.sqrt(mean_squared_error(y_test_single, y_pred))
                    mae = mean_absolute_error(y_test_single, y_pred)
                    
                    # 交叉验证
                    cv_scores = cross_val_score(model_copy, X_train_use, y_train_single, 
                                              cv=5, scoring='r2')
                    
                    target_results[model_name] = {
                        'r2': r2,
                        'rmse': rmse,
                        'mae': mae,
                        'cv_mean': cv_scores.mean(),
                        'cv_std': cv_scores.std()
                    }
                    
                    print(f"  {model_name}: R²={r2:.4f}, RMSE={rmse:.2f}")
                    
                except Exception as e:
                    print(f"  {model_name}: 训练失败 - {str(e)}")
            
            self.results[target_name] = target_results
    
    def generate_report(self):
        """生成实验报告"""
        print("\n" + "="*80)
        print("实验结果报告")
        print("="*80)
        
        # 为每个目标变量生成报告
        all_reports = {}
        
        for target_name, target_results in self.results.items():
            print(f"\n目标变量: {target_name}")
            print("-" * 50)
            
            # 创建结果DataFrame
            report_data = []
            for model_name, result in target_results.items():
                report_data.append({
                    '模型': model_name,
                    'R²得分': result['r2'],
                    'RMSE': result['rmse'],
                    'MAE': result['mae'],
                    '交叉验证R²': result['cv_mean'],
                    '交叉验证标准差': result['cv_std']
                })
            
            df_report = pd.DataFrame(report_data)
            df_report = df_report.sort_values('R²得分', ascending=False)
            
            print(df_report.to_string(index=False, float_format='%.4f'))
            all_reports[target_name] = df_report
        
        # 保存详细报告
        with pd.ExcelWriter('实验结果报告.xlsx', engine='openpyxl') as writer:
            for target_name, df_report in all_reports.items():
                sheet_name = target_name.replace('/', '_').replace(':', '_')[:31]
                df_report.to_excel(writer, sheet_name=sheet_name, index=False)
        
        return all_reports
    
    def plot_results(self):
        """绘制结果图表"""
        print("\n生成结果图表...")
        
        # 准备数据
        models = list(self.models.keys())
        targets = list(self.results.keys())
        
        # 创建R²得分矩阵
        r2_matrix = np.zeros((len(models), len(targets)))
        
        for i, model_name in enumerate(models):
            for j, target_name in enumerate(targets):
                if model_name in self.results[target_name]:
                    r2_matrix[i, j] = self.results[target_name][model_name]['r2']
                else:
                    r2_matrix[i, j] = np.nan
        
        # 绘制热力图
        plt.figure(figsize=(12, 8))
        sns.heatmap(r2_matrix, annot=True, fmt='.3f', cmap='RdYlBu_r',
                   xticklabels=targets, yticklabels=models)
        plt.title('各模型在不同目标变量上的R²得分')
        plt.xlabel('目标变量')
        plt.ylabel('模型')
        plt.tight_layout()
        plt.savefig('模型性能对比.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """主函数 - 完整实验流程"""
    print("机器人位姿预测实验开始")
    print("="*50)
    
    # 创建预测器
    predictor = RobotPosePredictor()
    
    # 1. 数据加载
    X, y = predictor.load_data()
    
    # 2. 数据探索
    predictor.explore_data()
    
    # 3. 数据预处理
    predictor.prepare_data()
    
    # 4. 模型初始化
    predictor.initialize_models()
    
    # 5. 训练和评估
    predictor.train_and_evaluate()
    
    # 6. 生成报告
    reports = predictor.generate_report()
    
    # 7. 绘制结果
    predictor.plot_results()
    
    print("\n实验完成！")
    print("生成文件:")
    print("- 实验结果报告.xlsx")
    print("- 数据探索分析.png") 
    print("- 模型性能对比.png")

if __name__ == "__main__":
    main()
