#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的PPT文件生成器
使用python-pptx库创建可编辑的PowerPoint文件

作者: 朱昕鋆
日期: 2025年7月12日
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import os

class MLMethodologyPPTGenerator:
    """机器学习方法论PPT生成器"""
    
    def __init__(self):
        self.prs = Presentation()
        self.output_dir = "输出结果"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 颜色方案
        self.colors = {
            'primary': RGBColor(46, 134, 171),      # 主色调-蓝色
            'secondary': RGBColor(162, 59, 114),    # 次色调-紫色
            'accent': RGBColor(241, 143, 1),        # 强调色-橙色
            'success': RGBColor(199, 62, 29),       # 成功色-红色
            'text': RGBColor(44, 62, 80),           # 文字色
            'light': RGBColor(236, 240, 241)        # 浅色
        }
    
    def add_title_slide(self):
        """添加标题页"""
        slide_layout = self.prs.slide_layouts[0]  # 标题布局
        slide = self.prs.slides.add_slide(slide_layout)
        
        title = slide.shapes.title
        subtitle = slide.placeholders[1]
        
        title.text = "机器学习解决工程问题的方法论"
        subtitle.text = "以机器人误差补偿为例\n\n朱昕鋆\n2025年7月12日"
        
        # 设置标题格式
        title_font = title.text_frame.paragraphs[0].font
        title_font.size = Pt(44)
        title_font.color.rgb = self.colors['primary']
        title_font.bold = True
        
        # 设置副标题格式
        subtitle_font = subtitle.text_frame.paragraphs[0].font
        subtitle_font.size = Pt(24)
        subtitle_font.color.rgb = self.colors['secondary']
    
    def add_content_slide(self, title, content_items, subtitle=""):
        """添加内容页"""
        slide_layout = self.prs.slide_layouts[1]  # 标题和内容布局
        slide = self.prs.slides.add_slide(slide_layout)
        
        # 设置标题
        title_shape = slide.shapes.title
        title_shape.text = title
        title_font = title_shape.text_frame.paragraphs[0].font
        title_font.size = Pt(36)
        title_font.color.rgb = self.colors['primary']
        title_font.bold = True
        
        # 添加副标题
        if subtitle:
            subtitle_box = slide.shapes.add_textbox(
                Inches(1), Inches(1.5), Inches(8), Inches(0.5)
            )
            subtitle_frame = subtitle_box.text_frame
            subtitle_para = subtitle_frame.paragraphs[0]
            subtitle_para.text = subtitle
            subtitle_para.font.size = Pt(20)
            subtitle_para.font.color.rgb = self.colors['secondary']
            subtitle_para.font.italic = True
        
        # 添加内容
        content_box = slide.shapes.add_textbox(
            Inches(1), Inches(2.2), Inches(8), Inches(5)
        )
        content_frame = content_box.text_frame
        content_frame.clear()
        
        for i, item in enumerate(content_items):
            if i > 0:
                content_frame.add_paragraph()
            
            para = content_frame.paragraphs[i]
            para.text = f"• {item}"
            para.font.size = Pt(18)
            para.font.color.rgb = self.colors['text']
            para.space_after = Pt(12)
    
    def add_two_column_slide(self, title, left_content, right_content, left_title="", right_title=""):
        """添加两列布局页面"""
        slide_layout = self.prs.slide_layouts[6]  # 空白布局
        slide = self.prs.slides.add_slide(slide_layout)
        
        # 标题
        title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(9), Inches(1))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = title
        title_para.font.size = Pt(36)
        title_para.font.color.rgb = self.colors['primary']
        title_para.font.bold = True
        title_para.alignment = PP_ALIGN.CENTER
        
        # 左列标题
        if left_title:
            left_title_box = slide.shapes.add_textbox(Inches(0.5), Inches(1.5), Inches(4), Inches(0.5))
            left_title_frame = left_title_box.text_frame
            left_title_para = left_title_frame.paragraphs[0]
            left_title_para.text = left_title
            left_title_para.font.size = Pt(24)
            left_title_para.font.color.rgb = self.colors['secondary']
            left_title_para.font.bold = True
        
        # 右列标题
        if right_title:
            right_title_box = slide.shapes.add_textbox(Inches(5.5), Inches(1.5), Inches(4), Inches(0.5))
            right_title_frame = right_title_box.text_frame
            right_title_para = right_title_frame.paragraphs[0]
            right_title_para.text = right_title
            right_title_para.font.size = Pt(24)
            right_title_para.font.color.rgb = self.colors['secondary']
            right_title_para.font.bold = True
        
        # 左列内容
        left_box = slide.shapes.add_textbox(Inches(0.5), Inches(2.2), Inches(4), Inches(5))
        left_frame = left_box.text_frame
        left_frame.clear()
        
        for i, item in enumerate(left_content):
            if i > 0:
                left_frame.add_paragraph()
            para = left_frame.paragraphs[i]
            para.text = f"• {item}"
            para.font.size = Pt(16)
            para.font.color.rgb = self.colors['text']
            para.space_after = Pt(8)
        
        # 右列内容
        right_box = slide.shapes.add_textbox(Inches(5.5), Inches(2.2), Inches(4), Inches(5))
        right_frame = right_box.text_frame
        right_frame.clear()
        
        for i, item in enumerate(right_content):
            if i > 0:
                right_frame.add_paragraph()
            para = right_frame.paragraphs[i]
            para.text = f"• {item}"
            para.font.size = Pt(16)
            para.font.color.rgb = self.colors['text']
            para.space_after = Pt(8)
    
    def add_process_slide(self, title, steps, subtitle=""):
        """添加流程图页面"""
        slide_layout = self.prs.slide_layouts[6]  # 空白布局
        slide = self.prs.slides.add_slide(slide_layout)
        
        # 标题
        title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(9), Inches(1))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = title
        title_para.font.size = Pt(36)
        title_para.font.color.rgb = self.colors['primary']
        title_para.font.bold = True
        title_para.alignment = PP_ALIGN.CENTER
        
        # 副标题
        if subtitle:
            subtitle_box = slide.shapes.add_textbox(Inches(0.5), Inches(1.2), Inches(9), Inches(0.5))
            subtitle_frame = subtitle_box.text_frame
            subtitle_para = subtitle_frame.paragraphs[0]
            subtitle_para.text = subtitle
            subtitle_para.font.size = Pt(20)
            subtitle_para.font.color.rgb = self.colors['secondary']
            subtitle_para.font.italic = True
            subtitle_para.alignment = PP_ALIGN.CENTER
        
        # 流程步骤
        start_y = 2.5
        step_height = 0.8
        step_width = 8
        
        for i, (step_title, step_desc) in enumerate(steps):
            y_pos = start_y + i * (step_height + 0.3)
            
            # 步骤框
            step_box = slide.shapes.add_shape(
                MSO_SHAPE.ROUNDED_RECTANGLE,
                Inches(1), Inches(y_pos), Inches(step_width), Inches(step_height)
            )
            step_box.fill.solid()
            step_box.fill.fore_color.rgb = self.colors['primary']
            step_box.line.color.rgb = self.colors['primary']
            
            # 步骤文字
            step_frame = step_box.text_frame
            step_frame.clear()
            
            # 标题段落
            title_para = step_frame.paragraphs[0]
            title_para.text = step_title
            title_para.font.size = Pt(20)
            title_para.font.color.rgb = RGBColor(255, 255, 255)
            title_para.font.bold = True
            
            # 描述段落
            if step_desc:
                step_frame.add_paragraph()
                desc_para = step_frame.paragraphs[1]
                desc_para.text = step_desc
                desc_para.font.size = Pt(16)
                desc_para.font.color.rgb = RGBColor(255, 255, 255)
            
            # 箭头（除了最后一个步骤）
            if i < len(steps) - 1:
                arrow = slide.shapes.add_connector(
                    1, Inches(5), Inches(y_pos + step_height + 0.05),
                    Inches(5), Inches(y_pos + step_height + 0.25)
                )
                arrow.line.color.rgb = self.colors['secondary']
                arrow.line.width = Pt(3)
    
    def generate_complete_ppt(self):
        """生成完整的PPT"""
        print("🎨 开始生成机器学习方法论PPT...")
        
        # 第1页：标题页
        self.add_title_slide()
        
        # 第2页：问题分析框架
        problem_analysis_content = [
            "明确要解决什么问题：从工程需求到ML目标",
            "分析现有数据：数据类型、质量、数量评估",
            "确定任务类型：分类、回归、聚类、强化学习",
            "设计评估指标：如何衡量模型成功",
            "识别约束条件：实时性、精度、资源限制"
        ]
        self.add_content_slide(
            "问题分析框架",
            problem_analysis_content,
            "如何将工程问题转化为机器学习问题"
        )
        
        # 第3页：数据预处理策略
        preprocessing_left = [
            "数据清洗：处理缺失值、异常值",
            "数据标准化：归一化、标准化",
            "特征选择：去除冗余、选择重要特征",
            "特征构造：组合特征、多项式特征",
            "数据增强：扩充数据集规模"
        ]
        
        preprocessing_right = [
            "6维 → 63维特征扩展",
            "原始角度：θ₁, θ₂, ..., θ₆",
            "三角函数：sin(θ), cos(θ)",
            "多项式：θ², θ³",
            "交互项：θᵢ × θⱼ",
            "工作空间特征 + 奇异性检测"
        ]
        
        self.add_two_column_slide(
            "数据预处理：让数据更有价值",
            preprocessing_left,
            preprocessing_right,
            "通用预处理策略",
            "机器人项目实例"
        )
        
        # 第4页：模型选择策略
        model_selection_steps = [
            ("第1阶段：建立基线", "线性回归、多项式回归 - 快速验证数据有效性"),
            ("第2阶段：经典机器学习", "SVM、随机森林、梯度提升 - 处理复杂非线性"),
            ("第3阶段：深度学习", "神经网络、CNN、RNN - 学习高维特征"),
            ("第4阶段：先进架构", "Transformer、PINN、集成方法 - 融合多种技术")
        ]
        
        self.add_process_slide(
            "模型选择：从简单到复杂的进化",
            model_selection_steps,
            "没有万能的模型，只有合适的模型"
        )
        
        # 第5页：先进技术融合
        advanced_tech_content = [
            "Physics-Informed Neural Networks (PINN)：融入物理约束",
            "Transformer注意力机制：自动发现关节间重要关系",
            "NSGA-II多目标优化：平衡精度、复杂度、稳定性",
            "分离式预测头：位置和角度误差分别建模",
            "加权损失函数：70%位置 + 30%角度 + 物理约束",
            "协同效应：1+1+1>3的技术组合"
        ]
        
        self.add_content_slide(
            "先进技术融合：1+1+1>3",
            advanced_tech_content,
            "如何组合多种技术发挥协同效应"
        )
        
        # 第6页：训练策略与技巧
        training_left = [
            "数据划分：训练/验证/测试",
            "损失函数设计：加权+约束",
            "优化器选择：Adam/AdamW",
            "学习率调度：动态调整",
            "正则化：Dropout/BatchNorm",
            "早停机制：防止过拟合"
        ]
        
        training_right = [
            "过拟合 → 增加正则化、减少复杂度",
            "欠拟合 → 增加模型容量、更多特征",
            "梯度消失 → 残差连接、更好激活函数",
            "训练不稳定 → 梯度裁剪、批标准化",
            "收敛慢 → 学习率调度、预训练"
        ]
        
        self.add_two_column_slide(
            "训练策略：让模型学得更好",
            training_left,
            training_right,
            "训练流程优化",
            "常见问题与解决方案"
        )
        
        # 第7页：评估方法与指标
        evaluation_content = [
            "准确性指标：MSE、MAE、R² - 衡量预测精度",
            "稳定性评估：交叉验证、方差分析 - 确保结果可靠",
            "泛化性测试：新数据验证 - 检验实际应用能力",
            "效率性分析：推理时间、内存占用 - 满足实时要求",
            "我们的结果：位置精度提升88.7%，角度精度提升81.6%",
            "综合评估：R²=0.95，推理时间<3ms"
        ]
        
        self.add_content_slide(
            "模型评估：如何判断好坏",
            evaluation_content,
            "全面的评估体系"
        )
        
        # 第8页：实用技巧与经验
        tips_content = [
            "永远先可视化数据：理解数据分布和特征关系",
            "从简单模型开始：建立基线，逐步增加复杂度",
            "记录实验过程：什么有效、什么无效、为什么",
            "避免数据泄露：先划分数据，再做预处理",
            "不要过度拟合验证集：使用交叉验证调参",
            "关注物理意义：预测结果要符合工程常识"
        ]
        
        self.add_content_slide(
            "实用技巧：避免常见坑",
            tips_content,
            "从实践中总结的经验"
        )
        
        # 第9页：未来发展趋势
        future_trends_steps = [
            ("2024-2025：多模态融合", "视觉+力觉+位置信息的综合建模"),
            ("2025-2026：自监督学习", "减少标注需求，利用无标签数据"),
            ("2026-2027：联邦学习", "多机器人协同学习，保护数据隐私"),
            ("2027-2028：神经符号AI", "知识驱动+数据驱动的混合方法"),
            ("2028+：通用机器人智能", "一个模型解决多种机器人任务")
        ]
        
        self.add_process_slide(
            "未来发展趋势",
            future_trends_steps,
            "机器学习在工程中的发展方向"
        )
        
        # 第10页：总结
        summary_content = [
            "问题导向：从工程需求出发，明确目标和约束",
            "数据为王：高质量数据胜过复杂模型",
            "循序渐进：从简单到复杂，逐步优化",
            "技术融合：组合多种技术发挥协同效应",
            "持续改进：评估-分析-优化的闭环",
            "我们的成功：机器人定位精度提升88.7%"
        ]
        
        self.add_content_slide(
            "总结：机器学习方法论",
            summary_content,
            "系统性思维解决工程问题"
        )
        
        # 保存PPT文件
        ppt_filename = f"{self.output_dir}/机器学习方法论-朱昕鋆.pptx"
        self.prs.save(ppt_filename)
        
        print(f"\n✅ PPT生成完成！")
        print(f"📁 文件位置: {ppt_filename}")
        print(f"📊 共 {len(self.prs.slides)} 页")
        print("🎯 可以直接用PowerPoint打开编辑")

def main():
    """主函数"""
    try:
        generator = MLMethodologyPPTGenerator()
        generator.generate_complete_ppt()
    except ImportError:
        print("❌ 缺少python-pptx库，正在安装...")
        import subprocess
        subprocess.check_call(["pip", "install", "python-pptx"])
        print("✅ 安装完成，重新运行程序")
        generator = MLMethodologyPPTGenerator()
        generator.generate_complete_ppt()

if __name__ == "__main__":
    main()
