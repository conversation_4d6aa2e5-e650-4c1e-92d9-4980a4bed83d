#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成实验结果表格 - 用于实验报告
"""

import pandas as pd
import numpy as np

def create_summary_table():
    """创建实验结果汇总表"""
    
    # 实验结果数据
    results_data = {
        'X坐标': {
            'LightGBM': (0.9928, 14.35),
            'XGBoost': (0.9919, 15.17),
            '随机森林': (0.9876, 18.79),
            '梯度提升': (0.9848, 20.82),
            '神经网络': (0.9725, 27.99),
            '线性回归': (0.8034, 74.85),
            '岭回归': (0.8034, 74.85),
            'Lasso回归': (0.8034, 74.85),
            'SVM': (0.5145, 117.64)
        },
        'Y坐标': {
            '神经网络': (0.9985, 7.90),
            'XGBoost': (0.9929, 17.25),
            'LightGBM': (0.9927, 17.61),
            '随机森林': (0.9877, 22.80),
            '梯度提升': (0.9845, 25.58),
            'Lasso回归': (0.8065, 90.37),
            '线性回归': (0.8065, 90.37),
            '岭回归': (0.8065, 90.37),
            'SVM': (0.5650, 135.48)
        },
        'Z坐标': {
            'LightGBM': (0.9964, 9.68),
            '神经网络': (0.9957, 10.56),
            'XGBoost': (0.9954, 10.87),
            '随机森林': (0.9953, 10.97),
            '梯度提升': (0.9908, 15.40),
            'Lasso回归': (0.8053, 70.82),
            '岭回归': (0.8053, 70.83),
            '线性回归': (0.8053, 70.83),
            'SVM': (0.4187, 122.37)
        },
        'Rx角度': {
            '神经网络': (0.9454, 10.43),
            'LightGBM': (0.8038, 19.77),
            'XGBoost': (0.8036, 19.78),
            '随机森林': (0.7540, 22.14),
            '梯度提升': (0.2481, 38.70),
            'SVM': (0.1259, 41.73),
            'Lasso回归': (-0.0317, 45.33),
            '岭回归': (-0.0321, 45.34),
            '线性回归': (-0.0321, 45.34)
        },
        'Ry角度': {
            'LightGBM': (0.6452, 4.39),
            'XGBoost': (0.6340, 4.45),
            '梯度提升': (0.5489, 4.95),
            '随机森林': (0.4964, 5.23),
            '神经网络': (0.4511, 5.46),
            'SVM': (0.4370, 5.53),
            'Lasso回归': (0.2942, 6.19),
            '岭回归': (0.2935, 6.19),
            '线性回归': (0.2935, 6.19)
        },
        'Rz角度': {
            '神经网络': (0.5796, 86.97),
            'LightGBM': (0.4569, 98.84),
            '随机森林': (0.4313, 101.15),
            'XGBoost': (0.3791, 105.68),
            '梯度提升': (0.1739, 121.90),
            'Lasso回归': (-0.0070, 134.59),
            '岭回归': (-0.0071, 134.60),
            '线性回归': (-0.0071, 134.60),
            'SVM': (-0.1637, 144.69)
        }
    }
    
    # 创建最佳模型汇总表
    best_models = []
    for target, models in results_data.items():
        best_model = max(models.items(), key=lambda x: x[1][0])
        r2_score, rmse = best_model[1]
        
        # 性能等级判断
        if r2_score >= 0.95:
            level = "🏆 卓越"
        elif r2_score >= 0.8:
            level = "🏆 优秀"
        elif r2_score >= 0.6:
            level = "✅ 良好"
        elif r2_score >= 0.3:
            level = "⚠️ 一般"
        else:
            level = "❌ 较差"
        
        best_models.append({
            '目标变量': target,
            '最佳模型': best_model[0],
            'R²得分': f"{r2_score:.4f}",
            'RMSE': f"{rmse:.2f}",
            '性能等级': level
        })
    
    df_best = pd.DataFrame(best_models)
    
    # 创建完整结果表
    all_results = []
    for target, models in results_data.items():
        for model, (r2, rmse) in models.items():
            all_results.append({
                '目标变量': target,
                '模型': model,
                'R²得分': r2,
                'RMSE': rmse
            })
    
    df_all = pd.DataFrame(all_results)
    
    return df_best, df_all

def calculate_model_rankings():
    """计算模型总体排名"""
    
    # 各模型平均R²得分（基于实验结果）
    model_avg_r2 = {
        '神经网络': 0.8238,
        'LightGBM': 0.8146,
        'XGBoost': 0.7995,
        '随机森林': 0.7687,
        '梯度提升': 0.6579,
        'Lasso回归': 0.4674,
        '岭回归': 0.4673,
        '线性回归': 0.4673,
        'SVM': 0.2464
    }
    
    rankings = []
    for rank, (model, avg_r2) in enumerate(sorted(model_avg_r2.items(), 
                                                  key=lambda x: x[1], reverse=True), 1):
        rankings.append({
            '排名': rank,
            '模型': model,
            '平均R²得分': f"{avg_r2:.4f}",
            '适用场景': get_model_scenario(model)
        })
    
    return pd.DataFrame(rankings)

def get_model_scenario(model_name):
    """获取模型适用场景"""
    scenarios = {
        '神经网络': '复杂非线性关系，高精度需求',
        'LightGBM': '生产环境，速度与精度平衡',
        'XGBoost': '竞赛项目，高性能需求',
        '随机森林': '需要模型解释性的场景',
        '梯度提升': '传统集成学习应用',
        'Lasso回归': '需要特征选择的线性问题',
        '岭回归': '多重共线性的线性问题',
        '线性回归': '简单线性关系建模',
        'SVM': '中小规模高维数据'
    }
    return scenarios.get(model_name, '通用场景')

def print_experiment_summary():
    """打印实验总结"""
    print("="*80)
    print("机器人位姿预测实验结果总结")
    print("="*80)
    
    # 最佳模型表
    df_best, df_all = create_summary_table()
    print("\n📊 各目标变量最佳模型:")
    print(df_best.to_string(index=False))
    
    # 模型排名表
    df_rankings = calculate_model_rankings()
    print("\n🏆 模型总体排名:")
    print(df_rankings.to_string(index=False))
    
    # 关键结论
    print("\n🎯 关键结论:")
    print("1. 位置坐标预测(X,Y,Z): 精度极高(R²>0.99)，达到工业级标准")
    print("2. 姿态角度预测: Rx优秀(0.95)，Ry良好(0.65)，Rz一般(0.58)")
    print("3. 最佳算法组合: LightGBM + 神经网络")
    print("4. 实用价值: 位置预测可立即部署，姿态预测需进一步优化")
    
    # 保存结果
    with pd.ExcelWriter('实验结果汇总.xlsx', engine='openpyxl') as writer:
        df_best.to_excel(writer, sheet_name='最佳模型', index=False)
        df_rankings.to_excel(writer, sheet_name='模型排名', index=False)
        df_all.to_excel(writer, sheet_name='完整结果', index=False)
    
    print("\n📁 结果已保存到: 实验结果汇总.xlsx")

if __name__ == "__main__":
    print_experiment_summary()
