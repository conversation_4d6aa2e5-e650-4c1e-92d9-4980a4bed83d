#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的PINN模型 - 快速验证版本
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def set_random_seeds(seed=42):
    """设置随机种子"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)

def generate_test_data(n_samples=500):
    """生成测试数据"""
    np.random.seed(42)
    
    # 生成关节角度
    joint_angles = []
    for i in range(6):
        if i in [0, 3, 4, 5]:  # 旋转关节
            angles = np.random.uniform(-180, 180, n_samples)
        else:  # 俯仰关节
            angles = np.random.uniform(-90, 90, n_samples)
        joint_angles.append(angles)
    
    joint_angles = np.array(joint_angles).T
    
    # 生成误差（简化版）
    errors = []
    for angles in joint_angles:
        angles_rad = np.deg2rad(angles)
        
        # 位置误差
        pos_error = np.array([
            0.3 * np.sin(angles_rad[0] - angles_rad[1]) + 0.1 * np.random.normal(0, 0.05),
            0.3 * np.cos(angles_rad[0] - angles_rad[2]) + 0.1 * np.random.normal(0, 0.05),
            0.25 * np.sin(angles_rad[1] + angles_rad[2]) + 0.08 * np.random.normal(0, 0.05)
        ])
        
        # 角度误差
        ori_error = np.array([
            0.08 * np.sin(angles_rad[3] - angles_rad[4]) + 0.02 * np.random.normal(0, 0.01),
            0.08 * np.cos(angles_rad[4] - angles_rad[5]) + 0.02 * np.random.normal(0, 0.01),
            0.06 * np.sin(angles_rad[3] + angles_rad[5]) + 0.015 * np.random.normal(0, 0.01)
        ])
        
        errors.append(np.concatenate([pos_error, ori_error]))
    
    return joint_angles, np.array(errors)

def create_simple_features(joint_angles):
    """创建简化的物理特征"""
    angles_rad = np.deg2rad(joint_angles)
    features = []
    
    # 基础特征
    features.extend(angles_rad)  # 6维
    
    # 三角函数特征
    for angle in angles_rad:
        features.extend([np.sin(angle), np.cos(angle)])  # 12维
    
    # 关键耦合特征
    for i in range(6):
        for j in range(i+1, 6):
            if j - i <= 2:  # 只考虑相邻关节
                features.append(np.cos(angles_rad[i] - angles_rad[j]))
    
    return np.array(features)

class SimplePINNModel(nn.Module):
    """简化的PINN模型用于测试"""
    
    def __init__(self, input_dim, hidden_dims=[128, 64, 32]):
        super(SimplePINNModel, self).__init__()
        
        # 特征提取层
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.GELU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # 位置分支
        self.position_branch = nn.Sequential(
            nn.Linear(prev_dim, 16),
            nn.GELU(),
            nn.Linear(16, 3)
        )
        
        # 角度分支
        self.orientation_branch = nn.Sequential(
            nn.Linear(prev_dim, 16),
            nn.GELU(),
            nn.Linear(16, 3)
        )
    
    def forward(self, x):
        features = self.feature_extractor(x)
        pos_pred = self.position_branch(features)
        ori_pred = self.orientation_branch(features)
        return torch.cat([pos_pred, ori_pred], dim=1)

def train_simple_model(X_train, y_train, X_test, y_test, epochs=300):
    """训练简化模型"""
    # 数据预处理
    scaler_X = StandardScaler()
    scaler_y = StandardScaler()
    
    X_train_scaled = scaler_X.fit_transform(X_train)
    X_test_scaled = scaler_X.transform(X_test)
    y_train_scaled = scaler_y.fit_transform(y_train)
    y_test_scaled = scaler_y.transform(y_test)
    
    # 转换为张量
    X_train_tensor = torch.FloatTensor(X_train_scaled)
    y_train_tensor = torch.FloatTensor(y_train_scaled)
    X_test_tensor = torch.FloatTensor(X_test_scaled)
    y_test_tensor = torch.FloatTensor(y_test_scaled)
    
    # 创建模型
    model = SimplePINNModel(input_dim=X_train.shape[1])
    optimizer = torch.optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=20, factor=0.8)
    
    # 训练
    train_losses = []
    test_losses = []
    
    for epoch in range(epochs):
        model.train()
        predictions = model(X_train_tensor)
        
        # 损失计算
        pos_loss = nn.MSELoss()(predictions[:, :3], y_train_tensor[:, :3])
        ori_loss = nn.MSELoss()(predictions[:, 3:], y_train_tensor[:, 3:])
        total_loss = 0.4 * pos_loss + 0.6 * ori_loss
        
        optimizer.zero_grad()
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        # 验证
        model.eval()
        with torch.no_grad():
            test_pred = model(X_test_tensor)
            test_loss = nn.MSELoss()(test_pred, y_test_tensor)
        
        train_losses.append(total_loss.item())
        test_losses.append(test_loss.item())
        scheduler.step(test_loss)
        
        if epoch % 50 == 0:
            print(f'Epoch {epoch}: Train Loss = {total_loss.item():.6f}, Test Loss = {test_loss.item():.6f}')
    
    return model, scaler_X, scaler_y, train_losses, test_losses

def main():
    """主测试函数"""
    print("🚀 测试优化PINN模型")
    print("="*50)
    
    # 设置随机种子
    set_random_seeds(42)
    
    # 生成测试数据
    print("📊 生成测试数据...")
    joint_angles, errors = generate_test_data(n_samples=500)
    
    # 创建特征
    print("🔬 创建物理特征...")
    features = []
    for angles in joint_angles:
        feat = create_simple_features(angles)
        features.append(feat)
    features = np.array(features)
    
    print(f"   特征维度: {joint_angles.shape[1]} → {features.shape[1]}")
    
    # 数据划分
    X_train, X_test, y_train, y_test = train_test_split(
        features, errors, test_size=0.2, random_state=42
    )
    
    # 训练模型
    print("🧠 训练模型...")
    model, scaler_X, scaler_y, train_losses, test_losses = train_simple_model(
        X_train, y_train, X_test, y_test, epochs=300
    )
    
    # 评估结果
    print("📊 评估结果...")
    model.eval()
    X_test_scaled = scaler_X.transform(X_test)
    X_test_tensor = torch.FloatTensor(X_test_scaled)
    
    with torch.no_grad():
        predictions_scaled = model(X_test_tensor).numpy()
        predictions = scaler_y.inverse_transform(predictions_scaled)
    
    # 计算误差
    pos_errors = np.sqrt(np.sum((predictions[:, :3] - y_test[:, :3])**2, axis=1))
    ori_errors = np.abs(predictions[:, 3:] - y_test[:, 3:])
    
    print(f"\n🎯 测试结果:")
    print(f"   平均位置误差: {np.mean(pos_errors):.3f} mm")
    print(f"   平均角度误差: {np.median(ori_errors):.3f}°")
    print(f"   最终训练损失: {train_losses[-1]:.6f}")
    print(f"   最终测试损失: {test_losses[-1]:.6f}")
    
    # 简单可视化
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='训练损失', alpha=0.8)
    plt.plot(test_losses, label='测试损失', alpha=0.8)
    plt.xlabel('轮数')
    plt.ylabel('损失')
    plt.title('训练曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 2)
    plt.scatter(np.sqrt(np.sum(y_test[:, :3]**2, axis=1)), 
                np.sqrt(np.sum(predictions[:, :3]**2, axis=1)), alpha=0.6)
    plt.xlabel('真实位置误差 (mm)')
    plt.ylabel('预测位置误差 (mm)')
    plt.title('位置误差对比')
    plt.plot([0, 1], [0, 1], 'r--', alpha=0.8)
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 3)
    plt.scatter(np.sqrt(np.sum(y_test[:, 3:]**2, axis=1)), 
                np.sqrt(np.sum(predictions[:, 3:]**2, axis=1)), alpha=0.6)
    plt.xlabel('真实角度误差 (°)')
    plt.ylabel('预测角度误差 (°)')
    plt.title('角度误差对比')
    plt.plot([0, 0.2], [0, 0.2], 'r--', alpha=0.8)
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('测试结果.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 测试完成！")

if __name__ == "__main__":
    main()
