#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三方对比分析模块
对比理论值、实测值、预测值的精度和误差特性
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ThreeWayComparison:
    """三方对比分析器"""
    
    def __init__(self):
        self.coordinates = ['X', 'Y', 'Z', 'Rx', 'Ry', 'Rz']
        self.coord_units = ['mm', 'mm', 'mm', '°', '°', '°']
        
    def load_all_data(self):
        """加载所有对比数据"""
        print("=== 加载三方对比数据 ===")
        
        # 加载关节角度数据
        self.joint_data = pd.read_excel('../theta2000.xlsx', header=None)
        self.joint_data.columns = [f'theta_{i+1}' for i in range(6)]
        
        # 加载实测数据
        self.measured_data = pd.read_excel('../real2000.xlsx')
        
        # 加载理论计算结果
        try:
            self.theoretical_data = pd.read_excel('理论位姿计算结果.xlsx')
        except FileNotFoundError:
            print("未找到理论计算结果，请先运行理论计算模块")
            return None
        
        # 加载机器学习预测结果
        try:
            self.predicted_data = pd.read_excel('机器学习预测结果.xlsx')
        except FileNotFoundError:
            print("未找到预测结果，请先运行机器学习预测模块")
            return None
        
        print(f"数据加载完成:")
        print(f"- 关节角度: {self.joint_data.shape}")
        print(f"- 实测数据: {self.measured_data.shape}")
        print(f"- 理论数据: {self.theoretical_data.shape}")
        print(f"- 预测数据: {self.predicted_data.shape}")
        
        return True
    
    def calculate_errors(self):
        """计算各种误差"""
        print("\n=== 计算误差指标 ===")
        
        self.errors = {}
        
        for i, coord in enumerate(self.coordinates):
            # 获取数据
            if coord in ['Rx', 'Ry', 'Rz']:
                measured = self.measured_data.iloc[:, i].values
                theoretical = self.theoretical_data.iloc[:, i].values
                predicted = self.predicted_data.iloc[:, i].values
            else:
                measured = self.measured_data.iloc[:, i].values
                theoretical = self.theoretical_data.iloc[:, i].values
                predicted = self.predicted_data.iloc[:, i].values
            
            # 计算误差
            theory_error = theoretical - measured  # 理论值 - 实测值
            pred_error = predicted - measured      # 预测值 - 实测值
            theory_pred_error = theoretical - predicted  # 理论值 - 预测值
            
            self.errors[coord] = {
                'measured': measured,
                'theoretical': theoretical,
                'predicted': predicted,
                'theory_error': theory_error,
                'pred_error': pred_error,
                'theory_pred_error': theory_pred_error
            }
        
        print("误差计算完成")
    
    def calculate_statistics(self):
        """计算统计指标"""
        print("\n=== 计算统计指标 ===")
        
        self.statistics = {}
        
        for coord in self.coordinates:
            errors = self.errors[coord]
            
            # 计算各种统计指标
            stats_dict = {}
            
            for error_type in ['theory_error', 'pred_error', 'theory_pred_error']:
                error_data = errors[error_type]
                
                stats_dict[error_type] = {
                    'mean': np.mean(error_data),
                    'std': np.std(error_data),
                    'rmse': np.sqrt(np.mean(error_data**2)),
                    'mae': np.mean(np.abs(error_data)),
                    'max_error': np.max(np.abs(error_data)),
                    'min_error': np.min(error_data),
                    'max_error_val': np.max(error_data),
                    'percentile_95': np.percentile(np.abs(error_data), 95),
                    'percentile_99': np.percentile(np.abs(error_data), 99)
                }
            
            self.statistics[coord] = stats_dict
        
        print("统计指标计算完成")
    
    def generate_summary_table(self):
        """生成汇总表格"""
        print("\n=== 生成汇总表格 ===")
        
        summary_data = []
        
        for coord in self.coordinates:
            unit = self.coord_units[self.coordinates.index(coord)]
            stats = self.statistics[coord]
            
            # 理论误差统计
            theory_stats = stats['theory_error']
            pred_stats = stats['pred_error']
            
            summary_data.append({
                '坐标': coord,
                '单位': unit,
                
                # 理论值 vs 实测值
                '理论误差均值': f"{theory_stats['mean']:.3f}",
                '理论误差标准差': f"{theory_stats['std']:.3f}",
                '理论RMSE': f"{theory_stats['rmse']:.3f}",
                '理论MAE': f"{theory_stats['mae']:.3f}",
                '理论最大误差': f"{theory_stats['max_error']:.3f}",
                
                # 预测值 vs 实测值
                '预测误差均值': f"{pred_stats['mean']:.3f}",
                '预测误差标准差': f"{pred_stats['std']:.3f}",
                '预测RMSE': f"{pred_stats['rmse']:.3f}",
                '预测MAE': f"{pred_stats['mae']:.3f}",
                '预测最大误差': f"{pred_stats['max_error']:.3f}",
                
                # 精度对比
                '理论精度等级': self.get_accuracy_level(theory_stats['rmse'], coord),
                '预测精度等级': self.get_accuracy_level(pred_stats['rmse'], coord),
                '最优方法': '预测' if pred_stats['rmse'] < theory_stats['rmse'] else '理论'
            })
        
        self.summary_df = pd.DataFrame(summary_data)
        
        # 保存汇总表
        self.summary_df.to_excel('三方对比汇总表.xlsx', index=False)
        print("汇总表已保存到: 三方对比汇总表.xlsx")
        
        return self.summary_df
    
    def get_accuracy_level(self, rmse, coord):
        """根据RMSE判断精度等级"""
        if coord in ['X', 'Y', 'Z']:  # 位置坐标 (mm)
            if rmse < 1:
                return "🏆 极高"
            elif rmse < 5:
                return "🏆 高"
            elif rmse < 20:
                return "✅ 中等"
            elif rmse < 50:
                return "⚠️ 一般"
            else:
                return "❌ 较低"
        else:  # 角度 (度)
            if rmse < 0.1:
                return "🏆 极高"
            elif rmse < 0.5:
                return "🏆 高"
            elif rmse < 2:
                return "✅ 中等"
            elif rmse < 5:
                return "⚠️ 一般"
            else:
                return "❌ 较低"
    
    def plot_error_comparison(self):
        """绘制误差对比图"""
        print("\n=== 生成误差对比图表 ===")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, coord in enumerate(self.coordinates):
            ax = axes[i]
            errors = self.errors[coord]
            unit = self.coord_units[i]
            
            # 绘制误差分布箱线图
            error_data = [
                errors['theory_error'],
                errors['pred_error']
            ]
            
            bp = ax.boxplot(error_data, labels=['理论误差', '预测误差'], patch_artist=True)
            bp['boxes'][0].set_facecolor('lightblue')
            bp['boxes'][1].set_facecolor('lightcoral')
            
            ax.set_title(f'{coord} 误差分布对比')
            ax.set_ylabel(f'误差 ({unit})')
            ax.grid(True, alpha=0.3)
            
            # 添加统计信息
            theory_rmse = self.statistics[coord]['theory_error']['rmse']
            pred_rmse = self.statistics[coord]['pred_error']['rmse']
            
            ax.text(0.02, 0.98, f'理论RMSE: {theory_rmse:.3f}\n预测RMSE: {pred_rmse:.3f}',
                   transform=ax.transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig('误差对比分析图.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_accuracy_comparison(self):
        """绘制精度对比雷达图"""
        print("\n生成精度对比雷达图...")
        
        # 准备雷达图数据
        categories = self.coordinates
        
        # 计算相对精度 (1 - normalized_rmse)
        theory_accuracy = []
        pred_accuracy = []
        
        for coord in categories:
            theory_rmse = self.statistics[coord]['theory_error']['rmse']
            pred_rmse = self.statistics[coord]['pred_error']['rmse']
            
            # 归一化RMSE (相对于数据范围)
            data_range = np.ptp(self.errors[coord]['measured'])
            theory_norm = min(theory_rmse / data_range, 1.0)
            pred_norm = min(pred_rmse / data_range, 1.0)
            
            theory_accuracy.append(1 - theory_norm)
            pred_accuracy.append(1 - pred_norm)
        
        # 绘制雷达图
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]  # 闭合
        
        theory_accuracy += theory_accuracy[:1]
        pred_accuracy += pred_accuracy[:1]
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        ax.plot(angles, theory_accuracy, 'o-', linewidth=2, label='理论计算', color='blue')
        ax.fill(angles, theory_accuracy, alpha=0.25, color='blue')
        
        ax.plot(angles, pred_accuracy, 'o-', linewidth=2, label='机器学习预测', color='red')
        ax.fill(angles, pred_accuracy, alpha=0.25, color='red')
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_ylim(0, 1)
        ax.set_title('各坐标精度对比雷达图', size=16, pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
        
        plt.savefig('精度对比雷达图.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_sample_showcase(self, n_samples=10):
        """创建样本展示数据"""
        print(f"\n=== 创建 {n_samples} 个样本展示 ===")
        
        # 选择代表性样本
        indices = np.linspace(0, len(self.joint_data)-1, n_samples, dtype=int)
        
        showcase_data = []
        
        for idx in indices:
            sample = {
                '样本编号': idx,
                '关节角度': f"[{', '.join([f'{self.joint_data.iloc[idx, i]:.1f}' for i in range(6)])}]"
            }
            
            # 添加三种方法的结果
            for i, coord in enumerate(self.coordinates):
                unit = self.coord_units[i]
                
                measured = self.errors[coord]['measured'][idx]
                theoretical = self.errors[coord]['theoretical'][idx]
                predicted = self.errors[coord]['predicted'][idx]
                
                theory_error = theoretical - measured
                pred_error = predicted - measured
                
                sample[f'{coord}_实测'] = f"{measured:.3f}{unit}"
                sample[f'{coord}_理论'] = f"{theoretical:.3f}{unit}"
                sample[f'{coord}_预测'] = f"{predicted:.3f}{unit}"
                sample[f'{coord}_理论误差'] = f"{theory_error:.3f}{unit}"
                sample[f'{coord}_预测误差'] = f"{pred_error:.3f}{unit}"
            
            showcase_data.append(sample)
        
        showcase_df = pd.DataFrame(showcase_data)
        
        # 保存展示数据
        showcase_df.to_excel('样本展示数据.xlsx', index=False)
        print(f"样本展示数据已保存: {len(showcase_df)} 个样本")
        
        return showcase_df
    
    def generate_final_report(self):
        """生成最终报告"""
        print("\n=== 生成最终分析报告 ===")
        
        report = []
        report.append("# 高精度位姿预测三方对比分析报告\n")
        
        report.append("## 1. 实验概述")
        report.append("本实验对比了三种位姿预测方法的精度：")
        report.append("- **理论计算**: 基于机器人运动学正解公式")
        report.append("- **激光跟踪仪**: 高精度实际测量值")
        report.append("- **机器学习**: 基于数据驱动的预测模型\n")
        
        report.append("## 2. 主要发现")
        
        # 找出最优方法
        best_methods = {}
        for coord in self.coordinates:
            theory_rmse = self.statistics[coord]['theory_error']['rmse']
            pred_rmse = self.statistics[coord]['pred_error']['rmse']
            best_methods[coord] = '机器学习' if pred_rmse < theory_rmse else '理论计算'
        
        report.append("### 2.1 各坐标最优方法")
        for coord in self.coordinates:
            method = best_methods[coord]
            rmse = min(self.statistics[coord]['theory_error']['rmse'],
                      self.statistics[coord]['pred_error']['rmse'])
            unit = self.coord_units[self.coordinates.index(coord)]
            report.append(f"- **{coord}**: {method} (RMSE: {rmse:.3f}{unit})")
        
        report.append("\n### 2.2 整体精度评估")
        ml_wins = sum(1 for method in best_methods.values() if method == '机器学习')
        theory_wins = len(self.coordinates) - ml_wins
        
        report.append(f"- 机器学习优于理论计算: {ml_wins}/{len(self.coordinates)} 个坐标")
        report.append(f"- 理论计算优于机器学习: {theory_wins}/{len(self.coordinates)} 个坐标")
        
        # 保存报告
        with open('三方对比分析报告.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print("最终分析报告已保存到: 三方对比分析报告.md")

def main():
    """主函数"""
    print("开始三方对比分析...")
    
    # 创建分析器
    analyzer = ThreeWayComparison()
    
    # 加载数据
    if not analyzer.load_all_data():
        print("数据加载失败，请确保已运行理论计算和机器学习预测模块")
        return
    
    # 计算误差
    analyzer.calculate_errors()
    
    # 计算统计指标
    analyzer.calculate_statistics()
    
    # 生成汇总表
    summary_df = analyzer.generate_summary_table()
    print("\n汇总表预览:")
    print(summary_df[['坐标', '理论RMSE', '预测RMSE', '最优方法']].to_string(index=False))
    
    # 绘制图表
    analyzer.plot_error_comparison()
    analyzer.plot_accuracy_comparison()
    
    # 创建样本展示
    showcase_df = analyzer.create_sample_showcase(10)
    
    # 生成最终报告
    analyzer.generate_final_report()
    
    print("\n三方对比分析完成！")
    print("生成文件:")
    print("- 三方对比汇总表.xlsx")
    print("- 样本展示数据.xlsx")
    print("- 误差对比分析图.png")
    print("- 精度对比雷达图.png")
    print("- 三方对比分析报告.md")

if __name__ == "__main__":
    main()
