
=== 第 1 页 ===

第 32 卷 第 18 期 光学 精密工程 Vol.32 No.18
2024 年 9 月 Optics and Precision Engineering Sept. 2024
文章编号 1004-924X（2024）18-2783-09
基于支持向量回归的工业机器人空间误差预测
乔贵方1，2*，高春晖1，蒋欣怡1，徐思敏1，刘 娣1
（1. 南京工程学院 自动化学院，江苏 南京 211167；
2. 东南大学 仪器科学与工程学院，江苏 南京 210096）
摘要：鉴于高端智能制造领域对高精度应用场景下的工业机器人绝对定位精度的更高要求。本文主要研究基于支持向
量回归（Support Vector Regression ， SVR）模型的机器人空间误差预测方法。针对Staubli TX60型串联工业机器人进行
了运动学建模和误差分析。搭建了基于Leica AT960激光跟踪仪的机器人测量实验平台，并进行了大量空间位姿点的
测量，通过真实数据集训练优化SVR模型。基于SVR方法对机器人实际位姿误差进行预测与补偿，避免了复杂的误差
建模过程。机器人平均位置误差和平均姿态误差分别由补偿前的（0.706 1 mm，0.174 2°）降低至（0.055 6 mm，
0.024 6°），位置误差降低了92.12%，姿态误差降低了85.88%。最后，通过与BP，Elman神经网络以及传统LM几何参
数标定方法进行对比，验证了基于SVR模型进行空间误差预测对机器人位置和姿态误差降低效果的有效性和均衡性。
关 键 词：支持向量回归；非模型标定；工业机器人；误差预测；机器人标定
中图分类号：TP394.1；TH691.9 文献标识码：A doi：10.37188/OPE.20243218.2783
Spatial error prediction method for industrial robot based on
Support Vector Regression
QIAO Guifang
1，2*，
GAO Chunhui
1，
JIANG Xinyi
1，
XU Simin
1，
LIU Di
1
（1.School of Automation， Nanjing Institute of Technology， Nanjing 211167， China；
2.School of Instrument Science and Engineering， Southeast University， Nanjing 210096， China）
* Corresponding author， E-mail： <EMAIL>
Abstract： The high-end intelligent manufacturing field has put forward higher requirements for the abso⁃
lute pose accuracy of industrial robots in high-accuracy application scenarios. This paper investigated the
improvement of robot accuracy performance based on Support Vector Regression（ SVR）. Kinematic mod⁃
eling and error analysis were performed on the Staubli TX60 series industrial robot. A robot measurement
experiment platform was established based on the Leica AT960 laser tracker， and a large number of spatial
position points were measured. The SVR model was trained and optimized based on real data sets. The
actual pose error of the robot is predicted by Support Vector Regression Model， which avoids the compli⁃
cated error modeling in the model-based robot accuracy improvement method. The average position error
and average attitude error of the robot are reduced from（ 0.706 1 mm，0.174 2°） to（ 0.055 6 mm，0.024 6°）
before compensation， respectively， and the position error is reduced by 92.12% and the attitude error is re⁃
收稿日期：2024-05-28；修订日期：2024-07-26.
基金项目：国家自然科学基金资助项目（No.51905258）；中国博士后科学基金资助项目（No.2019M650095）；江苏省
研究生科研与实践创新计划资助项目（No.SJCX23_1164）

==================================================


=== 第 2 页 ===

2784 光学 精密工程 第 32 卷
duced by 85.88%. Finally, the comparison with BP neural network, Elman neural network and tradition⁃
al LM geometric parameter calibration method verified the effectiveness and balance of spatial error predic⁃
tion based on SVR model in reducing robot position and attitude errors.
Key words： Support vector regression （SVR）； non-model calibration； industrial robots； error predic⁃
tion； robot calibration
1 引 言 误差补偿方法可进行预测误差的补偿，实现机器
人非模型精度标定。非模型标定方法的关键在
高端智能制造领域的快速发展，对诸如火箭 于机器人空间误差预测，误差预测方法的性能直
发动机焊接、精密部件加工磨削，以及精准对接 接决定了误差补偿效果。
装配等高精度应用场景下的工业机器人精度性 Gao T 等［11］通 过 极 限 学 习 机（Extreme
能提出了更高要求［1］。当前，工业机器人的重复 Learning Machine，ELM）神经网络针对机器人非
定位精度虽能达到0.01~0.1 mm的精度水平， 几何误差源进行了补偿，并验证了该方法的有效
但其绝对定位精度低的问题仍限制了工业机器 性。Min K等［12］针对机器人位置精度提出了一种
人在高端智能制造领域的实际应用［2］。由于几何 无模型标定方法，通过改进基于Kriging的误差
误差与非几何误差因素［3］的存在，工业机器人的 补偿方法，提高了误差补偿的性能和稳定性，可
绝对定位精度仍为毫米级，难以满足“智能机器 以显著降低机器人定位误差。Du等［13］基于BP
人”重点专项中对绝对定位精度优于0.1 mm和 神经网络进行定位误差补偿，同时考虑了几何误
姿态角精度优于0.1°的精度要求［4］。因此，标定 差和非几何误差因素，可将机器人定位精度提高
技术作为一种能够有效提高机器人绝对定位精 80%以上；Wang等［14］基于深度置信网络和误差相
度的技术手段而被重点研究。 似度建立了工业机器人位姿误差预测模型，并在
机器人误差来源主要由关节误差、几何参数 KUKA KR500-3 6机器人上验证了方案的有效
误差以及由关节柔性、齿轮间隙等因素引起的非 性，补偿后，最大绝对位置误差和姿态误差分别从
几何参数误差构成［5］，又以几何参数误差为主，但 1.524 mm，0.082°减小到 0.244 mm 和 0.037°。
非几何参数误差仍占总误差的10%~20%，同样 Hu J等［15］基于GPSO-DNN优化神经网络预测模
在一定程度上影响了机器人精度。高贯斌等 型，进行定位误差预测与补偿，将机器人定位误差
人［6］ 基于空间误差相似度的概念，提出了一种包 由补偿前的1.529 mm减小到0.343 mm，精度提
含距离和方向的定位误差预测和补偿方法，有效 高了77.57%，但均未对姿态误差展开研究。
地解决了由于关节迟滞等因素造成的方向性误 针对机器人非模型标定过程中关键的误差
差。当前机器人标定技术多集中于离线标定方 预测过程，鉴于支持向量回归（Support Vector
法，主要包括基于误差模型的几何参数标定与非 Regression，SVR）在处理小样本、非线性回归和
模型标定方法。几何参数标定基本步骤分为建 高维数据方面的优越表现，提出一种基于支持
模、测量、辨识和补偿［7］。由于该方法仅针对几何 向量回归模型的机器人空间误差预测方法。通
参数误差，误差建模与参数辨识过程繁琐且无法 过SVR模型建立机器人关节角与机器人位置和
建立完备的数学模型，补偿效果有限。非模型标 姿态误差的映射机理，实现机器人位姿误差
定方法能够综合地考虑几何参数误差与非几何 预测。
参数误差因素，通过建立误差与机器人理论位姿
2 工业机器人运动学模型与误差
或关节角之间的映射模型，利用Kriging插值、模
糊插值等空间插值法［8］或极限学习机ELM，BP 分析
神经网络等神经网络法进行机器人误差预测［9］。
通过文献［10］中基于伪目标迭代的误差补偿方法 2.1 工业机器人运动学模型
或基于Newton-Rapson的误差补偿方法等运动 本文待标定的Staubli TX60工业机器人为

==================================================


=== 第 3 页 ===

第 18 期 乔贵方，等：基于支持向量回归的工业机器人空间误差预测 2785
六自由度串联机器人，其机械结构及关节坐标系 Staubli TX60工业机器人的正运动学模型，相邻
的示意图如图 1 所示。根据 M-DH 模型建立 连杆坐标系的转换关系如式（1）所示：
A i=Rot(Z i ，θ i)Trans(Z i ，d i)Trans(X i ，a i)Rot(X i ，α i)Rot(Y i ，β i)=
■||cosθ icosβ i-sinθ isinα isinβ
i
-sinθ icosα
i
cosθ isinβ i+sinθ isinα icosβ
i
a icosθ
i
■|
|| | |
|| |
||sinθ icosβ i+cosθ isinα isinβ
i
cosθ icosα
i
sinθ isinβ i-cosθ isinα icosβ
i
a isinθ
i
|
|
||
|
|， （1）
|| -cosα isinβ
i
sinα
i
cosα icosβ
i
d
i |
|
|| |
|| |
■ 0 0 0 1 ■
表1 Staubli TX60机器人理论MD-H参数
式中，θ
i
，d
i
，a
i
，α
i
，β
i
分别为Staubli TX60机器人的
Tab.1 Theoretical MD-H parameters of Staubli TX60 robot
关节角、连杆偏距、连杆长度、连杆转角、相邻平
行两关节处绕Y轴转动的旋转参数β。
i θ i/rad d i/mm a i/mm α i/rad β i/rad
1 π 0 0 π/2 -
根据图 1 建立的关节坐标系，待标定的
2 π/2 0 290 0 0
Staubli TX60机器人的M-DH模型参数如表1所
3 π/2 20 0 π/2 -
示。根据相邻坐标系的齐次变换矩阵A 可得工
i
4 π 310 0 π/2 -
业机器人末端位姿变换矩阵如式（2）所示：
5 π 0 0 π/2 -
T n=A
1
A
2
A
3
⋯A
6
= ■
■
| || |R
0
n P
1
n ■
■
|||| ， （2）
6 0 70 0 0 -
其中：T 为机器人末端相对于基坐标系的齐次变
n
换矩阵，R 为机器人末端姿态旋转矩阵，P 为机 2.2 工业机器人标定系统与误差分析
n n
器人末端位置矢量，分别表示理论姿态和理论 图2为本文所搭建的工业机器人标定系统，
位置。
该系统利用Leica AT960激光跟踪仪测量待标定
工业机器人Staubli TX60。Leica AT960激光跟
踪仪的测量不确定度为±（15 μm+6 μm/m）。
Staubli TX60 工业机器人的重复定位精度为
±0.02 mm，额定负载为3 kg，最大负载为5 kg。
Leica AT960激光跟踪仪的T-mac测量工具安装
在 Staubli TX60工业机器人的末端法兰盘上。
配套使用的测量分析软件为Spatial Analyzer，该
软件提供了拟合几何体、建立坐标系等功能，相
图1 Staubli TX60工业机器人关节坐标系示意图
Fig.1 Schematic diagram of the joint coordinate system 图2 工业机器人标定系统
for the Staubli TX60 industrial robot Fig.2 Industrial robot calibration system

==================================================


=== 第 4 页 ===

2786 光学 精密工程 第 32 卷
关运动学建模及参数辨识程序利用Matlab软件。 对于给定机器人数据集D=｛（x
i
，y
i
）｝，（i=
本文中所涉及的测量过程均符合GB/T-12642- 1，2，…，m），构建回归模型：
( ) ( )
2013及ISO-9283工业机器人性能规范及其试验 f x =wTϕ x +b， （7）
方法标准［16］。 其中：w为特征权重向量，b为偏置项，ϕ ( x ) 为非
根据激光跟踪仪的测量数据，计算工业机器 线性映射函数。
人的末端位姿误差ΔT为： SVR通过寻找最优超平面，最大化间隔带宽
ΔT=T r-T n= ■
■
| || |R
0
r P
1
r ■
■
|||| - ■
■
| || |R
0
n P
1
n ■
■
|||| ，（3） 度
理部
与
分
最
不
小
可
化
分
总
数
损
据
失
点
建
，
立
引
优
入
化
松
目
弛
标
变
函
量
数
ξ
，
和
并
ξ
且
  ，
为
则有
处
i i
其中：T 为测量所得机器人末端通过激光跟踪
r SVR问题描述：
仪 阵 测 R r 量 与 得 位 到 置 的 矢 实 量 际 P r 位 ，分 姿 别 矩 表 阵 示 ，包 实 括 际 姿 姿 态 态 旋 和 转 实 矩 际 m w， i b n 2 1 ‖w‖2 +C∑ i= m 1 ( ξ i+ξ  i )
( )
位置。 s.t. f x
i
-y i≤ε+ξ
i
，
， （8）
根据微分运动原理［17］，工业机器人的末端位 y i-f ( x
i
) ≤ε+ξ 
i
，
姿变换矩阵dT为： ξ i≥0，ξ  i≥0，i=1，2，⋯，m
dT=T r-T n=σT
n
， （4）
式中，C为惩罚因子，用于平衡最小化间隔和控
式中，σ为工业机器人末端相对于基坐标系的微
制损失，且直接影响最小化函数式（8）求解模型
分变换矩阵，如式（5）所示：
权重w和偏置b的过程。C值越大表明对训练数
■|| 0 -δz δy dx■|| 据的拟合能力越强。但C值过大，可能会导致过
|| ||
σ= ■
■
| || |δ
0
r d
1
■
■
|||| =
|
| || |
|-
δ
δ
z
y δ
0
x
-
0
δx d
d
y
z|
||
|
. （5） 拟
因
合
此
现
，本
象
文
，相
通
反
过交
C值
叉
过
验
小
证
，
等
会
方
导
法
致
来
欠
找
拟
到
合
最
问
佳
题
的
。
|| ||
■ 0 0 0 1 ■ C值。
将式（5）中矩阵写成向量形式，如式（6）所 通过拉格朗日乘子法将问题转化为SVR模
示。从而计算出机器人的末端位姿误差。 型对偶问题，经过求解可获得 SVR 特征函数
e=[d
x
d
y
d
z
δ
x
δ
y
δ z]T. （6） 如式（9）：
m
( ) ( ) ( )
3 基 于 的 工 业 机 器 人 误 差
f x =∑ α  i-α
i
K x
i
，x +b， （9）
SVR i=1
( )
拟合 其中：α  i 和α i 为拉格朗日算子；K x i ，x 为核函
数，K
(
x
i
，x
)
=ϕ
(
x
i
)Tϕ (
x
)
。SVR核函数只需
与基于模型的机器人标定方法相比，基于非 满足Mercer定理即可，无需确定ϕ ( x ) 具体形式。
模型的机器人标定方法能够综合地考虑工业机 研究表明径向基（Radial Basis Function，RBF）核
器人多种误差因素，避免了复杂的误差建模过 函数对于复杂的非线性关系具有较好的拟合能
程。其中误差预测过程为非模型标定方法的关 力，因此本文选用RBF核函数进行数据的非线性
键步骤，通过构建机器人关节角与位姿误差之间 处理。RBF核函数如式（10）：
( )
的映射模型实现机器人误差预测。鉴于SVR在 K ( x
i
，x ) =exp -γ‖x i-x‖2 ， γ>0，（10）
处理小样本、非线性回归和高维数据方面具有优
1
越的表现，可通过核函数将输入数据映射到高维
其中：γ是RBF核函数的带宽参数，γ=
2σ2
，控制
特征空间，从而允许模型拟合复杂的非线性关 高斯核函数的局部作用范围，γ过大，则σ过小，
系，可以更好地处理机器人位姿误差数据。因 会造成过拟合，导致泛化能力较差。
此，本文采用SVR实现工业机器人空间误差的 考虑到六自由度串联机器人的逆解计算存
预测。 在奇异点的问题，SVR算法数据集以机器人的关

==================================================


=== 第 5 页 ===

第 18 期 乔贵方，等：基于支持向量回归的工业机器人空间误差预测 2787
节角数据作为输入向量x
i
，机器人实际位姿误差 δα，δβ，δγ）分别取数据集最大最小值进行归
数据作为输出量y。此外，由于工业机器人的位 一化。
i
姿误差与结构参数之间具有强耦合的互相影响
关系，同时也与空间位姿相关，本文SVR方法以 4 实验结果分析
六关节角作为输入样本特征x i=（θ
1i
，θ
2i
，θ
3i
，θ
4i
，
θ ，θ ），以单输出的形式进行模型预测，针对机 4.1 工业机器人位姿测量与数据处理
5i 6i
器人位姿误差（dx，dy，dz，δα，δβ，δγ）分别进行模 机器人标定系统利用Leica AT960激光跟踪
型训练和预测，以提高算法预测的可靠性。SVR 仪对Staubli TX60工业机器人进行位姿数据测
模型结构如图3所示。 量。实验中以Staubli TX60工业机器人的基坐
标系为参考坐标系，在机器人前侧选取边长为
1 000 mm的立方体范围中，通过RoboDyn软件
随机生成机器人可达的2 000个位姿点，位姿点
的空间分布如图4所示，并将数据集划分为训练
集和测试集，其中训练集占 80%，测试集占
20%。
图3 SVR模型结构
Fig.3 Structure of SVR model
SVR模型的惩罚因子C与核函数带宽参数
γ影响该模型的预测性能。本文使用网格搜索交
叉验证方法（Grid Search Cross-Validation Meth⁃
od）寻找最佳C，γ参数值。首先，通过设置网格
搜索区间和步长生成一组等间隔的c和g值；其
次，分别以c和g为指数，3为底数，定义C和γ参
数值的搜索网格。然后，通过k折交叉验证方法
（取k=5），将训练集分为k折，取其中一折为验证
集，其余为训练集，进行k次迭代，对搜索网格中
的每对C，γ进行交叉验证，计算SVR模型的平
均验证误差；最后，确定使平均验证误差最小的
图4 Staubli TX60工业机器人位姿测量点空间分布
C，γ作为最优模型参数。
Fig.4 Spatial distribution of pose measurement points for
为提高SVR机器人误差预测的准确度并消
Staubli TX60 industrial robot
除位姿量纲不同的影响，本文采用线性归一化方
法对输入样本关节角x i=（θ
1i
，θ
2i
，θ
3i
，θ
4i
，θ
5i
，θ
6i
）
根据式（5）~式（6）计算Staubli TX60工业机
及目标值实际位姿误差（dx，dy，dz，δα，δβ，δγ）进
器人在各位姿点的误差，并由欧氏距离计算其综
行归一化数据处理，将原始数据映射至［v ，v ］
min max 合误差，位置综合误差与姿态综合误差计算公式
区间内，线性归一化计算公式如式（11）：
如式（12）和式（13）：
( ) ( )
v -v × u-u
u′= max u min -u min +v min ， （11） ΔP= (dx2+dy2+dz2)， （12）
max min
其中：u为待归一化的数据，u ，u 分别表示u ΔA= (δx2+δy2+δz2). （13）
max min
的最大值、最小值，其中［v ，v ］区间取［-1， 由数据集位姿点综合误差分析可得，其平均
min max
1］，将数据映射至［-1，1］区间内。归一化过程 位置误差和平均姿态误差分别为0.706 1 mm和
对样本特征θ 1i~θ 6i 以及预测目标值（dx，dy，dz， 0.174 2°，最大位置误差和最大姿态误差分别为

==================================================


=== 第 6 页 ===

2788 光学 精密工程 第 32 卷
1.194 7 mm和0.280 4°。
4.2 基于SVR的机器人空间误差预测
基于训练集结合本文提出的 SVR方法对
Staubli TX60工业机器人进行误差拟合。机器人
平均位置误差和平均姿态误差分别由补偿前的
（0.708 1 mm，0.176 2°）降低至（0.038 1 mm，
0.024 5°），位置误差降低了94.62%，姿态误差降
低了86.10%；最大位置误差和最大姿态误差也
分别由补偿前的（1.223 5 mm，0.302 7°）降低至
（0.155 2 mm，0.147 7°），误 差 分 别 降 低 了
87.31%和51.21%。
图6 Staubli TX60机器人姿态误差补偿
基于训练优化后的SVR模型，对测试集进
Fig.6 Attitude error compensation for Staubli TX60 robot
行误差拟合以验证本文基于SVR的机器人空间
误差预测方法的有效性。测试机器人平均位置 4.3 对比分析实验
误差和平均姿态误差分别由补偿前的（0.706 1 为进一步验证本文所述基于SVR的机器人
mm，0.174 2°）降低至（0.055 6 mm，0.024 6°），位 空间误差预测方法的有效性，与传统的BP神经
置 误 差 降 低 了 92.12%，姿 态 误 差 降 低 了 网络、Elman神经网络以及LM优化算法进行实
85.89%；最大位置误差和最大姿态误差也分别 验对比，BP神经网络和Elman神经网络参数设
由补偿前的（1.194 7 mm，0.280 4°）降低至 置初始学习率为0.001，迭代训练次数为1 000，
（0.152 3 mm，0.107 3°），误 差 分 别 降 低 了 神经网络结构设置如表2所示。传统LM优化算
87.25%和61.73%。 法一般选用 50点即可实现辨识，因此本文从
基于SVR方法进行机器人空间误差拟合前 2 000点数据集中选择50点用作辨识，通过测试
后机器人位置与姿态平均误差对比结果如图5~ 集数据进行验证。实验结果如表3所示。图8、
图6所示。由图可知，本文所述基于SVR的机器
图9为4种方法测试集位置误差与姿态误差补偿
效果对比。
人空间误差预测方法能够准确地对机器人位置
及姿态误差进行拟合，有效降低机器人的位置误
表2 神经网络参数设置
差和姿态误差。
Tab.2 Neural network parameter settings
Number Inputs Middle Outputs
of layer Layer Layer Layer
BP 4 6 20/20 6
Elman 4 6 20/20 6
由图7~图8与表3实验结果分析可知，基于
SVR的机器人空间误差预测方法对于机器人误
差预补偿效果明显，且对于位置误差与姿态误差
的降低效果能够同时达到80%以上，具有很好的
均衡性。由图7位置误差拟合效果对比图可以明
显看出，基于SVR方法和基于BP、Elman神经网
络的非模型标定方法在误差降低效果上明显优
图5 Staubli TX60机器人位置误差补偿
Fig.5 Position error compensation for Staubli TX60 robot
于基于误差模型的LM几何参数标定方法。在

==================================================


=== 第 7 页 ===

第 18 期 乔贵方，等：基于支持向量回归的工业机器人空间误差预测 2789
表3 机器人位姿误差补偿对比实验结果
Tab.3 Comparison experimental results of robot pose error compensation
Average error Maximum error Standard deviation of error
Model
Position/mm Attitude/（°） Position/mm Attitude/（°） Position/mm Attitude/（°）
Origin 0.706 1 0.174 2 1.194 7 0.280 4 0.276 0 0.051 0
BP 0.052 6 0.057 5 0.136 8 0.134 0 0.024 1 0.024 5
Elman 0.051 7 0.059 0 0.140 4 0.145 3 0.023 4 0.026 9
LM 0.196 8 0.065 9 0.587 6 0.201 8 0.083 2 0.037 5
SVR 0.055 6 0.024 6 0.152 3 0.107 3 0.023 9 0.017 5
SVR方法对姿态平均误差的降低效果较BP神经
网络有效提升了57.27%，较Elman神经网络有
效 提 升 了 58.35%，较 LM 算 法 有 效 提 升 了
62.69%；SVR方法对最大姿态误差的降低效果
较BP神经网络有效提升了19.89%，较Elman神
经网络有效提升了26.15%，较LM算法有效提
升了46.82%。由此验证了本文基于SVR的机
器人空间误差预测方法的有效性和均衡性，对机
器人位置和姿态误差降低效果的均衡性上相较
于BP、Elman神经网络有明显优势，姿态精度的
稳定性也有明显提升。
图7 4种方法位置误差拟合效果对比
Fig.7 Comparison of four methods for fitting position errors 5 结 论
工业机器人的位姿误差与结构参数之间具
有强耦合的互相影响关系，同时也与空间位姿相
关。针对该问题，本文重点研究了基于SVR的
机器人空间误差预测方法。采用SVR模型进行
机器人实际位姿误差预测与补偿，能够较好地对
机器人误差数据进行非线性映射。实验结果表
明，机器人平均综合位置误差和平均综合姿态误
差分别由补偿前的（0.706 1 mm，0.174 2°）降低至
（0.055 6 mm，0.024 6°），位 置 误 差 降 低 了
92.12%，姿态误差降低了85.89%；最后，通过与
BP、Elman神经网络以及传统LM几何参数标定
图8 4种方法姿态误差拟合效果对比
Fig.8 Comparison of four methods for fitting attitude errors
方法进行实验对比，误差降低效果优于传统LM
几何参数标定方法，且误差预测补偿的效果优于
位置精度提升方面，SVR方法与BP、Elman神经 传统的BP，Elman神经网络算法，同时验证了本
网络在提升效果上没有明显差异。在姿态误差 文基于SVR模型对机器人位置和姿态误差降低
降低方面，SVR方法相比于其他三个算法最优， 效果的有效性和均衡性。

==================================================


=== 第 8 页 ===

2790 光学 精密工程 第 32 卷
参考文献： ZHOU W， LIAO W H， TIAN W. Theory and ex⁃
periment of industrial robot accuracy compensation
［1］ WANG Z Y， LIU Y， WANG X Y， et al. Field cal⁃
method based on spatial interpolation［J］. Journal of
ibration method for industrial robots based on single
Mechanical Engineering， 2013， 49（3）： 42-48.（in
position sensitive device［J］. IEEE Transactions on
Chinese）
Instrumentation and Measurement， 2088， 72：
［9］ CHEN D D， YUAN P J， WANG T M， et al. A
7506112.
compensation method based on error similarity and
［2］ 温秀兰， 宋爱国， 冯月贵， 等. 基于最优位姿集的
error correlation to enhance the position accuracy of
机器人标定及不确定度评定［J］. 仪器仪表学报，
an aviation drilling robot［J］. Measurement Science
2022， 43（9）： 276-283.
and Technology， 2018， 29（8）： 085011.
WEN X L， SONG A G， FENG Y G， et al. Robot
［10］ ZHANG Y， QIAO G F， SONG G M， et al. Ex⁃
calibration and uncertainty evaluation based on opti⁃
perimental analysis on the effectiveness of kinemat⁃
mal pose se［t J］. Chinese Journal of Scientific Instru⁃
ic error compensation methods for serial industrial
ment， 2022， 43（9）： 276-283.（in Chinese）
robots［J］. Mathematical Problems in Engineer⁃
［3］ 姜一舟， 于连栋， 常雅琪， 等. 基于改进差分进化
ing， 2021， 2021： 8086389.
算法的机器人运动学参数标定［J］. 光学 精密工
［11］ GAO T C， MENG F， ZHANG X Y， et al. An
程， 2021， 29（7）： 1580-1588.
operational calibration approach of industrial robots
JIANG Y Z， YU L D， CHANG Y Q， et al. Robot
through a motion capture system and an artificial
calibration based on modified differential evolution al⁃
gorithm［J］. Opt. Precision Eng.， 2021， 29（7）： neural network ELM［J］. The International Jour⁃
1580-1588.（in Chinese）
nal of Advanced Manufacturing Technology，
［4］ XIANG T W， JIANG X Y， QIAO G F， et al. Ki⁃
2023， 125（11）： 5135-5147.
［12］ MIN K， NI F L， CHEN Z Y， et al. A robot posi⁃
nematics parameter calibration of serial industrial ro⁃
bots based on partial pose measurement［J］. Mathe⁃ tional error compensation method based on im⁃
matics， 2023， 11（23）： 4802. proved Kriging interpolation and kronecker products
［5］ 温秀兰， 康传帅， 宋爱国， 等. 基于全位姿测量优
［J］. IEEE Transactions on Industrial Electronics，
化的机器人精度研究［J］. 仪器仪表学报， 2019， 40
2024， 71（4）： 3884-3893.
（7）： 81-89. ［13］ SHANGHAI B H， DU SHANGHAI W， YU T.
WEN X L， KANG C S， SONG A G， et al. Study Research on positioning error compensation of in⁃
dustrial robot based on bp neural network［C］.
on robot accuracy based on full pose measurement
and optimization［J］. Chinese Journal of Scientific 2021 International Conference on Information Sci⁃
Instrument， 2019， 40（7）： 81-89.（in Chinese） ence， Parallel and Distributed Systems （ISPDS）.
［6］ 高贯斌，牛锦鹏，刘飞等. 基于各向异性误差相似度 Hangzhou， China. IEEE， 2021： 131-135.
的六自由度机器人定位误差补偿［J］. 光学 精密工 ［14］ WANG W， TIAN W， LIAO W H， et al. Error
程，2022，30（16）：1955-1967. compensation of industrial robot based on deep be⁃
GAO G， NIU J， LIU F， et al. Positioning error lief network and error similarity［J］. Robotics and
compensation of 6-DOF robots based on anisotropic Computer-Integrated Manufacturing， 2022， 73：
error similarity［J］. Opt. Precision Eng.， 2022， 30 102220.
（16）：1955-1967. ［15］ HU J S， HUA F F， TIAN W. Robot positioning
［7］ HUANG P， HUANG H Z， LI Y F， et al. Posi⁃ error compensation method based on deep neural
tioning accuracy reliability analysis of industrial ro⁃
network［J］. Journal of Physics： Conference Se⁃
bots based on differential kinematics and saddle point ries， 2020， 1487（1）： 012045.
approximation ［J］. Mechanism and Machine ［16］ 乔贵方， 蒋欣怡， 高春晖， 等. 基于多目标优化的
Theory， 2021， 162： 104367. 工业机器人位置与姿态精度提升方法［J］. 仪器仪
［8］ 周炜， 廖文和， 田威. 基于空间插值的工业机器人 表学报， 2023， 44（12）： 217-224.
精度补偿方法理论与试验［J］. 机械工程学报， QIAO G F， JIANG X Y， GAO C H， et al. Meth⁃
2013， 49（3）： 42-48. od for improving position and attitude accuracy of

==================================================


=== 第 9 页 ===

第 18 期 乔贵方，等：基于支持向量回归的工业机器人空间误差预测 2791
industrial robots based on multi-objective optimiza⁃ 2021， 29（4）： 763-771.
tion［J］. Chinese Journal of Scientific Instrument， QIAO G F， LÜ Z Y， ZHANG Y， et al. Improve⁃
2023， 44（12）： 217-224.（in Chinese） ment of robot kinematic accuracy based on BAS-
［17］ 乔贵方， 吕仲艳， 张颖， 等. 基于BAS-PSO算法 PSO algorithm［J］. Opt. Precision Eng.， 2021，
的机器人定位精度提升［J］. 光学 精密工程， 29（4）： 763-771.（in Chinese）
作者简介：
乔贵方（1987-），男，江苏新沂人，博
士，副教授，硕士生导师，2010年于南 高春晖（1998-），男，山东临沂人，硕
京工程学院获得学士学位，2015年于 士研究生，2021年于山东理工大学获
东南大学获得博士学位，主要研究方 得学士学位,研究方向为机器人标定
向为机器人测试与标定、机器人仿生 技术。E-mail：<EMAIL>
控制技术等。E-mail：qiaoguifang@
126.com

==================================================
