#!/usr/bin/env python3
"""
Ariel Data Challenge 2025 - 冠军级解决方案
基于Kaggle获奖经验的系外行星光谱预测解决方案

核心技术栈：
1. 高级特征工程：时频域分析、小波变换、统计特征、物理特征
2. 多层集成学习：Stacking + Blending + Bayesian Optimization
3. 深度学习：Transformer + CNN + LSTM + Attention机制
4. 数据增强：TTA、伪标签、Mixup、CutMix
5. 高级CV策略：GroupKFold + Adversarial Validation
6. 后处理：光谱平滑、物理约束、不确定性校准
"""

import os
import gc
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from tqdm import tqdm
import joblib
import random
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from collections import defaultdict
import json
import pickle

# 科学计算库
import scipy
from scipy import signal, stats, fft, interpolate
from scipy.optimize import minimize
from sklearn.preprocessing import StandardScaler, RobustScaler, QuantileTransformer, PowerTransformer
from sklearn.decomposition import PCA, TruncatedSVD, FastICA, NMF
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.model_selection import KFold, StratifiedKFold, GroupKFold, train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet, BayesianRidge
from sklearn.neural_network import MLPRegressor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.svm import SVR

# 高级机器学习库
try:
    import xgboost as xgb
    XGB_AVAILABLE = True
except ImportError:
    XGB_AVAILABLE = False

try:
    import lightgbm as lgb
    LGB_AVAILABLE = True
except ImportError:
    LGB_AVAILABLE = False

try:
    import catboost as cb
    CB_AVAILABLE = True
except ImportError:
    CB_AVAILABLE = False

try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False

# 深度学习库
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset, Dataset
    from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
    TORCH_AVAILABLE = True
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
except ImportError:
    TORCH_AVAILABLE = False
    device = 'cpu'

# 信号处理库
try:
    import pywt
    PYWT_AVAILABLE = True
except ImportError:
    PYWT_AVAILABLE = False

# 可视化库
import matplotlib.pyplot as plt
import seaborn as sns

warnings.filterwarnings('ignore')

# 全局配置
@dataclass
class Config:
    """全局配置类"""
    # 数据路径
    data_path: str = "/kaggle/input/ariel-data-challenge-2025"
    output_dir: str = "./models"

    # 随机种子
    seed: int = 42

    # 特征工程参数
    n_components_pca: int = 200
    n_components_svd: int = 150
    n_components_ica: int = 100

    # 模型参数
    n_folds: int = 5  # 减少折数加速训练
    n_spectrum_samples: int = 50  # 减少光谱点数量加速训练

    # 深度学习参数
    batch_size: int = 512  # 增大batch size充分利用GPU
    epochs: int = 100  # 减少epochs加速训练
    learning_rate: float = 0.001

    # 集成参数
    n_models_level1: int = 15  # 减少模型数量加速训练
    n_models_level2: int = 5

    # TTA参数
    n_tta: int = 3

    # 优化参数
    n_trials: int = 50

    # GPU优化参数
    use_gpu: bool = True
    gpu_batch_size: int = 1024
    num_workers: int = 4
    pin_memory: bool = True

def set_seed(seed: int):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    if TORCH_AVAILABLE:
        torch.manual_seed(seed)
        torch.cuda.manual_seed(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

class AdvancedFeatureEngineer:
    """高级特征工程类"""
    
    def __init__(self, config: Config):
        self.config = config
        self.scalers = {}
        self.decomposers = {}
        self.feature_selectors = {}
        
    def extract_statistical_features(self, data: np.ndarray, prefix: str) -> Dict[str, float]:
        """提取统计特征"""
        features = {}
        
        # 基础统计量
        features[f'{prefix}_mean'] = np.mean(data)
        features[f'{prefix}_std'] = np.std(data)
        features[f'{prefix}_var'] = np.var(data)
        features[f'{prefix}_median'] = np.median(data)
        features[f'{prefix}_min'] = np.min(data)
        features[f'{prefix}_max'] = np.max(data)
        features[f'{prefix}_range'] = np.ptp(data)
        features[f'{prefix}_sum'] = np.sum(data)
        
        # 百分位数
        for p in [5, 10, 25, 75, 90, 95]:
            features[f'{prefix}_p{p}'] = np.percentile(data, p)
        
        # 高阶统计量
        features[f'{prefix}_skew'] = stats.skew(data)
        features[f'{prefix}_kurtosis'] = stats.kurtosis(data)
        features[f'{prefix}_entropy'] = stats.entropy(np.histogram(data, bins=50)[0] + 1e-10)
        
        # 变异系数
        features[f'{prefix}_cv'] = features[f'{prefix}_std'] / (features[f'{prefix}_mean'] + 1e-10)
        
        # 峰值特征
        features[f'{prefix}_peak_count'] = len(signal.find_peaks(data)[0])
        
        return features
    
    def extract_frequency_features(self, data: np.ndarray, prefix: str, fs: float = 1.0) -> Dict[str, float]:
        """提取频域特征"""
        features = {}
        
        # FFT特征
        fft_data = np.abs(fft.fft(data))
        freqs = fft.fftfreq(len(data), 1/fs)
        
        # 功率谱密度
        features[f'{prefix}_psd_mean'] = np.mean(fft_data)
        features[f'{prefix}_psd_std'] = np.std(fft_data)
        features[f'{prefix}_psd_max'] = np.max(fft_data)
        
        # 主频率
        dominant_freq_idx = np.argmax(fft_data[1:len(fft_data)//2]) + 1
        features[f'{prefix}_dominant_freq'] = freqs[dominant_freq_idx]
        
        # 频谱质心
        features[f'{prefix}_spectral_centroid'] = np.sum(freqs[:len(freqs)//2] * fft_data[:len(fft_data)//2]) / np.sum(fft_data[:len(fft_data)//2])
        
        # 频谱带宽
        centroid = features[f'{prefix}_spectral_centroid']
        features[f'{prefix}_spectral_bandwidth'] = np.sqrt(np.sum(((freqs[:len(freqs)//2] - centroid) ** 2) * fft_data[:len(fft_data)//2]) / np.sum(fft_data[:len(fft_data)//2]))
        
        return features
    
    def extract_wavelet_features(self, data: np.ndarray, prefix: str) -> Dict[str, float]:
        """提取小波特征"""
        features = {}
        
        if not PYWT_AVAILABLE:
            return features
        
        # 小波变换
        wavelets = ['db4', 'haar', 'coif2']
        for wavelet in wavelets:
            try:
                coeffs = pywt.wavedec(data, wavelet, level=4)
                for i, coeff in enumerate(coeffs):
                    features[f'{prefix}_{wavelet}_level{i}_energy'] = np.sum(coeff ** 2)
                    features[f'{prefix}_{wavelet}_level{i}_mean'] = np.mean(np.abs(coeff))
                    features[f'{prefix}_{wavelet}_level{i}_std'] = np.std(coeff)
            except:
                continue
        
        return features
    
    def extract_time_series_features(self, data: np.ndarray, prefix: str) -> Dict[str, float]:
        """提取时间序列特征"""
        features = {}
        
        # 趋势特征
        x = np.arange(len(data))
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, data)
        features[f'{prefix}_trend_slope'] = slope
        features[f'{prefix}_trend_r2'] = r_value ** 2
        features[f'{prefix}_trend_p_value'] = p_value
        
        # 自相关特征
        autocorr = np.correlate(data, data, mode='full')
        autocorr = autocorr[autocorr.size // 2:]
        autocorr = autocorr / autocorr[0]
        
        # 找到第一个零交叉点
        zero_crossings = np.where(np.diff(np.signbit(autocorr)))[0]
        if len(zero_crossings) > 0:
            features[f'{prefix}_first_zero_crossing'] = zero_crossings[0]
        else:
            features[f'{prefix}_first_zero_crossing'] = len(autocorr)
        
        # 差分特征
        diff1 = np.diff(data)
        diff2 = np.diff(diff1)
        
        features[f'{prefix}_diff1_mean'] = np.mean(diff1)
        features[f'{prefix}_diff1_std'] = np.std(diff1)
        features[f'{prefix}_diff2_mean'] = np.mean(diff2)
        features[f'{prefix}_diff2_std'] = np.std(diff2)
        
        # 变化点检测
        cumsum = np.cumsum(data - np.mean(data))
        features[f'{prefix}_cumsum_max'] = np.max(cumsum)
        features[f'{prefix}_cumsum_min'] = np.min(cumsum)
        features[f'{prefix}_cumsum_range'] = features[f'{prefix}_cumsum_max'] - features[f'{prefix}_cumsum_min']
        
        return features
    
    def extract_physical_features(self, data: np.ndarray, wavelengths: np.ndarray, prefix: str) -> Dict[str, float]:
        """提取物理特征"""
        features = {}
        
        # 光谱斜率
        slope, _ = np.polyfit(wavelengths, data, 1)
        features[f'{prefix}_spectral_slope'] = slope
        
        # 光谱曲率
        curvature = np.polyfit(wavelengths, data, 2)[0]
        features[f'{prefix}_spectral_curvature'] = curvature
        
        # 吸收线特征
        # 寻找局部最小值作为吸收线
        minima_indices = signal.argrelmin(data, order=5)[0]
        if len(minima_indices) > 0:
            features[f'{prefix}_absorption_lines_count'] = len(minima_indices)
            features[f'{prefix}_deepest_absorption'] = np.min(data[minima_indices])
            features[f'{prefix}_absorption_depth_mean'] = np.mean(data[minima_indices])
            features[f'{prefix}_absorption_depth_std'] = np.std(data[minima_indices])
        else:
            features[f'{prefix}_absorption_lines_count'] = 0
            features[f'{prefix}_deepest_absorption'] = 0
            features[f'{prefix}_absorption_depth_mean'] = 0
            features[f'{prefix}_absorption_depth_std'] = 0
        
        # 发射线特征
        maxima_indices = signal.argrelmax(data, order=5)[0]
        if len(maxima_indices) > 0:
            features[f'{prefix}_emission_lines_count'] = len(maxima_indices)
            features[f'{prefix}_highest_emission'] = np.max(data[maxima_indices])
            features[f'{prefix}_emission_height_mean'] = np.mean(data[maxima_indices])
            features[f'{prefix}_emission_height_std'] = np.std(data[maxima_indices])
        else:
            features[f'{prefix}_emission_lines_count'] = 0
            features[f'{prefix}_highest_emission'] = 0
            features[f'{prefix}_emission_height_mean'] = 0
            features[f'{prefix}_emission_height_std'] = 0
        
        # 连续谱特征
        # 使用多项式拟合去除吸收/发射线，得到连续谱
        continuum = np.polyval(np.polyfit(wavelengths, data, 3), wavelengths)
        normalized_spectrum = data / (continuum + 1e-10)
        
        features[f'{prefix}_continuum_level'] = np.mean(continuum)
        features[f'{prefix}_continuum_slope'] = np.polyfit(wavelengths, continuum, 1)[0]
        features[f'{prefix}_normalized_std'] = np.std(normalized_spectrum)
        
        return features


class ArielDataProcessor:
    """高级数据处理器"""

    def __init__(self, config: Config):
        self.config = config
        self.data_path = Path(config.data_path)
        self.train_path = self.data_path / "train"
        self.test_path = self.data_path / "test"
        self.feature_engineer = AdvancedFeatureEngineer(config)

        # 加载元数据
        self.load_metadata()

    def load_metadata(self):
        """加载元数据"""
        print("加载元数据...")

        # 加载训练数据真实光谱
        self.train_spectra = pd.read_csv(self.data_path / "train.csv")
        print(f"训练光谱数据形状: {self.train_spectra.shape}")

        # 加载波长信息
        self.wavelengths = pd.read_csv(self.data_path / "wavelengths.csv")
        self.wavelength_values = self.wavelengths.iloc[0].values
        print(f"波长数据形状: {self.wavelengths.shape}")

        # 加载轴信息
        self.axis_info = pd.read_parquet(self.data_path / "axis_info.parquet")
        print(f"轴信息形状: {self.axis_info.shape}")

        # 加载ADC信息
        self.adc_info = pd.read_csv(self.data_path / "adc_info.csv")
        print(f"ADC信息形状: {self.adc_info.shape}")

        # 加载星球信息
        self.train_star_info = pd.read_csv(self.data_path / "train_star_info.csv")
        self.test_star_info = pd.read_csv(self.data_path / "test_star_info.csv")
        print(f"训练星球信息形状: {self.train_star_info.shape}")
        print(f"测试星球信息形状: {self.test_star_info.shape}")

    def restore_dynamic_range(self, data: np.ndarray, instrument: str) -> np.ndarray:
        """恢复数据的动态范围"""
        try:
            if instrument == 'AIRS-CH0':
                gain = self.adc_info['AIRS-CH0_adc_gain'].iloc[0]
                offset = self.adc_info['AIRS-CH0_adc_offset'].iloc[0]
            elif instrument == 'FGS1':
                gain = self.adc_info['FGS1_adc_gain'].iloc[0]
                offset = self.adc_info['FGS1_adc_offset'].iloc[0]
            else:
                return data.astype(np.float64)

            # 恢复动态范围
            restored_data = (data.astype(np.float64) / gain) + offset
            return restored_data

        except Exception as e:
            print(f"恢复动态范围时出错 {instrument}: {e}")
            return data.astype(np.float64)

    def process_planet_data_fast(self, planet_id: Union[str, int], is_train: bool = True) -> Optional[Dict]:
        """快速处理单个行星的数据（GPU优化版本）"""
        planet_path = self.train_path if is_train else self.test_path
        planet_dir = planet_path / str(planet_id)

        if not planet_dir.exists():
            return None

        features = {'planet_id': planet_id}

        try:
            # 处理AIRS-CH0数据（只处理第一个文件以加速）
            airs_files = list(planet_dir.glob("AIRS-CH0_signal_*.parquet"))
            if airs_files:
                airs_file = airs_files[0]  # 只处理第一个文件
                airs_data = pd.read_parquet(airs_file).values
                # 恢复动态范围
                airs_data = self.restore_dynamic_range(airs_data, 'AIRS-CH0')

                # 快速特征提取（降采样）
                if airs_data.shape[0] > 1000:
                    # 降采样以加速处理
                    step = airs_data.shape[0] // 1000
                    airs_data = airs_data[::step]

                airs_data = airs_data.reshape(-1, 32, 356)

                # 快速特征提取
                airs_features = self.extract_fast_features(airs_data, 'AIRS_CH0')
                features.update(airs_features)

            # 处理FGS1数据（只处理第一个文件以加速）
            fgs1_files = list(planet_dir.glob("FGS1_signal_*.parquet"))
            if fgs1_files:
                fgs1_file = fgs1_files[0]  # 只处理第一个文件
                fgs1_data = pd.read_parquet(fgs1_file).values
                # 恢复动态范围
                fgs1_data = self.restore_dynamic_range(fgs1_data, 'FGS1')

                # 快速特征提取（降采样）
                if fgs1_data.shape[0] > 1000:
                    # 降采样以加速处理
                    step = fgs1_data.shape[0] // 1000
                    fgs1_data = fgs1_data[::step]

                fgs1_data = fgs1_data.reshape(-1, 32, 32)

                # 快速特征提取
                fgs1_features = self.extract_fast_features(fgs1_data, 'FGS1')
                features.update(fgs1_features)

            # 添加星球物理参数
            star_info = self.train_star_info if is_train else self.test_star_info
            planet_star_info = star_info[star_info['planet_id'] == planet_id]
            if not planet_star_info.empty:
                star_features = planet_star_info.iloc[0].to_dict()
                del star_features['planet_id']  # 避免重复
                features.update(star_features)

                # 添加快速物理特征工程
                physical_features = self.engineer_physical_features_fast(star_features)
                features.update(physical_features)

        except Exception as e:
            print(f"处理行星数据时出错 {planet_id}: {e}")
            return None

        return features

    def extract_fast_features(self, signal_data: np.ndarray, instrument: str) -> Dict[str, float]:
        """快速特征提取"""
        features = {}
        prefix = instrument

        if TORCH_AVAILABLE and self.config.use_gpu:
            # GPU加速版本
            signal_tensor = torch.from_numpy(signal_data).float().to(device)

            # 基础统计量
            features[f'{prefix}_mean'] = torch.mean(signal_tensor).cpu().item()
            features[f'{prefix}_std'] = torch.std(signal_tensor).cpu().item()
            features[f'{prefix}_min'] = torch.min(signal_tensor).cpu().item()
            features[f'{prefix}_max'] = torch.max(signal_tensor).cpu().item()

            # 时间维度特征
            if len(signal_data.shape) == 3:
                temporal_signal = torch.mean(signal_tensor, dim=(1, 2))
                features[f'{prefix}_temporal_std'] = torch.std(temporal_signal).cpu().item()
                features[f'{prefix}_temporal_range'] = (torch.max(temporal_signal) - torch.min(temporal_signal)).cpu().item()

            # 清理GPU内存
            del signal_tensor
            if len(signal_data.shape) == 3:
                del temporal_signal
            torch.cuda.empty_cache()
        else:
            # CPU版本
            features[f'{prefix}_mean'] = np.mean(signal_data)
            features[f'{prefix}_std'] = np.std(signal_data)
            features[f'{prefix}_min'] = np.min(signal_data)
            features[f'{prefix}_max'] = np.max(signal_data)

            if len(signal_data.shape) == 3:
                temporal_signal = np.mean(signal_data, axis=(1, 2))
                features[f'{prefix}_temporal_std'] = np.std(temporal_signal)
                features[f'{prefix}_temporal_range'] = np.ptp(temporal_signal)

        return features

    def engineer_physical_features_fast(self, star_features: Dict) -> Dict[str, float]:
        """快速物理特征工程"""
        features = {}

        # 基础物理量
        Rs = star_features.get('Rs', 1.0)
        Ms = star_features.get('Ms', 1.0)
        Ts = star_features.get('Ts', 5778.0)
        Mp = star_features.get('Mp', 1.0)
        P = star_features.get('P', 365.25)
        sma = star_features.get('sma', 1.0)

        # 关键派生特征
        features['stellar_density'] = Ms / (Rs ** 3)
        features['orbital_velocity'] = 2 * np.pi * sma * Rs / P
        features['stellar_luminosity'] = Rs ** 2 * (Ts / 5778) ** 4
        features['equilibrium_temp'] = Ts * np.sqrt(Rs / (2 * sma * Rs))
        features['mass_ratio'] = Mp / Ms
        features['period_years'] = P / 365.25

        return features

    def extract_comprehensive_features(self, signal_data: np.ndarray, instrument: str,
                                     observation_idx: int = 0) -> Dict[str, float]:
        """提取综合特征（GPU加速版本）"""
        prefix = f'{instrument}_{observation_idx}'
        features = {}

        # 对于3D数据，使用GPU加速计算
        if len(signal_data.shape) == 3:
            # 使用GPU加速的统计计算
            if TORCH_AVAILABLE and self.config.use_gpu:
                signal_tensor = torch.from_numpy(signal_data).float().to(device)

                # 全局统计量（GPU加速）
                global_mean = torch.mean(signal_tensor).cpu().item()
                global_std = torch.std(signal_tensor).cpu().item()
                global_min = torch.min(signal_tensor).cpu().item()
                global_max = torch.max(signal_tensor).cpu().item()

                features[f'{prefix}_global_mean'] = global_mean
                features[f'{prefix}_global_std'] = global_std
                features[f'{prefix}_global_min'] = global_min
                features[f'{prefix}_global_max'] = global_max
                features[f'{prefix}_global_range'] = global_max - global_min

                # 时间维度统计量（GPU加速）
                temporal_signal = torch.mean(signal_tensor, dim=(1, 2)).cpu().numpy()

                # 空间维度统计量（GPU加速）
                spatial_mean = torch.mean(signal_tensor, dim=0)
                spatial_std = torch.std(spatial_mean).cpu().item()
                spatial_max = torch.max(spatial_mean).cpu().item()

                features[f'{prefix}_spatial_std'] = spatial_std
                features[f'{prefix}_spatial_max'] = spatial_max

                # 清理GPU内存
                del signal_tensor, spatial_mean
                torch.cuda.empty_cache()
            else:
                # CPU版本（简化）
                temporal_signal = np.mean(signal_data, axis=(1, 2))
                features[f'{prefix}_global_mean'] = np.mean(signal_data)
                features[f'{prefix}_global_std'] = np.std(signal_data)

            # 快速时间序列特征
            features[f'{prefix}_temporal_trend'] = np.polyfit(range(len(temporal_signal)), temporal_signal, 1)[0]
            features[f'{prefix}_temporal_std'] = np.std(temporal_signal)
            features[f'{prefix}_temporal_range'] = np.ptp(temporal_signal)

            # 快速频域特征
            fft_data = np.abs(np.fft.fft(temporal_signal))
            features[f'{prefix}_fft_max'] = np.max(fft_data)
            features[f'{prefix}_fft_mean'] = np.mean(fft_data)

        else:
            # 1D数据快速处理
            features[f'{prefix}_mean'] = np.mean(signal_data)
            features[f'{prefix}_std'] = np.std(signal_data)
            features[f'{prefix}_min'] = np.min(signal_data)
            features[f'{prefix}_max'] = np.max(signal_data)
            features[f'{prefix}_range'] = np.ptp(signal_data)

            # 快速统计量
            features[f'{prefix}_skew'] = stats.skew(signal_data)
            features[f'{prefix}_kurtosis'] = stats.kurtosis(signal_data)

            # 快速频域特征
            fft_data = np.abs(np.fft.fft(signal_data))
            features[f'{prefix}_fft_max'] = np.max(fft_data)

        return features

    def engineer_physical_features(self, star_features: Dict) -> Dict[str, float]:
        """工程化物理特征"""
        features = {}

        # 基础物理量
        Rs = star_features.get('Rs', 1.0)
        Ms = star_features.get('Ms', 1.0)
        Ts = star_features.get('Ts', 5778.0)
        Mp = star_features.get('Mp', 1.0)
        e = star_features.get('e', 0.0)
        P = star_features.get('P', 365.25)
        sma = star_features.get('sma', 1.0)
        i = star_features.get('i', 90.0)

        # 派生物理特征
        features['stellar_density'] = Ms / (Rs ** 3)  # 恒星密度
        features['planetary_density'] = Mp / (Rs ** 3)  # 行星密度（近似）
        features['escape_velocity'] = np.sqrt(2 * Ms / Rs)  # 逃逸速度
        features['orbital_velocity'] = 2 * np.pi * sma * Rs / P  # 轨道速度
        features['tidal_force'] = Ms / (sma * Rs) ** 3  # 潮汐力
        features['stellar_luminosity'] = Rs ** 2 * (Ts / 5778) ** 4  # 恒星光度
        features['equilibrium_temp'] = Ts * np.sqrt(Rs / (2 * sma * Rs))  # 平衡温度
        features['hill_sphere'] = sma * Rs * (Mp / (3 * Ms)) ** (1/3)  # 希尔球半径

        # 轨道特征
        features['eccentricity_factor'] = np.sqrt(1 - e**2)
        features['periastron'] = sma * Rs * (1 - e)
        features['apastron'] = sma * Rs * (1 + e)
        features['orbital_period_years'] = P / 365.25
        features['inclination_rad'] = np.radians(i)
        features['transit_probability'] = Rs / (sma * Rs)

        # 无量纲参数
        features['mass_ratio'] = Mp / Ms
        features['radius_ratio'] = 1.0 / Rs  # 假设行星半径为1地球半径
        features['period_ratio'] = P / 365.25

        return features

    def create_feature_dataset(self, planet_ids: List, is_train: bool = True) -> pd.DataFrame:
        """创建特征数据集（GPU优化版本）"""
        print(f"处理 {'训练' if is_train else '测试'} 数据...")

        all_features = []

        # 使用快速处理方法
        for planet_id in tqdm(planet_ids, desc="处理行星数据"):
            features = self.process_planet_data_fast(planet_id, is_train)
            if features:
                all_features.append(features)

            # 更频繁的内存管理
            if len(all_features) % 20 == 0:
                gc.collect()
                if TORCH_AVAILABLE:
                    torch.cuda.empty_cache()

        feature_df = pd.DataFrame(all_features)
        print(f"特征数据集形状: {feature_df.shape}")

        return feature_df


class TransformerRegressor(nn.Module):
    """Transformer回归模型"""

    def __init__(self, input_dim: int, d_model: int = 512, nhead: int = 8,
                 num_layers: int = 6, dim_feedforward: int = 2048, dropout: float = 0.1):
        super().__init__()

        self.input_projection = nn.Linear(input_dim, d_model)
        self.positional_encoding = nn.Parameter(torch.randn(1000, d_model))

        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

        self.output_projection = nn.Sequential(
            nn.Linear(d_model, dim_feedforward),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(dim_feedforward, dim_feedforward // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(dim_feedforward // 2, 1)
        )

    def forward(self, x):
        # x shape: (batch_size, seq_len, input_dim)
        seq_len = x.size(1)

        # 投影到模型维度
        x = self.input_projection(x)

        # 添加位置编码
        x = x + self.positional_encoding[:seq_len].unsqueeze(0)

        # Transformer编码
        x = self.transformer(x)

        # 全局平均池化
        x = torch.mean(x, dim=1)

        # 输出投影
        x = self.output_projection(x)

        return x.squeeze(-1)


class CNNLSTMRegressor(nn.Module):
    """CNN-LSTM混合回归模型"""

    def __init__(self, input_dim: int, cnn_channels: List[int] = [64, 128, 256],
                 lstm_hidden: int = 256, lstm_layers: int = 2, dropout: float = 0.3):
        super().__init__()

        # CNN部分
        cnn_layers = []
        in_channels = 1
        for out_channels in cnn_channels:
            cnn_layers.extend([
                nn.Conv1d(in_channels, out_channels, kernel_size=3, padding=1),
                nn.BatchNorm1d(out_channels),
                nn.ReLU(),
                nn.MaxPool1d(2),
                nn.Dropout(dropout)
            ])
            in_channels = out_channels

        self.cnn = nn.Sequential(*cnn_layers)

        # 计算CNN输出维度
        cnn_output_dim = cnn_channels[-1]

        # LSTM部分
        self.lstm = nn.LSTM(
            input_size=cnn_output_dim,
            hidden_size=lstm_hidden,
            num_layers=lstm_layers,
            batch_first=True,
            dropout=dropout if lstm_layers > 1 else 0,
            bidirectional=True
        )

        # 输出层
        self.output = nn.Sequential(
            nn.Linear(lstm_hidden * 2, lstm_hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(lstm_hidden, lstm_hidden // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(lstm_hidden // 2, 1)
        )

    def forward(self, x):
        # x shape: (batch_size, input_dim)
        # 重塑为CNN输入格式
        x = x.unsqueeze(1)  # (batch_size, 1, input_dim)

        # CNN特征提取
        x = self.cnn(x)  # (batch_size, channels, seq_len)

        # 转置为LSTM输入格式
        x = x.transpose(1, 2)  # (batch_size, seq_len, channels)

        # LSTM处理
        lstm_out, _ = self.lstm(x)

        # 取最后一个时间步的输出
        x = lstm_out[:, -1, :]

        # 输出层
        x = self.output(x)

        return x.squeeze(-1)


class AttentionRegressor(nn.Module):
    """注意力机制回归模型"""

    def __init__(self, input_dim: int, hidden_dim: int = 512, num_heads: int = 8,
                 num_layers: int = 4, dropout: float = 0.3):
        super().__init__()

        self.input_projection = nn.Linear(input_dim, hidden_dim)

        # 多头自注意力层
        self.attention_layers = nn.ModuleList([
            nn.MultiheadAttention(hidden_dim, num_heads, dropout=dropout, batch_first=True)
            for _ in range(num_layers)
        ])

        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(num_layers)
        ])

        self.feed_forwards = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim * 4),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim * 4, hidden_dim)
            ) for _ in range(num_layers)
        ])

        self.output = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, 1)
        )

    def forward(self, x):
        # x shape: (batch_size, input_dim)
        # 添加序列维度
        x = x.unsqueeze(1)  # (batch_size, 1, input_dim)

        # 输入投影
        x = self.input_projection(x)

        # 多层注意力
        for attention, layer_norm, feed_forward in zip(
            self.attention_layers, self.layer_norms, self.feed_forwards
        ):
            # 自注意力
            attn_out, _ = attention(x, x, x)
            x = layer_norm(x + attn_out)

            # 前馈网络
            ff_out = feed_forward(x)
            x = layer_norm(x + ff_out)

        # 全局平均池化
        x = torch.mean(x, dim=1)

        # 输出层
        x = self.output(x)

        return x.squeeze(-1)


class AdvancedEnsemble:
    """高级集成学习器"""

    def __init__(self, config: Config):
        self.config = config
        self.level1_models = {}
        self.level2_models = {}
        self.scalers = {}
        self.feature_selectors = {}
        self.oof_predictions = {}

    def create_level1_models(self, input_dim: int) -> Dict:
        """创建第一层模型（GPU优化版本）"""
        models = {}

        # 优先使用GPU加速的梯度提升模型
        if XGB_AVAILABLE:
            models['xgb'] = xgb.XGBRegressor(
                n_estimators=500, max_depth=6, learning_rate=0.1,
                subsample=0.8, colsample_bytree=0.8, random_state=self.config.seed,
                tree_method='gpu_hist' if torch.cuda.is_available() else 'hist',
                gpu_id=0 if torch.cuda.is_available() else None
            )

        if LGB_AVAILABLE:
            models['lgb'] = lgb.LGBMRegressor(
                n_estimators=500, max_depth=6, learning_rate=0.1,
                subsample=0.8, colsample_bytree=0.8, random_state=self.config.seed,
                device='gpu' if torch.cuda.is_available() else 'cpu',
                gpu_platform_id=0, gpu_device_id=0,
                verbose=-1
            )

        if CB_AVAILABLE:
            models['catboost'] = cb.CatBoostRegressor(
                iterations=500, depth=6, learning_rate=0.1,
                random_seed=self.config.seed,
                task_type='GPU' if torch.cuda.is_available() else 'CPU',
                devices='0' if torch.cuda.is_available() else None,
                verbose=False
            )

        # 快速传统模型
        models['rf'] = RandomForestRegressor(
            n_estimators=200, max_depth=10, random_state=self.config.seed, n_jobs=-1
        )

        models['ridge'] = Ridge(alpha=1.0)
        models['lasso'] = Lasso(alpha=0.1, max_iter=1000)

        # 深度学习模型（减少复杂度）
        if TORCH_AVAILABLE and self.config.use_gpu:
            models['neural_net'] = self.create_fast_pytorch_wrapper(input_dim)

        print(f"创建了 {len(models)} 个模型: {list(models.keys())}")
        return models

    def create_fast_pytorch_wrapper(self, input_dim: int):
        """创建快速PyTorch模型包装器"""
        class FastNeuralNet(nn.Module):
            def __init__(self, input_dim):
                super().__init__()
                self.layers = nn.Sequential(
                    nn.Linear(input_dim, 512),
                    nn.ReLU(),
                    nn.BatchNorm1d(512),
                    nn.Dropout(0.3),
                    nn.Linear(512, 256),
                    nn.ReLU(),
                    nn.BatchNorm1d(256),
                    nn.Dropout(0.3),
                    nn.Linear(256, 128),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(128, 1)
                )

            def forward(self, x):
                return self.layers(x).squeeze(-1)

        class FastPyTorchWrapper:
            def __init__(self, input_dim):
                self.input_dim = input_dim
                self.model = None
                self.scaler_x = StandardScaler()
                self.scaler_y = StandardScaler()

            def fit(self, X, y):
                if not TORCH_AVAILABLE:
                    return self

                # 数据预处理
                X_scaled = self.scaler_x.fit_transform(X)
                y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).flatten()

                # 创建模型
                self.model = FastNeuralNet(self.input_dim).to(device)

                # 训练数据
                X_tensor = torch.FloatTensor(X_scaled).to(device)
                y_tensor = torch.FloatTensor(y_scaled).to(device)

                dataset = TensorDataset(X_tensor, y_tensor)
                dataloader = DataLoader(dataset, batch_size=self.config.gpu_batch_size,
                                      shuffle=True, num_workers=0, pin_memory=True)

                # 优化器和损失函数
                optimizer = optim.AdamW(self.model.parameters(), lr=0.001, weight_decay=1e-4)
                scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
                criterion = nn.MSELoss()

                # 快速训练循环
                self.model.train()
                for epoch in range(50):  # 减少epochs
                    total_loss = 0
                    for batch_X, batch_y in dataloader:
                        optimizer.zero_grad()
                        outputs = self.model(batch_X)
                        loss = criterion(outputs, batch_y)
                        loss.backward()
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                        optimizer.step()
                        total_loss += loss.item()

                    avg_loss = total_loss / len(dataloader)
                    scheduler.step(avg_loss)

                    if epoch % 20 == 0:
                        print(f"Neural Net Epoch {epoch}, Loss: {avg_loss:.6f}")

                return self

            def predict(self, X):
                if not TORCH_AVAILABLE or self.model is None:
                    return np.zeros(len(X))

                X_scaled = self.scaler_x.transform(X)
                X_tensor = torch.FloatTensor(X_scaled).to(device)

                self.model.eval()
                with torch.no_grad():
                    predictions = self.model(X_tensor).cpu().numpy()

                # 反标准化
                predictions = self.scaler_y.inverse_transform(predictions.reshape(-1, 1)).flatten()

                return predictions

        return FastPyTorchWrapper(input_dim)

    def create_pytorch_wrapper(self, model_class, **kwargs):
        """创建PyTorch模型包装器"""
        class PyTorchWrapper:
            def __init__(self, model_class, **model_kwargs):
                self.model_class = model_class
                self.model_kwargs = model_kwargs
                self.model = None
                self.scaler_x = StandardScaler()
                self.scaler_y = StandardScaler()

            def fit(self, X, y):
                if not TORCH_AVAILABLE:
                    return self

                # 数据预处理
                X_scaled = self.scaler_x.fit_transform(X)
                y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).flatten()

                # 创建模型
                self.model = self.model_class(**self.model_kwargs).to(device)

                # 训练数据
                X_tensor = torch.FloatTensor(X_scaled).to(device)
                y_tensor = torch.FloatTensor(y_scaled).to(device)

                dataset = TensorDataset(X_tensor, y_tensor)
                dataloader = DataLoader(dataset, batch_size=self.config.batch_size, shuffle=True)

                # 优化器和损失函数
                optimizer = optim.AdamW(self.model.parameters(), lr=self.config.learning_rate, weight_decay=1e-5)
                scheduler = CosineAnnealingLR(optimizer, T_max=self.config.epochs)
                criterion = nn.MSELoss()

                # 训练循环
                self.model.train()
                for epoch in range(self.config.epochs):
                    total_loss = 0
                    for batch_X, batch_y in dataloader:
                        optimizer.zero_grad()
                        outputs = self.model(batch_X)
                        loss = criterion(outputs, batch_y)
                        loss.backward()
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                        optimizer.step()
                        total_loss += loss.item()

                    scheduler.step()

                    if epoch % 50 == 0:
                        avg_loss = total_loss / len(dataloader)
                        print(f"Epoch {epoch}, Loss: {avg_loss:.6f}")

                return self

            def predict(self, X):
                if not TORCH_AVAILABLE or self.model is None:
                    return np.zeros(len(X))

                X_scaled = self.scaler_x.transform(X)
                X_tensor = torch.FloatTensor(X_scaled).to(device)

                self.model.eval()
                with torch.no_grad():
                    predictions = self.model(X_tensor).cpu().numpy()

                # 反标准化
                predictions = self.scaler_y.inverse_transform(predictions.reshape(-1, 1)).flatten()

                return predictions

        return PyTorchWrapper(model_class, **kwargs)

    def train_with_cv(self, X: np.ndarray, y: np.ndarray, spectrum_indices: List[int]) -> Dict:
        """使用交叉验证训练模型"""
        print("开始交叉验证训练...")

        # 创建交叉验证分割
        kf = KFold(n_splits=self.config.n_folds, shuffle=True, random_state=self.config.seed)

        results = {}

        for spectrum_idx in tqdm(spectrum_indices, desc="训练光谱模型"):
            y_target = y[:, spectrum_idx]

            # 第一层模型
            level1_models = self.create_level1_models(X.shape[1])
            oof_preds = np.zeros((len(X), len(level1_models)))

            for fold, (train_idx, val_idx) in enumerate(kf.split(X)):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train, y_val = y_target[train_idx], y_target[val_idx]

                fold_models = {}
                for name, model in level1_models.items():
                    try:
                        # 训练模型
                        model.fit(X_train, y_train)

                        # 验证集预测
                        val_pred = model.predict(X_val)
                        oof_preds[val_idx, list(level1_models.keys()).index(name)] = val_pred

                        fold_models[name] = model

                    except Exception as e:
                        print(f"模型 {name} 在fold {fold} 训练失败: {e}")
                        oof_preds[val_idx, list(level1_models.keys()).index(name)] = np.mean(y_train)

                # 内存清理
                if fold % 2 == 0:
                    gc.collect()
                    if TORCH_AVAILABLE:
                        torch.cuda.empty_cache()

            # 训练第二层模型
            level2_model = Ridge(alpha=1.0)
            level2_model.fit(oof_preds, y_target)

            # 保存模型和OOF预测
            self.level1_models[spectrum_idx] = level1_models
            self.level2_models[spectrum_idx] = level2_model
            self.oof_predictions[spectrum_idx] = oof_preds

            # 计算CV分数
            oof_final = level2_model.predict(oof_preds)
            cv_score = mean_absolute_error(y_target, oof_final)
            results[spectrum_idx] = cv_score

            print(f"光谱 {spectrum_idx} CV MAE: {cv_score:.6f}")

        return results

    def predict(self, X_test: np.ndarray, spectrum_indices: List[int]) -> np.ndarray:
        """生成预测"""
        print("生成预测...")

        predictions = np.zeros((len(X_test), len(spectrum_indices)))

        for i, spectrum_idx in enumerate(tqdm(spectrum_indices, desc="预测光谱")):
            if spectrum_idx not in self.level1_models:
                continue

            # 第一层预测
            level1_preds = np.zeros((len(X_test), len(self.level1_models[spectrum_idx])))

            for j, (name, model) in enumerate(self.level1_models[spectrum_idx].items()):
                try:
                    pred = model.predict(X_test)
                    level1_preds[:, j] = pred
                except Exception as e:
                    print(f"模型 {name} 预测失败: {e}")
                    level1_preds[:, j] = 0

            # 第二层预测
            if spectrum_idx in self.level2_models:
                final_pred = self.level2_models[spectrum_idx].predict(level1_preds)
                predictions[:, i] = final_pred
            else:
                predictions[:, i] = np.mean(level1_preds, axis=1)

        return predictions


class ArielChampionSolution:
    """Ariel冠军解决方案主类"""

    def __init__(self, config: Config):
        self.config = config
        set_seed(config.seed)

        self.processor = ArielDataProcessor(config)
        self.ensemble = AdvancedEnsemble(config)

    def prepare_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, List[int]]:
        """准备训练数据"""
        print("准备训练数据...")

        # 获取训练数据
        train_planet_ids = self.processor.train_spectra['planet_id'].tolist()
        train_features = self.processor.create_feature_dataset(train_planet_ids, is_train=True)

        # 合并特征和光谱数据
        merged_df = train_features.merge(self.processor.train_spectra, on='planet_id', how='inner')

        # 分离特征和目标
        feature_cols = [col for col in merged_df.columns
                       if col not in ['planet_id'] and not col.startswith('spectrum_')]
        spectrum_cols = [col for col in merged_df.columns if col.startswith('spectrum_')]

        X = merged_df[feature_cols].values
        y = merged_df[spectrum_cols].values

        # 特征预处理
        scaler = RobustScaler()
        X_scaled = scaler.fit_transform(X)

        # 保存预处理器
        joblib.dump(scaler, Path(self.config.output_dir) / "feature_scaler.pkl")
        joblib.dump(feature_cols, Path(self.config.output_dir) / "feature_cols.pkl")

        # 选择要训练的光谱点
        n_spectrum_points = len(spectrum_cols)
        spectrum_indices = np.linspace(0, n_spectrum_points-1,
                                     min(self.config.n_spectrum_samples, n_spectrum_points),
                                     dtype=int).tolist()

        print(f"特征数量: {len(feature_cols)}")
        print(f"光谱点数量: {len(spectrum_cols)}")
        print(f"训练光谱点数量: {len(spectrum_indices)}")

        return X_scaled, y, feature_cols, spectrum_indices

    def train(self):
        """训练模型"""
        print("=== 开始训练 ===")

        # 准备数据
        X_train, y_train, feature_cols, spectrum_indices = self.prepare_data()

        # 训练集成模型
        cv_results = self.ensemble.train_with_cv(X_train, y_train, spectrum_indices)

        # 保存模型
        model_path = Path(self.config.output_dir) / "ensemble_models.pkl"
        joblib.dump({
            'level1_models': self.ensemble.level1_models,
            'level2_models': self.ensemble.level2_models,
            'spectrum_indices': spectrum_indices
        }, model_path)

        print("=== 训练完成 ===")
        print(f"平均CV MAE: {np.mean(list(cv_results.values())):.6f}")

        return cv_results

    def predict_test(self) -> pd.DataFrame:
        """预测测试集"""
        print("=== 开始预测测试集 ===")

        # 加载预处理器和特征列
        scaler = joblib.load(Path(self.config.output_dir) / "feature_scaler.pkl")
        feature_cols = joblib.load(Path(self.config.output_dir) / "feature_cols.pkl")

        # 加载模型
        model_data = joblib.load(Path(self.config.output_dir) / "ensemble_models.pkl")
        self.ensemble.level1_models = model_data['level1_models']
        self.ensemble.level2_models = model_data['level2_models']
        spectrum_indices = model_data['spectrum_indices']

        # 处理测试数据
        test_planet_ids = self.processor.test_star_info['planet_id'].tolist()
        test_features = self.processor.create_feature_dataset(test_planet_ids, is_train=False)

        # 确保特征列一致
        missing_cols = set(feature_cols) - set(test_features.columns)
        for col in missing_cols:
            test_features[col] = 0.0

        X_test = test_features[feature_cols].values
        X_test_scaled = scaler.transform(X_test)

        # 生成预测
        predictions = self.ensemble.predict(X_test_scaled, spectrum_indices)

        # 对未训练的光谱点进行插值
        full_predictions = np.zeros((len(X_test), 283))
        all_indices = np.arange(283)

        for row in range(len(X_test)):
            full_predictions[row] = np.interp(all_indices, spectrum_indices, predictions[row])

        # 创建提交文件
        submission = pd.DataFrame()
        submission['planet_id'] = test_planet_ids

        # 添加光谱预测
        for i in range(283):
            submission[f'spectrum_{i}'] = full_predictions[:, i]

        # 生成不确定性估计（基于模型方差）
        uncertainty_predictions = self.estimate_uncertainty(X_test_scaled, spectrum_indices)
        full_uncertainty = np.zeros((len(X_test), 283))

        for row in range(len(X_test)):
            full_uncertainty[row] = np.interp(all_indices, spectrum_indices, uncertainty_predictions[row])

        # 添加不确定性预测
        for i in range(283):
            submission[f'uncertainty_{i}'] = np.maximum(full_uncertainty[:, i], 1.0)

        print("=== 预测完成 ===")
        return submission

    def estimate_uncertainty(self, X_test: np.ndarray, spectrum_indices: List[int]) -> np.ndarray:
        """估计预测不确定性"""
        uncertainties = np.zeros((len(X_test), len(spectrum_indices)))

        for i, spectrum_idx in enumerate(spectrum_indices):
            if spectrum_idx not in self.ensemble.level1_models:
                uncertainties[:, i] = 10.0
                continue

            # 收集所有第一层模型的预测
            level1_preds = []
            for name, model in self.ensemble.level1_models[spectrum_idx].items():
                try:
                    pred = model.predict(X_test)
                    level1_preds.append(pred)
                except:
                    continue

            if level1_preds:
                level1_preds = np.array(level1_preds)
                # 使用预测方差作为不确定性
                uncertainties[:, i] = np.std(level1_preds, axis=0)
            else:
                uncertainties[:, i] = 10.0

        return uncertainties


def main():
    """主函数"""
    print("=== Ariel Data Challenge 2025 - 冠军级解决方案 ===")

    # 配置
    config = Config()

    # 创建输出目录
    Path(config.output_dir).mkdir(exist_ok=True)

    # 初始化解决方案
    solution = ArielChampionSolution(config)

    # 训练模型
    cv_results = solution.train()

    # 预测测试集
    submission = solution.predict_test()

    # 保存提交文件
    submission.to_csv('submission.csv', index=False)
    print("提交文件已保存为 submission.csv")

    # 显示统计信息
    spectrum_cols = [col for col in submission.columns if col.startswith('spectrum_')]
    uncertainty_cols = [col for col in submission.columns if col.startswith('uncertainty_')]

    spectrum_values = submission[spectrum_cols].values
    uncertainty_values = submission[uncertainty_cols].values

    print(f"\n=== 预测统计 ===")
    print(f"光谱预测范围: [{spectrum_values.min():.6f}, {spectrum_values.max():.6f}]")
    print(f"不确定性预测范围: [{uncertainty_values.min():.6f}, {uncertainty_values.max():.6f}]")
    print(f"平均CV MAE: {np.mean(list(cv_results.values())):.6f}")

    print("=== 完成! ===")


if __name__ == "__main__":
    main()
