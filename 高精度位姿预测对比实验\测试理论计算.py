#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试理论计算模块
验证Staubli TX60机器人的正向运动学计算
"""

import numpy as np
import pandas as pd
from 理论计算模块 import RobotKinematics

def test_theoretical_calculation():
    """测试理论计算的正确性"""
    print("=== 测试Staubli TX60理论计算 ===")
    
    # 创建机器人运动学计算器
    robot = RobotKinematics()
    
    # 测试1: 零位姿
    print("\n测试1: 零位姿")
    zero_angles = np.zeros(6)
    zero_pose = robot.forward_kinematics(zero_angles)
    print(f"零位姿结果: {zero_pose}")
    print(f"位置 (mm): X={zero_pose[0]:.3f}, Y={zero_pose[1]:.3f}, Z={zero_pose[2]:.3f}")
    print(f"姿态 (度): Rx={zero_pose[3]:.3f}, Ry={zero_pose[4]:.3f}, Rz={zero_pose[5]:.3f}")
    
    # 测试2: 典型位姿
    print("\n测试2: 典型位姿")
    test_angles = np.array([30, 45, -30, 60, -45, 90])  # 度
    test_pose = robot.forward_kinematics(test_angles)
    print(f"输入关节角 (度): {test_angles}")
    print(f"输出位姿: {test_pose}")
    print(f"位置 (mm): X={test_pose[0]:.3f}, Y={test_pose[1]:.3f}, Z={test_pose[2]:.3f}")
    print(f"姿态 (度): Rx={test_pose[3]:.3f}, Ry={test_pose[4]:.3f}, Rz={test_pose[5]:.3f}")
    
    # 测试3: 与实际数据对比
    print("\n测试3: 与实际数据对比")
    try:
        # 加载实际数据
        joint_data = pd.read_excel('../theta2000.xlsx', header=None)
        joint_data.columns = [f'theta_{i+1}' for i in range(6)]
        measured_data = pd.read_excel('../real2000.xlsx')
        
        # 选择前5个样本进行对比
        print("前5个样本的对比:")
        print("样本 | 理论位置(mm) | 实测位置(mm) | 位置误差(mm)")
        print("-" * 70)
        
        for i in range(5):
            # 理论计算
            joint_angles = joint_data.iloc[i].values
            theoretical_pose = robot.forward_kinematics(joint_angles)
            
            # 实测值
            measured_pose = measured_data.iloc[i].values
            
            # 位置误差
            pos_error = np.sqrt(np.sum((theoretical_pose[:3] - measured_pose[:3])**2))
            
            print(f"{i+1:4d} | ({theoretical_pose[0]:6.1f},{theoretical_pose[1]:6.1f},{theoretical_pose[2]:6.1f}) | "
                  f"({measured_pose[0]:6.1f},{measured_pose[1]:6.1f},{measured_pose[2]:6.1f}) | {pos_error:8.3f}")
        
        # 计算整体统计
        print(f"\n计算前100个样本的统计...")
        theoretical_poses = []
        for i in range(min(100, len(joint_data))):
            joint_angles = joint_data.iloc[i].values
            theoretical_pose = robot.forward_kinematics(joint_angles)
            theoretical_poses.append(theoretical_pose)
        
        theoretical_poses = np.array(theoretical_poses)
        measured_poses = measured_data.iloc[:len(theoretical_poses)].values
        
        # 位置误差统计
        pos_errors = np.sqrt(np.sum((theoretical_poses[:, :3] - measured_poses[:, :3])**2, axis=1))
        print(f"位置误差统计 (前{len(theoretical_poses)}个样本):")
        print(f"  平均误差: {np.mean(pos_errors):.3f} mm")
        print(f"  标准差:   {np.std(pos_errors):.3f} mm")
        print(f"  最大误差: {np.max(pos_errors):.3f} mm")
        print(f"  最小误差: {np.min(pos_errors):.3f} mm")
        
        # 姿态误差统计
        angle_errors = np.sqrt(np.sum((theoretical_poses[:, 3:] - measured_poses[:, 3:])**2, axis=1))
        print(f"姿态误差统计 (前{len(theoretical_poses)}个样本):")
        print(f"  平均误差: {np.mean(angle_errors):.3f} 度")
        print(f"  标准差:   {np.std(angle_errors):.3f} 度")
        print(f"  最大误差: {np.max(angle_errors):.3f} 度")
        print(f"  最小误差: {np.min(angle_errors):.3f} 度")
        
    except FileNotFoundError as e:
        print(f"数据文件未找到: {e}")
        print("请确保theta2000.xlsx和real2000.xlsx在上级目录中")
    
    # 测试4: 验证DH参数
    print(f"\n测试4: DH参数验证")
    print("Staubli TX60 M-DH参数:")
    print("关节 |   a(mm) | alpha(rad) |  d(mm) | theta_offset(rad) | beta(rad)")
    print("-" * 70)
    for i, params in enumerate(robot.dh_params, 1):
        a, alpha, d, theta_offset, beta = params
        print(f"{i:4d} | {a:7.1f} | {alpha:10.3f} | {d:6.1f} | {theta_offset:13.3f} | {beta:9.3f}")

def test_workspace_analysis():
    """分析工作空间特性"""
    print(f"\n=== 工作空间分析 ===")
    
    robot = RobotKinematics()
    
    # 生成随机关节角度
    np.random.seed(42)
    n_samples = 1000
    joint_ranges = [
        (-180, 180),  # θ1
        (-125, 125),  # θ2  
        (-138, 138),  # θ3
        (-270, 270),  # θ4
        (-120, 120),  # θ5
        (-270, 270)   # θ6
    ]
    
    poses = []
    for _ in range(n_samples):
        joint_angles = []
        for min_angle, max_angle in joint_ranges:
            angle = np.random.uniform(min_angle, max_angle)
            joint_angles.append(angle)
        
        pose = robot.forward_kinematics(joint_angles)
        poses.append(pose)
    
    poses = np.array(poses)
    
    print(f"工作空间分析 (基于{n_samples}个随机样本):")
    print("坐标 | 最小值 | 最大值 | 范围 | 平均值 | 标准差")
    print("-" * 60)
    
    coord_names = ['X(mm)', 'Y(mm)', 'Z(mm)', 'Rx(°)', 'Ry(°)', 'Rz(°)']
    for i, name in enumerate(coord_names):
        min_val = np.min(poses[:, i])
        max_val = np.max(poses[:, i])
        range_val = max_val - min_val
        mean_val = np.mean(poses[:, i])
        std_val = np.std(poses[:, i])
        
        print(f"{name:5s} | {min_val:6.1f} | {max_val:6.1f} | {range_val:6.1f} | {mean_val:6.1f} | {std_val:6.1f}")

if __name__ == "__main__":
    test_theoretical_calculation()
    test_workspace_analysis()
    
    print("\n=== 测试完成 ===")
    print("如果理论计算与实测值差异较大，可能需要:")
    print("1. 调整DH参数")
    print("2. 检查坐标系定义")
    print("3. 确认角度单位和符号约定")
    print("4. 验证变换矩阵公式")
