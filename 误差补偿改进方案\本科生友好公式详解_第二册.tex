\documentclass[12pt,a4paper]{article}
\usepackage[UTF8]{ctex}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{xcolor}
\usepackage{framed}
\usepackage{tikz}

\geometry{left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}

\title{\textbf{机器人误差补偿公式详解\\本科生超级友好版 - 第二册}}

\author{具体公式的推导过程}

\date{\today}

\begin{document}

\maketitle

\section{第六章：多目标优化公式详解}

\subsection{6.1 为什么需要多目标优化？}

\textbf{现实问题}：我们有三个相互冲突的目标

\begin{framed}
\textcolor{blue}{\textbf{三个目标}}
\begin{enumerate}
\item $f_1(\bm{w})$：位置要准确（位置误差要小）
\item $f_2(\bm{w})$：角度要准确（角度误差要小）  
\item $f_3(\bm{w})$：模型要简单（参数不能太多）
\end{enumerate}
\end{framed}

\textbf{冲突在哪里？}
\begin{itemize}
\item 要位置准确 → 需要复杂模型 → 与目标3冲突
\item 要角度准确 → 可能牺牲位置精度 → 与目标1冲突
\item 要模型简单 → 精度可能下降 → 与目标1、2冲突
\end{itemize}

\subsection{6.2 传统单目标方法的问题}

\textbf{传统做法}：把三个目标加权求和
$$\mathcal{L}_{total} = w_1 f_1(\bm{w}) + w_2 f_2(\bm{w}) + w_3 f_3(\bm{w})$$

\textbf{问题}：权重$w_1, w_2, w_3$怎么选？

\textcolor{red}{\textbf{例子}}：
\begin{itemize}
\item 如果$w_1 = 0.8, w_2 = 0.1, w_3 = 0.1$ → 只关心位置，忽略角度
\item 如果$w_1 = 0.1, w_2 = 0.8, w_3 = 0.1$ → 只关心角度，忽略位置
\item 权重选择是主观的，可能错过更好的解
\end{itemize}

\subsection{6.3 多目标优化的数学表达}

\textbf{我们的方法}：不用权重，直接优化向量
$$\min_{\bm{w}} \bm{F}(\bm{w}) = \min_{\bm{w}} \begin{bmatrix} f_1(\bm{w}) \\ f_2(\bm{w}) \\ f_3(\bm{w}) \end{bmatrix}$$

\textbf{这是什么意思？}
我们不寻找一个解，而是寻找一组解，每个解在三个目标之间有不同的平衡。

\subsection{6.4 支配关系：怎么比较两个解？}

\textbf{问题}：解A和解B哪个更好？

\textbf{支配关系定义}：
解$\bm{w}_1$支配解$\bm{w}_2$（记作$\bm{w}_1 \prec \bm{w}_2$），当且仅当：
\begin{enumerate}
\item 在所有目标上，$\bm{w}_1$都不比$\bm{w}_2$差
\item 在至少一个目标上，$\bm{w}_1$比$\bm{w}_2$好
\end{enumerate}

\textbf{数学表达}：
$$\bm{w}_1 \prec \bm{w}_2 \iff \begin{cases}
\forall i: f_i(\bm{w}_1) \leq f_i(\bm{w}_2) & \text{(都不差)} \\
\exists j: f_j(\bm{w}_1) < f_j(\bm{w}_2) & \text{(至少一个更好)}
\end{cases}$$

\textcolor{blue}{\textbf{具体例子}}：
\begin{itemize}
\item 解A：位置误差=0.1mm，角度误差=0.05°，复杂度=100
\item 解B：位置误差=0.2mm，角度误差=0.08°，复杂度=120
\item 结论：A支配B（A在所有方面都更好）
\end{itemize}

\textcolor{red}{\textbf{不能比较的情况}}：
\begin{itemize}
\item 解C：位置误差=0.1mm，角度误差=0.08°，复杂度=80
\item 解D：位置误差=0.15mm，角度误差=0.05°，复杂度=90
\item 结论：C和D互不支配（各有优劣）
\end{itemize}

\subsection{6.5 Pareto最优解集}

\textbf{定义}：Pareto最优解集是所有不被其他解支配的解的集合。

\textcolor{blue}{\textbf{用人话说}}：这些解都是"最好的"，但好的方面不同。

\textbf{数学表达}：
$$\mathcal{P}^* = \{\bm{w} \in \Omega : \nexists \bm{w}' \in \Omega, \bm{w}' \prec \bm{w}\}$$

其中$\Omega$是所有可能解的集合。

\section{第七章：NSGA-II算法详解}

\subsection{7.1 算法的基本思想}

\textbf{核心思想}：模拟生物进化
\begin{enumerate}
\item \textbf{选择}：好的解有更大概率被选中
\item \textbf{交叉}：两个好解"结合"产生新解
\item \textbf{变异}：随机改变解的某些部分
\item \textbf{环境选择}：保留最好的解，淘汰差的解
\end{enumerate}

\subsection{7.2 非支配排序}

\textbf{目的}：把所有解分成不同的"等级"

\textbf{算法步骤}：
\begin{enumerate}
\item \textbf{第一前沿$\mathcal{F}_1$}：找出所有不被任何解支配的解
\item \textbf{第二前沿$\mathcal{F}_2$}：去掉第一前沿后，找出不被剩余解支配的解
\item \textbf{第三前沿$\mathcal{F}_3$}：继续这个过程...
\end{enumerate}

\textcolor{blue}{\textbf{具体例子}}：
假设有6个解：A, B, C, D, E, F
\begin{itemize}
\item A支配D, E；B支配E, F；C支配F
\item 第一前沿：\{A, B, C\}（没有被其他解支配）
\item 第二前沿：\{D\}（只被第一前沿支配）
\item 第三前沿：\{E, F\}（被前两个前沿支配）
\end{itemize}

\subsection{7.3 拥挤距离}

\textbf{问题}：同一个前沿内的解怎么排序？

\textbf{解决方案}：计算拥挤距离，优先保留"孤立"的解

\textbf{拥挤距离公式}：
$$d_i = \sum_{k=1}^{M} \frac{f_k(\bm{w}_{i+1}) - f_k(\bm{w}_{i-1})}{f_k^{max} - f_k^{min}}$$

\textbf{公式解释}：
\begin{itemize}
\item $M=3$：目标函数个数
\item $\bm{w}_{i-1}, \bm{w}_{i+1}$：在第$k$个目标上相邻的解
\item $f_k^{max} - f_k^{min}$：第$k$个目标的取值范围（归一化）
\item $d_i$：解$i$的拥挤距离
\end{itemize}

\textcolor{blue}{\textbf{物理含义}}：
\begin{itemize}
\item $d_i$越大 → 解$i$周围越"空旷" → 多样性贡献越大
\item $d_i$越小 → 解$i$周围很"拥挤" → 可以被淘汰
\end{itemize}

\textbf{边界处理}：
边界点（最好和最差的解）的拥挤距离设为无穷大：$d_{boundary} = \infty$

\subsection{7.4 选择机制}

\textbf{锦标赛选择}：随机选择几个解，选择其中最好的

\textbf{比较规则}：
$$\text{select}(\bm{w}_i, \bm{w}_j) = \begin{cases}
\bm{w}_i & \text{if } rank_i < rank_j \text{ (前沿等级更好)} \\
\bm{w}_j & \text{if } rank_i > rank_j \\
\bm{w}_i & \text{if } rank_i = rank_j \text{ and } d_i > d_j \text{ (拥挤距离更大)} \\
\bm{w}_j & \text{otherwise}
\end{cases}$$

\section{第八章：物理约束公式详解}

\subsection{8.1 为什么需要物理约束？}

\textbf{问题}：纯数据驱动的方法可能学到"不合理"的规律

\textcolor{red}{\textbf{例子}}：
\begin{itemize}
\item 神经网络可能预测：关节1转30°，末端移动到火星上
\item 这在数学上可能拟合得很好，但物理上不可能
\end{itemize}

\textbf{解决方案}：在损失函数中加入物理定律作为"软约束"

\subsection{8.2 PINN损失函数的构成}

\textbf{总损失函数}：
$$\mathcal{L}_{PINN} = \mathcal{L}_{data} + \lambda_{physics} \mathcal{L}_{physics} + \lambda_{boundary} \mathcal{L}_{boundary}$$

\textbf{各项含义}：
\begin{itemize}
\item $\mathcal{L}_{data}$：数据拟合损失（让预测接近真实值）
\item $\mathcal{L}_{physics}$：物理约束损失（让预测符合物理定律）
\item $\mathcal{L}_{boundary}$：边界条件损失（让预测在合理范围内）
\item $\lambda_{physics}, \lambda_{boundary}$：权重系数
\end{itemize}

\subsection{8.3 数据拟合损失}

\textbf{基本形式}：
$$\mathcal{L}_{data} = \frac{1}{N} \sum_{i=1}^{N} \|\bm{\epsilon}_i - \hat{\bm{\epsilon}}_i\|^2$$

\textbf{符号解释}：
\begin{itemize}
\item $N$：训练样本数量
\item $\bm{\epsilon}_i$：第$i$个样本的真实误差
\item $\hat{\bm{\epsilon}}_i$：第$i$个样本的预测误差
\item $\|\cdot\|^2$：平方和（让正负误差都被惩罚）
\end{itemize}

\textbf{加权版本}：
$$\mathcal{L}_{data} = w_{pos} \mathcal{L}_{pos} + w_{ori} \mathcal{L}_{ori}$$

其中：
\begin{align}
\mathcal{L}_{pos} &= \frac{1}{N} \sum_{i=1}^{N} \|\bm{\epsilon}_{pos,i} - \hat{\bm{\epsilon}}_{pos,i}\|^2 \quad \text{(位置误差)} \\
\mathcal{L}_{ori} &= \frac{1}{N} \sum_{i=1}^{N} \|\bm{\epsilon}_{ori,i} - \hat{\bm{\epsilon}}_{ori,i}\|^2 \quad \text{(角度误差)}
\end{align}

\textbf{权重选择}：$w_{pos} = 0.7, w_{ori} = 0.3$（位置更重要）

\subsection{8.4 运动学约束}

\textbf{物理原理}：神经网络学到的"导数"应该等于理论上的雅可比矩阵

\textbf{约束公式}：
$$\mathcal{L}_{kinematics} = \frac{1}{N} \sum_{i=1}^{N} \left\|\frac{\partial \mathcal{F}(\bm{\theta}_i)}{\partial \bm{\theta}} - \bm{J}(\bm{\theta}_i)\right\|_F^2$$

\textbf{符号解释}：
\begin{itemize}
\item $\mathcal{F}(\bm{\theta}_i)$：神经网络的输出
\item $\frac{\partial \mathcal{F}(\bm{\theta}_i)}{\partial \bm{\theta}}$：神经网络输出对输入的导数
\item $\bm{J}(\bm{\theta}_i)$：理论雅可比矩阵
\item $\|\cdot\|_F^2$：Frobenius范数（矩阵的平方和）
\end{itemize}

\textcolor{blue}{\textbf{用人话说}}：
神经网络预测的"敏感性"应该和物理理论计算的"敏感性"一致。

\subsection{8.5 几何约束}

\textbf{物理原理}：旋转矩阵必须满足正交性和行列式性质

\textbf{约束公式}：
$$\mathcal{L}_{geometry} = \|\bm{R}^T\bm{R} - \bm{I}\|_F^2 + (\det(\bm{R}) - 1)^2$$

\textbf{两个条件解释}：

\textcolor{blue}{\textbf{条件1：正交性}}
$$\bm{R}^T\bm{R} = \bm{I}$$

\textbf{含义}：旋转矩阵的行（或列）互相垂直，且长度为1

\textbf{具体展开}：
$$\bm{R}^T\bm{R} = \begin{bmatrix}
r_{11} & r_{21} & r_{31} \\
r_{12} & r_{22} & r_{32} \\
r_{13} & r_{23} & r_{33}
\end{bmatrix} \begin{bmatrix}
r_{11} & r_{12} & r_{13} \\
r_{21} & r_{22} & r_{23} \\
r_{31} & r_{32} & r_{33}
\end{bmatrix} = \begin{bmatrix}
1 & 0 & 0 \\
0 & 1 & 0 \\
0 & 0 & 1
\end{bmatrix}$$

\textcolor{blue}{\textbf{条件2：行列式}}
$$\det(\bm{R}) = 1$$

\textbf{含义}：这是旋转，不是反射（镜像）

\textbf{行列式计算}：
$$\det(\bm{R}) = r_{11}(r_{22}r_{33} - r_{23}r_{32}) - r_{12}(r_{21}r_{33} - r_{23}r_{31}) + r_{13}(r_{21}r_{32} - r_{22}r_{31})$$

\section{第九章：确定性初始化公式推导}

\subsection{9.1 随机初始化的问题}

\textbf{传统做法}：
```python
weights = np.random.normal(0, 0.01, size=(n_params,))
```

\textbf{问题}：
\begin{itemize}
\item 每次训练结果不同
\item 可能从很差的起点开始
\item 容易陷入局部最优
\end{itemize}

\subsection{9.2 我们的解决思路}

\textbf{核心思想}：用物理知识计算一个"聪明"的起点

\textbf{问题建模}：
假设误差和关节角度之间存在线性关系：
$$\bm{\epsilon} \approx \bm{J}(\bm{\theta}) \Delta\bm{\theta}$$

如果我们有训练数据$\{(\bm{\theta}_i, \bm{\epsilon}_i)\}_{i=1}^N$，怎么找到最好的线性关系？

\subsection{9.3 最小二乘法推导}

\textbf{目标}：找到参数$\bm{w}$，使得预测误差最小
$$\min_{\bm{w}} \sum_{i=1}^{N} \|\bm{\epsilon}_i - \bm{J}(\bm{\theta}_i) \bm{w}\|^2$$

\textbf{矩阵形式}：
$$\min_{\bm{w}} \|\bm{E} - \bm{J} \bm{w}\|^2$$

其中：
\begin{itemize}
\item $\bm{E} = [\bm{\epsilon}_1; \bm{\epsilon}_2; \ldots; \bm{\epsilon}_N]$：所有误差堆叠
\item $\bm{J} = [\bm{J}(\bm{\theta}_1); \bm{J}(\bm{\theta}_2); \ldots; \bm{J}(\bm{\theta}_N)]$：所有雅可比矩阵堆叠
\end{itemize}

\textbf{求导求解}：
$$\frac{\partial}{\partial \bm{w}} \|\bm{E} - \bm{J} \bm{w}\|^2 = -2\bm{J}^T(\bm{E} - \bm{J} \bm{w}) = 0$$

解得：
$$\bm{J}^T \bm{J} \bm{w} = \bm{J}^T \bm{E}$$

$$\bm{w} = (\bm{J}^T \bm{J})^{-1} \bm{J}^T \bm{E}$$

\subsection{9.4 正则化版本}

\textbf{问题}：$\bm{J}^T \bm{J}$可能不可逆（奇异）

\textbf{解决方案}：加入正则化项
$$\min_{\bm{w}} \|\bm{E} - \bm{J} \bm{w}\|^2 + \alpha \|\bm{w}\|^2$$

\textbf{解析解}：
$$\bm{w}_{init} = (\bm{J}^T \bm{J} + \alpha \bm{I})^{-1} \bm{J}^T \bm{E}$$

\textbf{参数含义}：
\begin{itemize}
\item $\alpha > 0$：正则化参数（通常取$10^{-3}$到$10^{-6}$）
\item $\bm{I}$：单位矩阵
\item 正则化项$\alpha \bm{I}$确保矩阵可逆
\end{itemize}

\subsection{9.5 几何解释}

\textbf{几何含义}：
在参数空间中，$\bm{w}_{init}$是距离原点最近的、能够拟合训练数据的点。

\textbf{为什么这样好？}
\begin{itemize}
\item \textbf{物理合理}：基于线性化的物理模型
\item \textbf{确定性}：每次都得到相同的起点
\item \textbf{接近最优}：通常离真正的最优解不远
\end{itemize}

\section{第十章：公式的实际意义}

\subsection{10.1 从公式到实际效果}

\textbf{我们的三大创新}：

\begin{framed}
\textcolor{red}{\textbf{创新总结}}
\begin{enumerate}
\item \textbf{多目标优化}：$\min [\text{位置误差}, \text{角度误差}, \text{复杂度}]$
\item \textbf{物理约束}：$\mathcal{L} = \mathcal{L}_{data} + \lambda \mathcal{L}_{physics}$
\item \textbf{确定性初始化}：$\bm{w}_{init} = (\bm{J}^T\bm{J} + \alpha\bm{I})^{-1}\bm{J}^T\bm{E}$
\end{enumerate}
\end{framed}

\textbf{实际效果}：
\begin{itemize}
\item 位置精度提升：91.7%（从0.708mm → 0.059mm）
\item 角度精度提升：72.5%（从0.179° → 0.049°）
\item 收敛速度提升：47%（从127轮 → 67轮）
\end{itemize}

\subsection{10.2 为什么这些公式有效？}

\textbf{多目标优化有效的原因}：
\begin{itemize}
\item 避免了主观权重选择
\item 找到了所有可能的平衡解
\item 通过种群多样性避免局部最优
\end{itemize}

\textbf{物理约束有效的原因}：
\begin{itemize}
\item 利用了领域知识
\item 减少了需要学习的复杂度
\item 提高了泛化能力
\end{itemize}

\textbf{确定性初始化有效的原因}：
\begin{itemize}
\item 提供了物理合理的起点
\item 减少了搜索空间
\item 提高了收敛稳定性
\end{itemize}

\section{下一册预告}

在第三册中，我们将学习：
\begin{itemize}
\item 如何用Python实现这些公式
\item 每个算法的具体编程步骤
\item 如何调试和优化代码
\item 实际应用中的注意事项
\end{itemize}

\textcolor{blue}{\textbf{记住}}：理解了公式的含义，编程实现就不难了！

\end{document}
