# 基于支持向量回归的工业机器人空间误差预测实验设计

## 实验目标
根据论文《基于支持向量回归的工业机器人空间误差预测》，设计一个完整的实验来验证：
1. 理论计算值与激光跟踪仪实际测量值的误差
2. ML模型预测值与实际测量值的误差
3. 证明ML模型相比理论计算具有更高的精度

## 实验设计思路

### 1. 数据来源
- **关节角度数据**: theta2000.xlsx (2000组关节角度θ1-θ6)
- **实际位姿数据**: real2000.xlsx (激光跟踪仪测量的实际位姿X,Y,Z)
- **理论计算**: 基于M-DH运动学模型计算理论位姿
- **ML预测**: 基于SVR模型训练预测位姿

### 2. 实验流程
```
关节角度 → 理论计算 → 理论位姿值
    ↓
关节角度 → ML模型训练 → ML预测位姿值
    ↓
激光跟踪仪 → 实际位姿值（真值）
    ↓
误差计算与对比分析
```

### 3. 核心计算公式

#### 3.1 M-DH运动学模型
根据论文，连杆坐标系转换关系：
```
A_i = Rot(Z_i, θ_i) * Trans(Z_i, d_i) * Trans(X_i, a_i) * Rot(X_i, α_i) * Rot(Y_i, β_i)
```

#### 3.2 误差计算
- **位置误差**: ΔP = √[(X_pred - X_real)² + (Y_pred - Y_real)² + (Z_pred - Z_real)²]
- **平均绝对误差**: MAE = (1/n) * Σ|P_pred - P_real|
- **均方根误差**: RMSE = √[(1/n) * Σ(P_pred - P_real)²]

### 4. 实验数据分割
- **训练集**: 1600组数据 (80%)
- **测试集**: 400组数据 (20%)
- **展示数据**: 从测试集中选择50组代表性数据进行详细分析

### 5. 预期结果
根据论文，预期ML模型的预测精度将显著优于理论计算：
- 理论计算误差: 预计在毫米级别
- ML模型误差: 预计比理论计算减少50%以上

## 实验实施计划

### 阶段1: 数据预处理
1. 加载关节角度和实际位姿数据
2. 数据清洗和标准化
3. 数据集分割

### 阶段2: 理论计算实现
1. 实现M-DH运动学正解算法
2. 计算所有测试点的理论位姿
3. 计算理论值与实际值的误差

### 阶段3: ML模型训练
1. 使用SVR模型训练
2. 超参数优化
3. 模型验证

### 阶段4: 结果对比分析
1. 计算各种误差指标
2. 生成对比图表
3. 统计分析报告

### 阶段5: 学术报告生成
1. 实验方法描述
2. 结果分析
3. 结论与讨论

## 文件结构
```
新实验设计/
├── 实验设计方案.md
├── 数据预处理.py
├── 理论计算模块.py
├── ML模型训练.py
├── 误差分析.py
├── 结果可视化.py
├── 学术报告生成.py
└── 实验结果/
    ├── 数据对比表.xlsx
    ├── 误差分析图.png
    └── 实验报告.pdf
```

这个实验设计将为您的学术论文提供完整的实验验证和数据支撑。
