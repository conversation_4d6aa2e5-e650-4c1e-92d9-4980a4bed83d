#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习方法论PPT生成器
专门讲解机器学习解决工程问题的通用思路和方法

作者: 朱昕鋆
日期: 2025年7月12日
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Circle, Arrow
import numpy as np
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class MLMethodologyPPT:
    """机器学习方法论PPT生成器"""

    def __init__(self):
        self.output_dir = "输出结果/PPT"
        os.makedirs(self.output_dir, exist_ok=True)

        # PPT样式设置
        self.slide_width = 16
        self.slide_height = 9
        self.title_fontsize = 24
        self.subtitle_fontsize = 18
        self.content_fontsize = 14
        self.small_fontsize = 12

        # 颜色方案
        self.colors = {
            'primary': '#2E86AB',      # 主色调-蓝色
            'secondary': '#A23B72',    # 次色调-紫色
            'accent': '#F18F01',       # 强调色-橙色
            'success': '#C73E1D',      # 成功色-红色
            'background': '#F5F5F5',   # 背景色
            'text': '#2C3E50',         # 文字色
            'light': '#ECF0F1'         # 浅色
        }

    def create_slide_template(self, title, subtitle=""):
        """创建PPT模板"""
        fig, ax = plt.subplots(figsize=(self.slide_width, self.slide_height))
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.axis('off')

        # 背景
        bg = FancyBboxPatch((0, 0), 10, 10,
                           boxstyle="round,pad=0",
                           facecolor=self.colors['background'],
                           edgecolor='none')
        ax.add_patch(bg)

        # 标题栏
        title_bg = FancyBboxPatch((0, 8.5), 10, 1.5,
                                 boxstyle="round,pad=0.1",
                                 facecolor=self.colors['primary'],
                                 edgecolor='none')
        ax.add_patch(title_bg)

        # 标题文字
        ax.text(5, 9.2, title, ha='center', va='center',
                fontsize=self.title_fontsize, color='white', weight='bold')

        if subtitle:
            ax.text(5, 8.8, subtitle, ha='center', va='center',
                    fontsize=self.subtitle_fontsize, color='white', alpha=0.9)

        return fig, ax

    def slide_1_title(self):
        """第1页：标题页"""
        fig, ax = self.create_slide_template("机器学习解决工程问题的方法论",
                                           "以机器人误差补偿为例")

        # 主要内容
        content = [
            "🎯 从问题定义到解决方案的完整思路",
            "🔧 传统方法 vs 现代机器学习方法",
            "🧠 深度学习模型选择与设计原则",
            "⚡ 先进技术的融合应用策略",
            "📊 实验设计与结果评估方法"
        ]

        for i, text in enumerate(content):
            ax.text(1, 7 - i*0.8, text, fontsize=self.content_fontsize,
                   color=self.colors['text'], va='center')

        # 装饰元素
        circle = Circle((8.5, 3), 1.2, facecolor=self.colors['accent'], alpha=0.3)
        ax.add_patch(circle)
        ax.text(8.5, 3, "ML\n方法论", ha='center', va='center',
                fontsize=16, weight='bold', color=self.colors['accent'])

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/01_标题页.png", dpi=300, bbox_inches='tight')
        plt.close()

    def slide_2_problem_analysis(self):
        """第2页：问题分析框架"""
        fig, ax = self.create_slide_template("机器学习问题分析框架",
                                           "如何将工程问题转化为ML问题")

        # 问题分析流程
        steps = [
            ("1. 问题定义", "明确要解决什么问题"),
            ("2. 数据分析", "有什么数据，数据质量如何"),
            ("3. 任务类型", "分类、回归、聚类、强化学习？"),
            ("4. 评估指标", "用什么标准衡量成功"),
            ("5. 约束条件", "有哪些限制和要求")
        ]

        # 绘制流程图
        y_positions = [6.5, 5.5, 4.5, 3.5, 2.5]

        for i, (step, desc) in enumerate(steps):
            # 步骤框
            step_box = FancyBboxPatch((0.5, y_positions[i]-0.3), 2, 0.6,
                                     boxstyle="round,pad=0.1",
                                     facecolor=self.colors['primary'],
                                     edgecolor='none')
            ax.add_patch(step_box)

            # 步骤文字
            ax.text(1.5, y_positions[i], step, ha='center', va='center',
                   fontsize=self.content_fontsize, color='white', weight='bold')

            # 描述文字
            ax.text(3, y_positions[i], desc, ha='left', va='center',
                   fontsize=self.content_fontsize, color=self.colors['text'])

            # 箭头
            if i < len(steps) - 1:
                ax.arrow(1.5, y_positions[i]-0.4, 0, -0.3,
                        head_width=0.1, head_length=0.1,
                        fc=self.colors['secondary'], ec=self.colors['secondary'])

        # 右侧示例
        ax.text(7, 7, "机器人误差补偿实例", fontsize=self.subtitle_fontsize,
               color=self.colors['secondary'], weight='bold')

        examples = [
            "提高机器人定位精度",
            "关节角度 + 实测位姿数据",
            "多输出回归问题",
            "位置误差 + 角度误差",
            "实时性 + 物理一致性"
        ]

        for i, example in enumerate(examples):
            ax.text(7, 6.5 - i, f"→ {example}", fontsize=self.small_fontsize,
                   color=self.colors['accent'])

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/02_问题分析框架.png", dpi=300, bbox_inches='tight')
        plt.close()

    def slide_3_data_preprocessing(self):
        """第3页：数据预处理策略"""
        fig, ax = self.create_slide_template("数据预处理：让数据更有价值",
                                           "特征工程是机器学习成功的关键")

        # 左侧：通用策略
        ax.text(0.5, 7.5, "通用数据预处理策略", fontsize=self.subtitle_fontsize,
               color=self.colors['primary'], weight='bold')

        strategies = [
            "数据清洗：处理缺失值、异常值",
            "数据标准化：归一化、标准化",
            "特征选择：去除冗余、选择重要特征",
            "特征构造：组合特征、多项式特征",
            "数据增强：扩充数据集规模"
        ]

        for i, strategy in enumerate(strategies):
            ax.text(0.5, 6.8 - i*0.6, f"• {strategy}", fontsize=self.content_fontsize,
                   color=self.colors['text'])

        # 右侧：机器人项目实例
        ax.text(5.5, 7.5, "机器人项目中的应用", fontsize=self.subtitle_fontsize,
               color=self.colors['secondary'], weight='bold')

        # 特征工程示例
        feature_box = FancyBboxPatch((5.5, 4), 4, 3,
                                    boxstyle="round,pad=0.2",
                                    facecolor=self.colors['light'],
                                    edgecolor=self.colors['secondary'])
        ax.add_patch(feature_box)

        ax.text(7.5, 6.5, "6维 → 63维特征扩展", ha='center', va='center',
               fontsize=self.content_fontsize, weight='bold', color=self.colors['secondary'])

        features = [
            "原始角度: θ₁, θ₂, ..., θ₆",
            "三角函数: sin(θ), cos(θ)",
            "多项式: θ², θ³",
            "交互项: θᵢ × θⱼ",
            "工作空间特征",
            "奇异性检测"
        ]

        for i, feature in enumerate(features):
            ax.text(5.7, 6 - i*0.3, f"• {feature}", fontsize=self.small_fontsize,
                   color=self.colors['text'])

        # 底部要点
        ax.text(5, 1.5, "💡 关键思想：让机器更容易理解数据中的模式",
               ha='center', va='center', fontsize=self.content_fontsize,
               color=self.colors['accent'], weight='bold')

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/03_数据预处理策略.png", dpi=300, bbox_inches='tight')
        plt.close()

    def slide_4_model_selection(self):
        """第4页：模型选择策略"""
        fig, ax = self.create_slide_template("模型选择：从简单到复杂的进化",
                                           "没有万能的模型，只有合适的模型")

        # 模型复杂度阶梯
        models = [
            ("线性回归", "简单快速", self.colors['success']),
            ("多项式回归", "处理非线性", self.colors['accent']),
            ("支持向量机", "核技巧", self.colors['primary']),
            ("随机森林", "集成学习", self.colors['secondary']),
            ("神经网络", "深度学习", '#8E44AD'),
            ("Transformer", "注意力机制", '#E74C3C')
        ]

        # 绘制阶梯图
        for i, (model, desc, color) in enumerate(models):
            x = 1 + i * 1.3
            y = 2 + i * 0.8

            # 模型框
            model_box = FancyBboxPatch((x-0.5, y-0.3), 1, 0.6,
                                      boxstyle="round,pad=0.1",
                                      facecolor=color,
                                      edgecolor='none')
            ax.add_patch(model_box)

            # 模型名称
            ax.text(x, y, model, ha='center', va='center',
                   fontsize=self.small_fontsize, color='white', weight='bold')

            # 描述
            ax.text(x, y-0.6, desc, ha='center', va='center',
                   fontsize=10, color=color)

            # 连接线
            if i < len(models) - 1:
                ax.arrow(x+0.5, y, 0.3, 0.8, head_width=0.1, head_length=0.1,
                        fc='gray', ec='gray', alpha=0.5)

        # 选择原则
        ax.text(1, 7.5, "模型选择原则", fontsize=self.subtitle_fontsize,
               color=self.colors['primary'], weight='bold')

        principles = [
            "从简单开始：建立基线",
            "逐步增加复杂度",
            "考虑数据量大小",
            "平衡精度与可解释性",
            "考虑计算资源限制"
        ]

        for i, principle in enumerate(principles):
            ax.text(1, 7 - i*0.3, f"• {principle}", fontsize=self.small_fontsize,
                   color=self.colors['text'])

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/04_模型选择策略.png", dpi=300, bbox_inches='tight')
        plt.close()

    def slide_5_advanced_techniques(self):
        """第5页：先进技术融合"""
        fig, ax = self.create_slide_template("先进技术融合：1+1+1>3",
                                           "如何组合多种技术发挥协同效应")

        # 中心：核心问题
        center_circle = Circle((5, 5), 1, facecolor=self.colors['primary'], alpha=0.8)
        ax.add_patch(center_circle)
        ax.text(5, 5, "机器人\n误差补偿", ha='center', va='center',
               fontsize=self.content_fontsize, color='white', weight='bold')

        # 三个技术圆圈
        techniques = [
            ("PINN\n物理约束", (2, 7), self.colors['accent']),
            ("Transformer\n注意力机制", (8, 7), self.colors['secondary']),
            ("NSGA-II\n多目标优化", (5, 2), self.colors['success'])
        ]

        for name, pos, color in techniques:
            tech_circle = Circle(pos, 0.8, facecolor=color, alpha=0.7)
            ax.add_patch(tech_circle)
            ax.text(pos[0], pos[1], name, ha='center', va='center',
                   fontsize=self.small_fontsize, color='white', weight='bold')

            # 连接线到中心
            ax.plot([pos[0], 5], [pos[1], 5], '--', color=color, linewidth=2, alpha=0.6)

        # 融合优势
        advantages = [
            "物理一致性保证",
            "复杂关系建模",
            "多目标平衡优化",
            "更强泛化能力"
        ]

        ax.text(1, 1, "融合优势:", fontsize=self.content_fontsize,
               color=self.colors['primary'], weight='bold')

        for i, adv in enumerate(advantages):
            ax.text(1, 0.5 - i*0.3, f"✓ {adv}", fontsize=self.small_fontsize,
                   color=self.colors['text'])

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/05_先进技术融合.png", dpi=300, bbox_inches='tight')
        plt.close()

    def slide_6_training_strategies(self):
        """第6页：训练策略与技巧"""
        fig, ax = self.create_slide_template("训练策略：让模型学得更好",
                                           "实用的训练技巧和经验")

        # 左侧：训练流程
        ax.text(0.5, 7.5, "训练流程优化", fontsize=self.subtitle_fontsize,
               color=self.colors['primary'], weight='bold')

        training_steps = [
            "数据划分：训练/验证/测试",
            "损失函数设计：加权+约束",
            "优化器选择：Adam/AdamW",
            "学习率调度：动态调整",
            "正则化：Dropout/BatchNorm",
            "早停机制：防止过拟合"
        ]

        for i, step in enumerate(training_steps):
            # 步骤编号
            step_circle = Circle((0.8, 6.8 - i*0.6), 0.15,
                               facecolor=self.colors['accent'], edgecolor='white')
            ax.add_patch(step_circle)
            ax.text(0.8, 6.8 - i*0.6, str(i+1), ha='center', va='center',
                   fontsize=10, color='white', weight='bold')

            # 步骤内容
            ax.text(1.2, 6.8 - i*0.6, step, ha='left', va='center',
                   fontsize=self.small_fontsize, color=self.colors['text'])

        # 右侧：常见问题与解决方案
        ax.text(5.5, 7.5, "常见问题与解决方案", fontsize=self.subtitle_fontsize,
               color=self.colors['secondary'], weight='bold')

        problems_solutions = [
            ("过拟合", "增加正则化、减少模型复杂度"),
            ("欠拟合", "增加模型容量、更多特征"),
            ("梯度消失", "残差连接、更好的激活函数"),
            ("训练不稳定", "梯度裁剪、批标准化"),
            ("收敛慢", "学习率调度、预训练")
        ]

        for i, (problem, solution) in enumerate(problems_solutions):
            y_pos = 6.8 - i*0.8

            # 问题框
            problem_box = FancyBboxPatch((5.5, y_pos-0.15), 1.5, 0.3,
                                        boxstyle="round,pad=0.05",
                                        facecolor=self.colors['success'],
                                        alpha=0.8)
            ax.add_patch(problem_box)
            ax.text(6.25, y_pos, problem, ha='center', va='center',
                   fontsize=self.small_fontsize, color='white', weight='bold')

            # 箭头
            ax.arrow(7.1, y_pos, 0.3, 0, head_width=0.08, head_length=0.1,
                    fc=self.colors['text'], ec=self.colors['text'])

            # 解决方案
            ax.text(7.6, y_pos, solution, ha='left', va='center',
                   fontsize=10, color=self.colors['text'])

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/06_训练策略技巧.png", dpi=300, bbox_inches='tight')
        plt.close()

    def slide_7_evaluation_methods(self):
        """第7页：评估方法与指标"""
        fig, ax = self.create_slide_template("模型评估：如何判断好坏",
                                           "全面的评估体系")

        # 评估维度
        dimensions = [
            ("准确性", ["MSE", "MAE", "R²"], self.colors['primary']),
            ("稳定性", ["交叉验证", "方差分析"], self.colors['secondary']),
            ("泛化性", ["测试集", "新数据"], self.colors['accent']),
            ("效率性", ["推理时间", "内存占用"], self.colors['success'])
        ]

        # 绘制评估维度
        positions = [(2, 6), (8, 6), (2, 3), (8, 3)]

        for i, ((dim_name, metrics, color), pos) in enumerate(zip(dimensions, positions)):
            # 维度框
            dim_box = FancyBboxPatch((pos[0]-1, pos[1]-1), 2, 2,
                                    boxstyle="round,pad=0.1",
                                    facecolor=color,
                                    alpha=0.2,
                                    edgecolor=color,
                                    linewidth=2)
            ax.add_patch(dim_box)

            # 维度名称
            ax.text(pos[0], pos[1]+0.5, dim_name, ha='center', va='center',
                   fontsize=self.content_fontsize, color=color, weight='bold')

            # 指标列表
            for j, metric in enumerate(metrics):
                ax.text(pos[0], pos[1] - 0.2 - j*0.3, f"• {metric}",
                       ha='center', va='center',
                       fontsize=self.small_fontsize, color=self.colors['text'])

        # 底部：我们项目的评估结果
        result_box = FancyBboxPatch((1, 0.5), 8, 1,
                                   boxstyle="round,pad=0.1",
                                   facecolor=self.colors['light'],
                                   edgecolor=self.colors['primary'])
        ax.add_patch(result_box)

        ax.text(5, 1.2, "我们项目的评估结果", ha='center', va='center',
               fontsize=self.content_fontsize, color=self.colors['primary'], weight='bold')

        results = "位置精度提升88.7% | 角度精度提升81.6% | 推理时间<3ms | R²=0.95"
        ax.text(5, 0.8, results, ha='center', va='center',
               fontsize=self.small_fontsize, color=self.colors['text'])

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/07_评估方法指标.png", dpi=300, bbox_inches='tight')
        plt.close()

    def slide_8_practical_tips(self):
        """第8页：实用技巧与经验"""
        fig, ax = self.create_slide_template("实用技巧：避免常见坑",
                                           "从实践中总结的经验")

        # 技巧分类
        tips_categories = [
            ("数据处理", [
                "永远先可视化数据",
                "检查数据分布和异常值",
                "特征工程比模型更重要",
                "保留原始数据备份"
            ]),
            ("模型训练", [
                "从简单模型开始",
                "设置合理的验证集",
                "监控训练过程",
                "保存最佳模型"
            ]),
            ("调试技巧", [
                "逐步增加复杂度",
                "可视化中间结果",
                "对比不同方法",
                "记录实验过程"
            ])
        ]

        # 绘制技巧
        x_positions = [1.5, 5, 8.5]
        colors = [self.colors['primary'], self.colors['secondary'], self.colors['accent']]

        for i, ((category, tips), x_pos, color) in enumerate(zip(tips_categories, x_positions, colors)):
            # 类别标题
            ax.text(x_pos, 7.5, category, ha='center', va='center',
                   fontsize=self.content_fontsize, color=color, weight='bold')

            # 技巧列表
            for j, tip in enumerate(tips):
                tip_box = FancyBboxPatch((x_pos-1.2, 6.8-j*0.8), 2.4, 0.6,
                                        boxstyle="round,pad=0.05",
                                        facecolor=color,
                                        alpha=0.1,
                                        edgecolor=color)
                ax.add_patch(tip_box)

                ax.text(x_pos, 6.8-j*0.8+0.3, tip, ha='center', va='center',
                       fontsize=10, color=self.colors['text'])

        # 底部警告
        warning_box = FancyBboxPatch((1, 1.5), 8, 1,
                                    boxstyle="round,pad=0.1",
                                    facecolor='#FFE5E5',
                                    edgecolor='#FF6B6B')
        ax.add_patch(warning_box)

        ax.text(5, 2.2, "⚠️ 常见错误", ha='center', va='center',
               fontsize=self.content_fontsize, color='#FF6B6B', weight='bold')

        warnings = "数据泄露 | 过度拟合验证集 | 忽略物理约束 | 盲目追求复杂模型"
        ax.text(5, 1.8, warnings, ha='center', va='center',
               fontsize=self.small_fontsize, color='#FF6B6B')

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/08_实用技巧经验.png", dpi=300, bbox_inches='tight')
        plt.close()

    def slide_9_future_trends(self):
        """第9页：未来发展趋势"""
        fig, ax = self.create_slide_template("未来发展趋势",
                                           "机器学习在工程中的发展方向")

        # 趋势时间线
        trends = [
            ("2024", "多模态融合", "视觉+力觉+位置"),
            ("2025", "自监督学习", "减少标注需求"),
            ("2026", "联邦学习", "多机器人协同"),
            ("2027", "神经符号AI", "知识+数据驱动"),
            ("2028+", "通用机器人智能", "一个模型多任务")
        ]

        # 绘制时间线
        for i, (year, trend, desc) in enumerate(trends):
            x = 1 + i * 2
            y = 5

            # 时间点
            time_circle = Circle((x, y), 0.3, facecolor=self.colors['primary'])
            ax.add_patch(time_circle)
            ax.text(x, y, year, ha='center', va='center',
                   fontsize=10, color='white', weight='bold')

            # 趋势名称
            ax.text(x, y+1, trend, ha='center', va='center',
                   fontsize=self.content_fontsize, color=self.colors['primary'], weight='bold')

            # 描述
            ax.text(x, y-1, desc, ha='center', va='center',
                   fontsize=self.small_fontsize, color=self.colors['text'])

            # 连接线
            if i < len(trends) - 1:
                ax.plot([x+0.3, x+1.7], [y, y], '-', color=self.colors['secondary'], linewidth=3)

        # 关键技术
        ax.text(5, 2.5, "关键技术发展", ha='center', va='center',
               fontsize=self.subtitle_fontsize, color=self.colors['secondary'], weight='bold')

        key_techs = [
            "Transformer → 更强的序列建模",
            "PINN → 物理约束的深度融合",
            "AutoML → 自动化模型设计",
            "边缘计算 → 实时推理优化"
        ]

        for i, tech in enumerate(key_techs):
            ax.text(5, 2 - i*0.3, f"• {tech}", ha='center', va='center',
                   fontsize=self.small_fontsize, color=self.colors['text'])

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/09_未来发展趋势.png", dpi=300, bbox_inches='tight')
        plt.close()

    def slide_10_summary(self):
        """第10页：总结"""
        fig, ax = self.create_slide_template("总结：机器学习方法论",
                                           "系统性思维解决工程问题")

        # 核心要点
        key_points = [
            "🎯 问题导向：从工程需求出发，明确目标",
            "📊 数据为王：高质量数据胜过复杂模型",
            "🔧 循序渐进：从简单到复杂，逐步优化",
            "🧠 技术融合：组合多种技术发挥协同效应",
            "📈 持续改进：评估-分析-优化的闭环"
        ]

        for i, point in enumerate(key_points):
            # 要点框
            point_box = FancyBboxPatch((0.5, 6.5-i*1), 9, 0.8,
                                      boxstyle="round,pad=0.1",
                                      facecolor=self.colors['light'],
                                      edgecolor=self.colors['primary'])
            ax.add_patch(point_box)

            ax.text(5, 6.9-i*1, point, ha='center', va='center',
                   fontsize=self.content_fontsize, color=self.colors['text'], weight='bold')

        # 成功案例
        success_box = FancyBboxPatch((2, 0.5), 6, 1.5,
                                    boxstyle="round,pad=0.1",
                                    facecolor=self.colors['accent'],
                                    alpha=0.2,
                                    edgecolor=self.colors['accent'])
        ax.add_patch(success_box)

        ax.text(5, 1.6, "我们的成功实践", ha='center', va='center',
               fontsize=self.content_fontsize, color=self.colors['accent'], weight='bold')

        ax.text(5, 1.1, "机器人定位精度提升88.7%", ha='center', va='center',
               fontsize=self.content_fontsize, color=self.colors['text'], weight='bold')

        ax.text(5, 0.7, "PINN + Transformer + NSGA-II 技术融合", ha='center', va='center',
               fontsize=self.small_fontsize, color=self.colors['text'])

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/10_总结.png", dpi=300, bbox_inches='tight')
        plt.close()

    def generate_all_slides(self):
        """生成所有PPT页面"""
        print("🎨 开始生成机器学习方法论PPT...")

        slides = [
            ("标题页", self.slide_1_title),
            ("问题分析框架", self.slide_2_problem_analysis),
            ("数据预处理策略", self.slide_3_data_preprocessing),
            ("模型选择策略", self.slide_4_model_selection),
            ("先进技术融合", self.slide_5_advanced_techniques),
            ("训练策略技巧", self.slide_6_training_strategies),
            ("评估方法指标", self.slide_7_evaluation_methods),
            ("实用技巧经验", self.slide_8_practical_tips),
            ("未来发展趋势", self.slide_9_future_trends),
            ("总结", self.slide_10_summary)
        ]

        for slide_name, slide_func in slides:
            print(f"  生成: {slide_name}")
            slide_func()

        print(f"\n✅ PPT生成完成！共{len(slides)}页")
        print(f"📁 文件位置: {self.output_dir}/")
        print("🎯 可以直接插入到PowerPoint或其他演示软件中使用")

def main():
    """主函数"""
    generator = MLMethodologyPPT()
    generator.generate_all_slides()

if __name__ == "__main__":
    main()