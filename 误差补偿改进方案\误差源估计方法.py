#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于现有数据的误差源贡献估计方法
说明数据限制和正确的计算方法

作者: AI助手
日期: 2025年
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import signal
from scipy.optimize import curve_fit
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

from 理论计算模块 import RobotKinematics

class ErrorSourceEstimation:
    """误差源贡献估计（基于数据限制的近似方法）"""
    
    def __init__(self):
        self.robot = RobotKinematics()
        
    def load_data(self):
        """加载现有数据"""
        print("=== 数据分析 ===")
        
        # 加载数据
        self.theta_command = pd.read_excel('../theta2000.xlsx', header=None).values
        self.real_poses = pd.read_excel('../real2000.xlsx').values
        
        # 计算理论位姿
        self.theoretical_poses = []
        for joints in self.theta_command:
            pose = self.robot.forward_kinematics(joints)
            self.theoretical_poses.append(pose)
        self.theoretical_poses = np.array(self.theoretical_poses)
        
        # 计算总误差
        self.total_errors = self.real_poses - self.theoretical_poses
        
        # 修复角度连续性
        for i in range(self.total_errors.shape[0]):
            for j in range(3, 6):
                error = self.total_errors[i, j]
                candidates = [error, error + 360, error - 360]
                self.total_errors[i, j] = min(candidates, key=abs)
        
        print(f"数据加载完成: {self.theta_command.shape}")
        print(f"总位置误差: {np.mean(np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1))):.6f} mm")
        print(f"总角度误差: {np.median(np.abs(self.total_errors[:, 3:])):.6f} 度")
        
    def analyze_data_limitations(self):
        """分析数据限制"""
        print("\n=== 数据限制分析 ===")
        
        print("现有数据:")
        print("✅ theta2000.xlsx: 指令关节角度 (2000×6)")
        print("✅ real2000.xlsx: 实测末端位姿 (2000×6)")
        print("✅ 总误差 = 实测位姿 - 理论位姿")
        
        print("\n缺失的关键数据:")
        print("❌ 实际关节角度 (编码器反馈)")
        print("❌ 关节力矩 (电机电流或力矩传感器)")
        print("❌ 负载信息 (末端工具重量)")
        print("❌ 温度数据 (影响刚度和间隙)")
        print("❌ 关节速度和加速度")
        
        print("\n数据限制的影响:")
        print("🔸 无法直接测量减速机传动误差")
        print("🔸 无法直接测量连杆柔性变形")
        print("🔸 只能进行误差源贡献估计")
        print("🔸 无法与现有补偿实验直接匹配")
        
    def estimate_gear_error_contribution(self):
        """估计减速机误差贡献（基于周期性分析）"""
        print("\n=== 减速机误差贡献估计 ===")
        
        gear_contributions = {}
        
        for joint_idx in range(6):
            joint_angles = self.theta_command[:, joint_idx]
            pos_errors = np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1))
            
            # 周期性分析
            angle_rad = np.deg2rad(joint_angles)
            
            # 拟合周期性模型: E = A*sin(θ) + B*cos(θ) + C*sin(2θ) + D*cos(2θ) + E0
            design_matrix = np.column_stack([
                np.sin(angle_rad),
                np.cos(angle_rad),
                np.sin(2*angle_rad),
                np.cos(2*angle_rad),
                np.ones(len(angle_rad))
            ])
            
            # 最小二乘拟合
            coeffs, residuals, rank, s = np.linalg.lstsq(design_matrix, pos_errors, rcond=None)
            A, B, C, D, E0 = coeffs
            
            # 计算周期性误差的幅值
            amplitude_1st = np.sqrt(A**2 + B**2)  # 一阶谐波
            amplitude_2nd = np.sqrt(C**2 + D**2)  # 二阶谐波
            total_periodic = amplitude_1st + amplitude_2nd
            
            # 计算拟合优度
            predicted = design_matrix @ coeffs
            r_squared = 1 - np.sum((pos_errors - predicted)**2) / np.sum((pos_errors - np.mean(pos_errors))**2)
            
            gear_contributions[f'joint_{joint_idx+1}'] = {
                'amplitude_1st': amplitude_1st,
                'amplitude_2nd': amplitude_2nd,
                'total_periodic': total_periodic,
                'r_squared': r_squared,
                'estimated_contribution': total_periodic * r_squared  # 加权贡献
            }
            
            print(f"关节 {joint_idx+1}:")
            print(f"  一阶谐波幅值: {amplitude_1st:.6f} mm")
            print(f"  二阶谐波幅值: {amplitude_2nd:.6f} mm")
            print(f"  拟合优度 R²: {r_squared:.4f}")
            print(f"  估计贡献: {total_periodic * r_squared:.6f} mm")
        
        # 总的减速机误差估计
        total_gear_contribution = sum([v['estimated_contribution'] for v in gear_contributions.values()])
        total_error = np.mean(np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1)))
        gear_percentage = total_gear_contribution / total_error * 100
        
        print(f"\n减速机误差总估计:")
        print(f"  估计贡献: {total_gear_contribution:.6f} mm")
        print(f"  占总误差: {gear_percentage:.1f}%")
        
        self.gear_contributions = gear_contributions
        return gear_contributions
    
    def estimate_flexibility_contribution(self):
        """估计柔性误差贡献（基于位置相关性分析）"""
        print("\n=== 连杆柔性误差贡献估计 ===")
        
        # 计算工作空间位置
        workspace_positions = self.theoretical_poses[:, :3]  # X, Y, Z
        pos_errors = np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1))
        
        # 分析误差与工作空间位置的相关性
        correlations = {}
        for i, axis in enumerate(['X', 'Y', 'Z']):
            corr = np.corrcoef(workspace_positions[:, i], pos_errors)[0, 1]
            correlations[axis] = abs(corr)
        
        # 分析误差与重力方向的相关性（Z轴）
        z_positions = workspace_positions[:, 2]
        z_correlation = abs(np.corrcoef(z_positions, pos_errors)[0, 1])
        
        # 估计重力相关的柔性误差
        # 假设重力载荷主要影响Z方向
        z_range = np.max(z_positions) - np.min(z_positions)
        estimated_gravity_effect = z_correlation * np.std(pos_errors) * (z_range / 1000)  # 转换为mm
        
        flexibility_estimation = {
            'position_correlations': correlations,
            'z_correlation': z_correlation,
            'estimated_gravity_effect': estimated_gravity_effect,
            'estimated_contribution': estimated_gravity_effect * 0.1  # 保守估计
        }
        
        print(f"位置相关性分析:")
        for axis, corr in correlations.items():
            print(f"  {axis}轴相关性: {corr:.4f}")
        
        print(f"重力效应估计:")
        print(f"  Z轴相关性: {z_correlation:.4f}")
        print(f"  估计重力效应: {estimated_gravity_effect:.6f} mm")
        print(f"  估计柔性贡献: {flexibility_estimation['estimated_contribution']:.6f} mm")
        
        self.flexibility_estimation = flexibility_estimation
        return flexibility_estimation
    
    def explain_correct_calculation_method(self):
        """说明正确的计算方法"""
        print("\n" + "="*80)
        print("📚 正确的误差源计算方法说明")
        print("="*80)
        
        print("\n🔧 减速机传动误差的正确计算:")
        print("需要的数据:")
        print("  1. θ_command: 控制器指令角度")
        print("  2. θ_encoder: 关节编码器实际角度")
        print("  3. θ̇: 关节角速度 (用于间隙分析)")
        
        print("\n计算公式:")
        print("  基本传动误差 = θ_encoder - θ_command")
        print("  周期性误差 = Σ[Ai·sin(ni·θ + φi)]")
        print("  间隙误差 = f(θ̇, direction)")
        print("  总传动误差 = 基本误差 + 周期性误差 + 间隙误差")
        
        print("\n🔗 连杆柔性误差的正确计算:")
        print("需要的数据:")
        print("  1. τ_external: 外部载荷 (重力+惯性+外力)")
        print("  2. K_link: 连杆刚度矩阵")
        print("  3. 连杆几何和质量参数")
        
        print("\n计算公式:")
        print("  重力载荷 = Σ[mi·g·ri(θ)]")
        print("  惯性载荷 = Σ[Ii·α + ω×(Ii·ω)]")
        print("  角度变形 = K^(-1)·τ_total")
        print("  末端误差 = J(θ)·角度变形")
        
        print("\n📊 基于现有数据的局限性:")
        print("✅ 可以做的:")
        print("  - 误差源贡献估计")
        print("  - 周期性特征分析")
        print("  - 相关性分析")
        print("  - 统计特征提取")
        
        print("\n❌ 无法精确做的:")
        print("  - 直接测量传动误差")
        print("  - 精确计算柔性变形")
        print("  - 误差源完全分离")
        print("  - 与补偿实验直接匹配")
        
    def suggest_data_collection_method(self):
        """建议数据采集方法"""
        print("\n" + "="*80)
        print("💡 建议的完整数据采集方案")
        print("="*80)
        
        print("\n🎯 理想的数据采集:")
        print("1. 关节数据:")
        print("   - 指令角度 θ_command")
        print("   - 编码器角度 θ_encoder")
        print("   - 关节角速度 θ̇")
        print("   - 关节角加速度 θ̈")
        print("   - 电机电流/力矩 τ_motor")
        
        print("\n2. 末端数据:")
        print("   - 激光跟踪仪位姿 (已有)")
        print("   - 末端负载信息")
        print("   - 运动轨迹信息")
        
        print("\n3. 环境数据:")
        print("   - 关节温度")
        print("   - 环境温度")
        print("   - 湿度")
        
        print("\n🔄 数据采集流程:")
        print("1. 静态标定:")
        print("   - 不同负载下的静态位姿测量")
        print("   - 用于分离重力效应")
        
        print("\n2. 动态测试:")
        print("   - 不同速度的运动测试")
        print("   - 用于分析惯性效应")
        
        print("\n3. 温度测试:")
        print("   - 不同温度下的重复测量")
        print("   - 用于分析热效应")
        
    def create_limitation_analysis_report(self):
        """生成数据限制分析报告"""
        print("\n=== 生成分析报告 ===")
        
        # 创建对比图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 当前数据vs理想数据对比
        current_data = ['指令角度', '末端位姿']
        ideal_data = ['指令角度', '编码器角度', '关节力矩', '末端位姿', '负载信息', '温度数据']
        
        axes[0, 0].barh(range(len(current_data)), [1, 1], color='lightcoral', alpha=0.7, label='现有数据')
        axes[0, 0].barh(range(len(ideal_data)), [1]*len(ideal_data), color='lightblue', alpha=0.7, label='理想数据')
        axes[0, 0].set_yticks(range(max(len(current_data), len(ideal_data))))
        axes[0, 0].set_yticklabels(ideal_data)
        axes[0, 0].set_title('数据完整性对比', fontsize=14, fontweight='bold')
        axes[0, 0].legend()
        
        # 2. 误差源分析能力
        error_sources = ['几何误差', '传动误差', '柔性误差', '热误差']
        analysis_capability = [100, 30, 10, 0]  # 百分比
        
        axes[0, 1].bar(error_sources, analysis_capability, color=['green', 'orange', 'red', 'gray'], alpha=0.7)
        axes[0, 1].set_title('误差源分析能力', fontsize=14, fontweight='bold')
        axes[0, 1].set_ylabel('分析精度 (%)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 周期性分析示例
        joint_idx = 0  # 关节1
        joint_angles = self.theta_command[:, joint_idx]
        pos_errors = np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1))
        
        axes[1, 0].scatter(joint_angles, pos_errors, alpha=0.6, s=1)
        axes[1, 0].set_xlabel('关节1角度 (度)')
        axes[1, 0].set_ylabel('位置误差 (mm)')
        axes[1, 0].set_title('周期性误差分析示例', fontsize=14, fontweight='bold')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 误差贡献估计
        if hasattr(self, 'gear_contributions'):
            gear_contrib = sum([v['estimated_contribution'] for v in self.gear_contributions.values()])
            flex_contrib = self.flexibility_estimation['estimated_contribution']
            total_error = np.mean(np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1)))
            geom_contrib = total_error - gear_contrib - flex_contrib
            
            contributions = [geom_contrib, gear_contrib, flex_contrib]
            labels = ['几何误差', '传动误差', '柔性误差']
            colors = ['#ff9999', '#66b3ff', '#99ff99']
            
            axes[1, 1].pie(contributions, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            axes[1, 1].set_title('误差源贡献估计', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('输出结果/数据限制分析报告.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 数据限制分析报告已生成")
    
    def run_complete_analysis(self):
        """运行完整的数据限制分析"""
        print("="*80)
        print("📊 基于现有数据的误差源分析（数据限制版本）")
        print("="*80)
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 分析数据限制
        self.analyze_data_limitations()
        
        # 3. 估计减速机误差贡献
        self.estimate_gear_error_contribution()
        
        # 4. 估计柔性误差贡献
        self.estimate_flexibility_contribution()
        
        # 5. 说明正确方法
        self.explain_correct_calculation_method()
        
        # 6. 建议数据采集
        self.suggest_data_collection_method()
        
        # 7. 生成报告
        self.create_limitation_analysis_report()
        
        print("\n✅ 数据限制分析完成!")
        print("📝 重要提醒: 当前分析仅为误差源贡献估计，不是精确计算!")

def main():
    """主函数"""
    import os
    os.makedirs("输出结果", exist_ok=True)
    
    analyzer = ErrorSourceEstimation()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
