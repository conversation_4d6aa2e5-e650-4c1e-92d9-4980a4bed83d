#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
误差补偿策略详解
通过具体例子说明误差补偿策略的原理和优势
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from 理论计算模块 import RobotKinematics

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def demonstrate_error_compensation_concept():
    """演示误差补偿策略的基本概念"""
    print("="*80)
    print("误差补偿策略详解")
    print("="*80)
    
    # 加载实际数据
    joint_data = pd.read_excel('../theta2000.xlsx', header=None)
    joint_data.columns = [f'theta_{i+1}' for i in range(6)]
    measured_data = pd.read_excel('../real2000.xlsx')
    
    # 计算理论值
    robot = RobotKinematics()
    
    print("\n1. 基本概念演示")
    print("-" * 50)
    
    # 选择一个具体例子
    sample_idx = 0
    joint_angles = joint_data.iloc[sample_idx].values
    measured_pose = measured_data.iloc[sample_idx].values
    theoretical_pose = robot.forward_kinematics(joint_angles)
    
    print(f"样本 {sample_idx + 1}:")
    print(f"关节角度: [{', '.join([f'{angle:.1f}°' for angle in joint_angles])}]")
    print(f"理论位姿: [{', '.join([f'{val:.2f}' for val in theoretical_pose])}]")
    print(f"实测位姿: [{', '.join([f'{val:.2f}' for val in measured_pose])}]")
    
    # 计算误差
    error = measured_pose - theoretical_pose
    print(f"实际误差: [{', '.join([f'{val:.3f}' for val in error])}]")
    
    print(f"\n2. 两种策略对比")
    print("-" * 50)
    
    print("策略A - 直接预测法:")
    print("  输入: 关节角度 [θ1, θ2, θ3, θ4, θ5, θ6]")
    print("  输出: 位姿预测 [X, Y, Z, Rx, Ry, Rz]")
    print("  目标: 直接学习 关节角度 → 位姿 的映射")
    print("  挑战: 位姿值范围大，变化复杂")
    
    print("\n策略B - 误差补偿法:")
    print("  输入: 关节角度 [θ1, θ2, θ3, θ4, θ5, θ6]")
    print("  输出: 误差预测 [ΔX, ΔY, ΔZ, ΔRx, ΔRy, ΔRz]")
    print("  目标: 学习 关节角度 → 误差 的映射")
    print("  优势: 误差值范围小，更容易学习")
    print("  最终: 位姿 = 理论值 + 预测误差")

def analyze_data_characteristics():
    """分析数据特征，说明为什么误差补偿更有效"""
    print(f"\n3. 数据特征分析")
    print("-" * 50)
    
    # 加载数据
    joint_data = pd.read_excel('../theta2000.xlsx', header=None)
    joint_data.columns = [f'theta_{i+1}' for i in range(6)]
    measured_data = pd.read_excel('../real2000.xlsx')
    
    # 计算理论值和误差
    robot = RobotKinematics()
    theoretical_poses = []
    for i in range(min(100, len(joint_data))):  # 只分析前100个样本
        joint_angles = joint_data.iloc[i].values
        pose = robot.forward_kinematics(joint_angles)
        theoretical_poses.append(pose)
    
    theoretical_poses = np.array(theoretical_poses)
    measured_poses = measured_data.iloc[:len(theoretical_poses)].values
    errors = measured_poses - theoretical_poses
    
    # 统计分析
    print("位姿绝对值 vs 误差值的数据特征对比:")
    print()
    
    coord_names = ['X(mm)', 'Y(mm)', 'Z(mm)', 'Rx(°)', 'Ry(°)', 'Rz(°)']
    
    print(f"{'坐标':<8} {'位姿范围':<20} {'位姿标准差':<12} {'误差范围':<20} {'误差标准差':<12} {'学习难度'}")
    print("-" * 90)
    
    for i, coord in enumerate(coord_names):
        # 位姿统计
        pose_min = min(measured_poses[:, i].min(), theoretical_poses[:, i].min())
        pose_max = max(measured_poses[:, i].max(), theoretical_poses[:, i].max())
        pose_std = np.std(measured_poses[:, i])
        pose_range = pose_max - pose_min
        
        # 误差统计
        error_min = errors[:, i].min()
        error_max = errors[:, i].max()
        error_std = np.std(errors[:, i])
        error_range = error_max - error_min
        
        # 学习难度评估
        difficulty_ratio = pose_range / error_range if error_range > 0 else float('inf')
        if difficulty_ratio > 100:
            difficulty = "误差更易学习"
        elif difficulty_ratio > 10:
            difficulty = "误差较易学习"
        else:
            difficulty = "差异不大"
        
        print(f"{coord:<8} [{pose_min:6.1f}, {pose_max:6.1f}] {pose_std:10.2f}   "
              f"[{error_min:6.3f}, {error_max:6.3f}] {error_std:10.3f}   {difficulty}")

def visualize_error_compensation():
    """可视化误差补偿策略"""
    print(f"\n4. 可视化演示")
    print("-" * 50)
    
    # 加载数据
    joint_data = pd.read_excel('../theta2000.xlsx', header=None)
    measured_data = pd.read_excel('../real2000.xlsx')
    
    # 计算理论值和误差
    robot = RobotKinematics()
    n_samples = 50  # 只显示前50个样本
    
    theoretical_poses = []
    for i in range(n_samples):
        joint_angles = joint_data.iloc[i].values
        pose = robot.forward_kinematics(joint_angles)
        theoretical_poses.append(pose)
    
    theoretical_poses = np.array(theoretical_poses)
    measured_poses = measured_data.iloc[:n_samples].values
    errors = measured_poses - theoretical_poses
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    coord_names = ['X', 'Y', 'Z', 'Rx', 'Ry', 'Rz']
    units = ['mm', 'mm', 'mm', '°', '°', '°']
    
    for i, (coord, unit) in enumerate(zip(coord_names, units)):
        row = i // 3
        col = i % 3
        ax = axes[row, col]
        
        # 绘制位姿值和误差值的分布
        sample_indices = range(n_samples)
        
        # 位姿绝对值
        ax.plot(sample_indices, measured_poses[:, i], 'b-', label=f'实测{coord}', linewidth=2)
        ax.plot(sample_indices, theoretical_poses[:, i], 'g--', label=f'理论{coord}', linewidth=2)
        
        # 在右侧y轴显示误差
        ax2 = ax.twinx()
        ax2.plot(sample_indices, errors[:, i], 'r-', label=f'{coord}误差', linewidth=2, alpha=0.7)
        
        ax.set_xlabel('样本编号')
        ax.set_ylabel(f'{coord} 位姿值 ({unit})', color='blue')
        ax2.set_ylabel(f'{coord} 误差 ({unit})', color='red')
        
        ax.tick_params(axis='y', labelcolor='blue')
        ax2.tick_params(axis='y', labelcolor='red')
        
        ax.set_title(f'{coord} 坐标: 位姿值 vs 误差值')
        ax.grid(True, alpha=0.3)
        
        # 添加图例
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper right')
    
    plt.tight_layout()
    plt.savefig('误差补偿策略可视化.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("可视化图表已保存: 误差补偿策略可视化.png")

def demonstrate_learning_difficulty():
    """演示学习难度的差异"""
    print(f"\n5. 学习难度对比")
    print("-" * 50)
    
    # 模拟数据
    np.random.seed(42)
    n_samples = 1000
    
    # 模拟位姿数据（大范围变化）
    pose_data = np.random.normal(0, 100, n_samples)  # 位姿值：均值0，标准差100
    
    # 模拟误差数据（小范围变化）
    error_data = np.random.normal(0, 1, n_samples)   # 误差值：均值0，标准差1
    
    print("模拟学习任务对比:")
    print(f"任务A - 预测位姿值:")
    print(f"  数据范围: [{pose_data.min():.1f}, {pose_data.max():.1f}]")
    print(f"  标准差: {pose_data.std():.1f}")
    print(f"  学习目标: 预测大范围变化的位姿值")
    
    print(f"\n任务B - 预测误差值:")
    print(f"  数据范围: [{error_data.min():.3f}, {error_data.max():.3f}]")
    print(f"  标准差: {error_data.std():.3f}")
    print(f"  学习目标: 预测小范围变化的误差值")
    
    print(f"\n学习难度分析:")
    print(f"  数据范围比: {(pose_data.max() - pose_data.min()) / (error_data.max() - error_data.min()):.1f}:1")
    print(f"  标准差比: {pose_data.std() / error_data.std():.1f}:1")
    print(f"  结论: 误差预测比位姿预测容易约100倍")

def explain_why_error_compensation_works():
    """解释误差补偿策略为什么有效"""
    print(f"\n6. 误差补偿策略有效的原因")
    print("-" * 50)
    
    reasons = [
        ("数据范围小", "误差值比位姿绝对值小得多，神经网络更容易学习小范围的数值变化"),
        ("分布规律", "误差通常呈正态分布，比位姿的复杂分布更适合机器学习"),
        ("物理基础", "基于理论计算提供了物理上合理的基准，只需学习残差"),
        ("数值稳定", "小数值变化使得梯度更稳定，训练过程更收敛"),
        ("先验知识", "利用了机器人运动学的先验知识，而不是从零开始学习"),
        ("误差相关性", "误差往往与特定的机械特性相关，具有可学习的模式")
    ]
    
    for i, (reason, explanation) in enumerate(reasons, 1):
        print(f"{i}. {reason}")
        print(f"   {explanation}")
        print()

def practical_implementation_guide():
    """实际实现指南"""
    print(f"7. 实际实现指南")
    print("-" * 50)
    
    print("步骤1: 准备数据")
    print("  - 收集关节角度数据")
    print("  - 收集高精度实测位姿数据（如激光跟踪仪）")
    print("  - 使用理论运动学计算对应的理论位姿")
    
    print("\n步骤2: 计算误差")
    print("  - 误差 = 实测位姿 - 理论位姿")
    print("  - 这个误差就是机器学习的目标变量")
    
    print("\n步骤3: 训练模型")
    print("  - 输入: 关节角度")
    print("  - 输出: 误差预测")
    print("  - 使用回归模型（神经网络、随机森林等）")
    
    print("\n步骤4: 预测和补偿")
    print("  - 对新的关节角度，先计算理论位姿")
    print("  - 使用训练好的模型预测误差")
    print("  - 最终位姿 = 理论位姿 + 预测误差")
    
    print("\n代码示例:")
    print("""
    # 计算理论位姿
    theoretical_pose = forward_kinematics(joint_angles)
    
    # 计算训练误差
    training_errors = measured_poses - theoretical_poses
    
    # 训练模型预测误差
    model.fit(joint_angles, training_errors)
    
    # 预测新样本
    predicted_error = model.predict(new_joint_angles)
    compensated_pose = theoretical_pose + predicted_error
    """)

def main():
    """主函数"""
    print("开始误差补偿策略详解...")
    
    # 1. 基本概念演示
    demonstrate_error_compensation_concept()
    
    # 2. 数据特征分析
    analyze_data_characteristics()
    
    # 3. 可视化演示
    visualize_error_compensation()
    
    # 4. 学习难度对比
    demonstrate_learning_difficulty()
    
    # 5. 原理解释
    explain_why_error_compensation_works()
    
    # 6. 实现指南
    practical_implementation_guide()
    
    print("\n" + "="*80)
    print("误差补偿策略详解完成！")
    print("="*80)
    print("关键要点:")
    print("1. 误差补偿 = 理论计算 + 机器学习误差预测")
    print("2. 学习误差比学习绝对值容易得多")
    print("3. 结合了物理知识和数据驱动方法的优势")
    print("4. 在机器人位姿预测中效果显著")

if __name__ == "__main__":
    main()
