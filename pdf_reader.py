#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本提取工具
用于提取论文中的理论计算公式和方法
"""

import pdfplumber
import re

def extract_pdf_text(pdf_path):
    """提取PDF文本内容"""
    text_content = []
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            print(f"PDF总页数: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages, 1):
                print(f"正在处理第 {page_num} 页...")
                
                # 提取文本
                text = page.extract_text()
                if text:
                    text_content.append(f"\n=== 第 {page_num} 页 ===\n")
                    text_content.append(text)
                    text_content.append("\n" + "="*50 + "\n")
                
                # 如果是前几页，详细显示内容
                if page_num <= 3:
                    print(f"第 {page_num} 页内容预览:")
                    print(text[:500] if text else "无文本内容")
                    print("-" * 30)
    
    except Exception as e:
        print(f"PDF读取错误: {str(e)}")
        return None
    
    return "\n".join(text_content)

def find_kinematics_content(text):
    """查找运动学相关内容"""
    if not text:
        return []
    
    # 关键词列表
    keywords = [
        "运动学", "正解", "DH参数", "变换矩阵", "齐次变换",
        "关节角", "位姿", "坐标变换", "欧拉角", "旋转矩阵",
        "理论值", "计算公式", "数学模型", "机器人模型"
    ]
    
    relevant_sections = []
    lines = text.split('\n')
    
    for i, line in enumerate(lines):
        for keyword in keywords:
            if keyword in line:
                # 提取相关段落（前后各5行）
                start = max(0, i-5)
                end = min(len(lines), i+6)
                section = '\n'.join(lines[start:end])
                
                relevant_sections.append({
                    'keyword': keyword,
                    'line_number': i+1,
                    'content': section
                })
                break
    
    return relevant_sections

def extract_formulas(text):
    """提取可能的数学公式"""
    if not text:
        return []
    
    # 查找包含数学符号的行
    formula_patterns = [
        r'.*[=+\-*/].*',  # 包含数学运算符
        r'.*[θφψαβγ].*',  # 包含希腊字母
        r'.*[xyz].*=.*',   # 坐标公式
        r'.*cos.*sin.*',   # 三角函数
        r'.*matrix.*',     # 矩阵相关
        r'.*Matrix.*',
    ]
    
    formulas = []
    lines = text.split('\n')
    
    for i, line in enumerate(lines):
        for pattern in formula_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                formulas.append({
                    'line_number': i+1,
                    'content': line.strip()
                })
                break
    
    return formulas

def main():
    """主函数"""
    pdf_path = "基于支持向量回归的工业机器人空间误差预测_乔贵方 (1).pdf"
    
    print("开始提取PDF内容...")
    
    # 提取文本
    text = extract_pdf_text(pdf_path)
    
    if text:
        # 保存完整文本
        with open("论文文本内容.txt", "w", encoding="utf-8") as f:
            f.write(text)
        print("完整文本已保存到: 论文文本内容.txt")
        
        # 查找运动学相关内容
        print("\n查找运动学相关内容...")
        kinematics_sections = find_kinematics_content(text)
        
        if kinematics_sections:
            print(f"找到 {len(kinematics_sections)} 个相关段落:")
            
            with open("运动学相关内容.txt", "w", encoding="utf-8") as f:
                for section in kinematics_sections:
                    print(f"\n关键词: {section['keyword']} (第{section['line_number']}行)")
                    print(section['content'][:200] + "...")
                    
                    f.write(f"\n=== 关键词: {section['keyword']} (第{section['line_number']}行) ===\n")
                    f.write(section['content'])
                    f.write("\n" + "="*50 + "\n")
            
            print("运动学相关内容已保存到: 运动学相关内容.txt")
        else:
            print("未找到运动学相关内容")
        
        # 查找数学公式
        print("\n查找数学公式...")
        formulas = extract_formulas(text)
        
        if formulas:
            print(f"找到 {len(formulas)} 个可能的公式:")
            
            with open("数学公式.txt", "w", encoding="utf-8") as f:
                for formula in formulas[:20]:  # 只显示前20个
                    print(f"第{formula['line_number']}行: {formula['content']}")
                    f.write(f"第{formula['line_number']}行: {formula['content']}\n")
            
            print("数学公式已保存到: 数学公式.txt")
        else:
            print("未找到数学公式")
    
    else:
        print("PDF文本提取失败")

if __name__ == "__main__":
    main()
