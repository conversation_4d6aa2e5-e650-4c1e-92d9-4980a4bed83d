<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎使用 Apache Tomcat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 800px;
            width: 90%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .feature {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px 20px;
            border-radius: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .feature:nth-child(2) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .feature:nth-child(3) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .feature h3 {
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .feature p {
            font-size: 0.9em;
            opacity: 0.9;
            line-height: 1.5;
        }

        .status {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            border-left: 5px solid #4facfe;
        }

        .status h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #43e97b;
            border-radius: 50%;
            margin-right: 10px;
            animation: blink 1.5s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .footer {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #eee;
            color: #888;
            font-size: 0.9em;
        }

        .version {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            margin: 10px 0;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">T</div>
        
        <h1>欢迎使用 Apache Tomcat</h1>
        
        <p class="subtitle">
            您的Web应用服务器已成功启动并运行！<br>
            Apache Tomcat 是一个开源的Java Servlet容器，为您的Web应用提供强大的支持。
        </p>

        <div class="status">
            <h3><span class="status-indicator"></span>服务器状态：正常运行</h3>
            <p>所有核心服务已启动，准备处理您的请求</p>
        </div>

        <div class="features">
            <div class="feature">
                <h3>🚀 高性能</h3>
                <p>优化的请求处理机制，确保您的应用快速响应</p>
            </div>
            <div class="feature">
                <h3>🔒 安全可靠</h3>
                <p>内置安全机制，保护您的Web应用免受常见攻击</p>
            </div>
            <div class="feature">
                <h3>⚙️ 易于管理</h3>
                <p>简单的配置和管理界面，让部署变得轻松简单</p>
            </div>
        </div>

        <div class="version">
            Apache Tomcat 版本信息
        </div>

        <div class="footer">
            <p>感谢您选择 Apache Tomcat！</p>
            <p>如需帮助，请访问 <strong>http://tomcat.apache.org</strong></p>
            <p style="margin-top: 15px; font-size: 0.8em;">
                © 2024 Apache Software Foundation. All rights reserved.
            </p>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为特性卡片添加点击效果
            const features = document.querySelectorAll('.feature');
            features.forEach(feature => {
                feature.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-5px)';
                    }, 150);
                });
            });

            // 显示当前时间
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');
            
            // 可以在控制台显示启动信息
            console.log('🎉 Tomcat 欢迎页面已加载');
            console.log('⏰ 当前时间：' + timeString);
        });
    </script>
</body>
</html>
