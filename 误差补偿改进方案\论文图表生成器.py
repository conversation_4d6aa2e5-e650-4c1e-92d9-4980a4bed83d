#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
论文图表生成器
生成高质量的学术论文图表，支持中文显示

作者: 张振意
日期: 2025年7月20日
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import seaborn as sns
import pandas as pd
import os

# 设置学术论文风格
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams['font.family'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 14
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['xtick.labelsize'] = 12
plt.rcParams['ytick.labelsize'] = 12
plt.rcParams['legend.fontsize'] = 12
plt.rcParams['figure.titlesize'] = 18
plt.rcParams['axes.unicode_minus'] = False

# 学术配色方案
ACADEMIC_COLORS = {
    'primary': '#2E86AB',      # 深蓝色
    'secondary': '#A23B72',    # 深紫色  
    'accent': '#F18F01',       # 橙色
    'success': '#C73E1D',      # 红色
    'warning': '#F4A261',      # 浅橙色
    'info': '#264653',         # 深绿色
    'light': '#E9C46A',        # 浅黄色
    'dark': '#2A2A2A'          # 深灰色
}

class AcademicFigureGenerator:
    """学术图表生成器"""
    
    def __init__(self):
        self.output_dir = "输出结果/论文图表"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def generate_loss_landscape_comparison(self):
        """生成损失函数地形图对比 - 图1"""
        print("📊 生成图1: 损失函数地形图对比...")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # 创建参数空间
        w1 = np.linspace(-3, 3, 100)
        w2 = np.linspace(-3, 3, 100)
        W1, W2 = np.meshgrid(w1, w2)
        
        # 传统损失函数（多个局部最优）
        traditional_loss = (W1**2 + W2**2) + 0.5 * np.sin(5*W1) * np.cos(5*W2) + \
                          0.3 * np.sin(3*W1) + 0.2 * np.cos(4*W2)
        
        # PINN损失函数（平滑化）
        physics_constraint = 0.1 * (W1**2 + W2**2)
        pinn_loss = (W1**2 + W2**2) + physics_constraint + \
                   0.1 * np.sin(2*W1) * np.cos(2*W2)
        
        # 绘制传统损失函数
        contour1 = ax1.contour(W1, W2, traditional_loss, levels=15, colors='black', alpha=0.4, linewidths=0.8)
        im1 = ax1.contourf(W1, W2, traditional_loss, levels=50, cmap='viridis', alpha=0.8)
        
        # 标记局部最优点
        local_minima = [(-1.5, 1.2), (1.8, -0.8), (-0.5, -1.5)]
        for i, (x, y) in enumerate(local_minima):
            ax1.plot(x, y, 'ro', markersize=8, markeredgecolor='white', markeredgewidth=1)
            if i == 0:
                ax1.plot([], [], 'ro', markersize=8, label='局部最优点')
        
        # 全局最优
        ax1.plot(0, 0, '*', color='gold', markersize=15, markeredgecolor='black', 
                markeredgewidth=1, label='全局最优点')
        
        ax1.set_title('(a) 传统损失函数地形图', fontsize=14, fontweight='bold', pad=15)
        ax1.set_xlabel('参数 $w_1$', fontsize=12)
        ax1.set_ylabel('参数 $w_2$', fontsize=12)
        ax1.legend(loc='upper right', framealpha=0.9)
        ax1.grid(True, alpha=0.3)
        
        # 绘制PINN损失函数
        contour2 = ax2.contour(W1, W2, pinn_loss, levels=15, colors='black', alpha=0.4, linewidths=0.8)
        im2 = ax2.contourf(W1, W2, pinn_loss, levels=50, cmap='plasma', alpha=0.8)
        
        # 全局最优
        ax2.plot(0, 0, '*', color='gold', markersize=15, markeredgecolor='black', 
                markeredgewidth=1, label='全局最优点')
        
        ax2.set_title('(b) PINN损失函数地形图', fontsize=14, fontweight='bold', pad=15)
        ax2.set_xlabel('参数 $w_1$', fontsize=12)
        ax2.set_ylabel('参数 $w_2$', fontsize=12)
        ax2.legend(loc='upper right', framealpha=0.9)
        ax2.grid(True, alpha=0.3)
        
        # 添加颜色条
        cbar1 = plt.colorbar(im1, ax=ax1, shrink=0.8)
        cbar1.set_label('损失值', fontsize=11)
        cbar2 = plt.colorbar(im2, ax=ax2, shrink=0.8)
        cbar2.set_label('损失值', fontsize=11)
        
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(f'{self.output_dir}/图1_损失函数地形图对比.png', dpi=300, bbox_inches='tight')
        plt.savefig(f'{self.output_dir}/图1_损失函数地形图对比.pdf', dpi=300, bbox_inches='tight')
        plt.savefig(f'{self.output_dir}/图1_损失函数地形图对比.eps', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_physics_constraints_effects(self):
        """生成物理约束效果图 - 图2"""
        print("📊 生成图2: 物理约束效果...")
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 运动学约束效果
        theta = np.linspace(0, 2*np.pi, 100)
        unconstrained_x = 2 * np.cos(theta) + 0.3 * np.random.randn(100)
        unconstrained_y = 2 * np.sin(theta) + 0.3 * np.random.randn(100)
        constrained_x = 2 * np.cos(theta)
        constrained_y = 2 * np.sin(theta)
        
        ax1.scatter(unconstrained_x, unconstrained_y, alpha=0.6, c=ACADEMIC_COLORS['accent'], 
                   label='无物理约束预测', s=25, edgecolors='white', linewidths=0.5)
        ax1.plot(constrained_x, constrained_y, color=ACADEMIC_COLORS['primary'], linewidth=3, 
                label='物理约束预测')
        ax1.set_title('(a) 运动学约束效果', fontsize=14, fontweight='bold')
        ax1.set_xlabel('X 位置 (mm)', fontsize=12)
        ax1.set_ylabel('Y 位置 (mm)', fontsize=12)
        ax1.legend(framealpha=0.9)
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        
        # 2. 能量守恒约束
        t = np.linspace(0, 10, 100)
        energy_violation = 10 + 2*t + 0.3*np.random.randn(100)
        energy_conservation = 10 + 0.1*np.sin(t) + 0.05*np.random.randn(100)
        
        ax2.plot(t, energy_violation, '--', color=ACADEMIC_COLORS['accent'], linewidth=2, 
                alpha=0.8, label='违反能量守恒')
        ax2.plot(t, energy_conservation, '-', color=ACADEMIC_COLORS['primary'], linewidth=2, 
                label='符合能量守恒')
        ax2.set_title('(b) 能量守恒约束', fontsize=14, fontweight='bold')
        ax2.set_xlabel('时间 (s)', fontsize=12)
        ax2.set_ylabel('系统总能量 (J)', fontsize=12)
        ax2.legend(framealpha=0.9)
        ax2.grid(True, alpha=0.3)
        
        # 3. 关节限制约束
        joint_angles = np.linspace(-180, 180, 1000)
        unconstrained_torque = 50 * np.sin(np.deg2rad(joint_angles * 2))
        joint_limits = [-150, 150]
        constrained_torque = unconstrained_torque.copy()
        constrained_torque[joint_angles < joint_limits[0]] *= 0.1
        constrained_torque[joint_angles > joint_limits[1]] *= 0.1
        
        ax3.plot(joint_angles, unconstrained_torque, '--', color=ACADEMIC_COLORS['accent'], 
                alpha=0.8, linewidth=2, label='无关节限制')
        ax3.plot(joint_angles, constrained_torque, '-', color=ACADEMIC_COLORS['primary'], 
                linewidth=2, label='考虑关节限制')
        ax3.axvline(joint_limits[0], color='gray', linestyle=':', alpha=0.8, linewidth=2)
        ax3.axvline(joint_limits[1], color='gray', linestyle=':', alpha=0.8, linewidth=2, 
                   label='关节限制')
        ax3.set_title('(c) 关节限制约束', fontsize=14, fontweight='bold')
        ax3.set_xlabel('关节角度 (度)', fontsize=12)
        ax3.set_ylabel('关节力矩 (Nm)', fontsize=12)
        ax3.legend(framealpha=0.9)
        ax3.grid(True, alpha=0.3)
        
        # 4. 物理约束权重影响
        epochs = np.arange(1, 101)
        lambda_values = [0.0, 0.01, 0.1, 0.5]
        colors = [ACADEMIC_COLORS['accent'], ACADEMIC_COLORS['warning'], 
                 ACADEMIC_COLORS['primary'], ACADEMIC_COLORS['secondary']]
        
        for i, (lam, color) in enumerate(zip(lambda_values, colors)):
            base_loss = 1.0 * np.exp(-epochs/30)
            noise = 0.05 * np.random.randn(100) * np.exp(-epochs/50)
            
            if lam == 0.0:
                loss = base_loss + 0.15 * np.sin(epochs/5) + noise
            else:
                loss = base_loss * (1 + lam*0.1) + noise * (1 - lam*0.5)
            
            ax4.plot(epochs, loss, linewidth=2, label=f'λ = {lam}', color=color)
        
        ax4.set_title('(d) 物理约束权重对收敛的影响', fontsize=14, fontweight='bold')
        ax4.set_xlabel('训练轮数', fontsize=12)
        ax4.set_ylabel('损失值', fontsize=12)
        ax4.set_yscale('log')
        ax4.legend(framealpha=0.9)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(f'{self.output_dir}/图2_物理约束效果.png', dpi=300, bbox_inches='tight')
        plt.savefig(f'{self.output_dir}/图2_物理约束效果.pdf', dpi=300, bbox_inches='tight')
        plt.savefig(f'{self.output_dir}/图2_物理约束效果.eps', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_multi_objective_optimization(self):
        """生成多目标优化图 - 图3"""
        print("📊 生成图3: 多目标优化...")
        
        fig = plt.figure(figsize=(18, 6))
        
        # 生成模拟数据
        np.random.seed(42)
        n_solutions = 100
        
        f1 = np.random.exponential(0.1, n_solutions)
        f2 = 0.2 - 0.5 * f1 + 0.05 * np.random.randn(n_solutions)
        f2 = np.maximum(f2, 0.01)
        f3 = 100 + 50 * np.random.randn(n_solutions)
        f3 = np.maximum(f3, 10)
        
        # 1. 2D Pareto前沿
        ax1 = fig.add_subplot(131)
        
        # 计算Pareto前沿
        pareto_indices = self.find_pareto_front(np.column_stack([f1, f2]))
        
        ax1.scatter(f1, f2, alpha=0.6, c=ACADEMIC_COLORS['info'], s=40, 
                   label='所有解', edgecolors='white', linewidths=0.5)
        ax1.scatter(f1[pareto_indices], f2[pareto_indices], 
                   c=ACADEMIC_COLORS['accent'], s=80, label='Pareto最优解', 
                   edgecolors='black', linewidths=1)
        
        # 连接Pareto前沿
        pareto_f1 = f1[pareto_indices]
        pareto_f2 = f2[pareto_indices]
        sorted_indices = np.argsort(pareto_f1)
        ax1.plot(pareto_f1[sorted_indices], pareto_f2[sorted_indices], 
                color=ACADEMIC_COLORS['primary'], linestyle='--', alpha=0.8, linewidth=2)
        
        ax1.set_xlabel('位置误差 (mm)', fontsize=12)
        ax1.set_ylabel('角度误差 (度)', fontsize=12)
        ax1.set_title('(a) 二维Pareto前沿', fontsize=14, fontweight='bold')
        ax1.legend(framealpha=0.9)
        ax1.grid(True, alpha=0.3)
        
        # 2. 3D目标空间
        ax2 = fig.add_subplot(132, projection='3d')
        
        ax2.scatter(f1, f2, f3, alpha=0.6, c=ACADEMIC_COLORS['info'], s=30)
        ax2.scatter(f1[pareto_indices], f2[pareto_indices], f3[pareto_indices],
                   c=ACADEMIC_COLORS['accent'], s=80, edgecolors='black')
        
        ax2.set_xlabel('位置误差 (mm)', fontsize=11)
        ax2.set_ylabel('角度误差 (度)', fontsize=11)
        ax2.set_zlabel('模型复杂度', fontsize=11)
        ax2.set_title('(b) 三维目标空间', fontsize=14, fontweight='bold')
        
        # 3. 收敛历史
        ax3 = fig.add_subplot(133)
        
        generations = np.arange(1, 21)
        best_f1 = []
        best_f2 = []
        
        for gen in generations:
            improvement_factor = 1 - 0.8 * (gen - 1) / 20
            current_f1 = np.min(f1) * improvement_factor
            current_f2 = np.min(f2) * improvement_factor
            best_f1.append(current_f1)
            best_f2.append(current_f2)
        
        ax3.plot(generations, best_f1, '-o', color=ACADEMIC_COLORS['primary'], 
                linewidth=2, markersize=6, label='最佳位置误差')
        ax3.plot(generations, best_f2, '-s', color=ACADEMIC_COLORS['secondary'], 
                linewidth=2, markersize=6, label='最佳角度误差')
        
        ax3.set_xlabel('进化代数', fontsize=12)
        ax3.set_ylabel('误差值', fontsize=12)
        ax3.set_title('(c) NSGA-II收敛历史', fontsize=14, fontweight='bold')
        ax3.legend(framealpha=0.9)
        ax3.grid(True, alpha=0.3)
        ax3.set_yscale('log')
        
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(f'{self.output_dir}/图3_多目标优化.png', dpi=300, bbox_inches='tight')
        plt.savefig(f'{self.output_dir}/图3_多目标优化.pdf', dpi=300, bbox_inches='tight')
        plt.savefig(f'{self.output_dir}/图3_多目标优化.eps', dpi=300, bbox_inches='tight')
        plt.show()
        
    def find_pareto_front(self, objectives):
        """找到Pareto前沿"""
        n_points = objectives.shape[0]
        pareto_indices = []
        
        for i in range(n_points):
            is_pareto = True
            for j in range(n_points):
                if i != j:
                    if all(objectives[j] <= objectives[i]) and any(objectives[j] < objectives[i]):
                        is_pareto = False
                        break
            if is_pareto:
                pareto_indices.append(i)
        
        return pareto_indices
    
    def generate_attention_mechanism(self):
        """生成注意力机制图 - 图4"""
        print("📊 生成图4: 注意力机制...")
        
        # 模拟关节耦合数据
        joint_names = ['关节1', '关节2', '关节3', '关节4', '关节5', '关节6']
        
        # 理论耦合矩阵
        theoretical_coupling = np.array([
            [1.0, 0.8, 0.3, 0.2, 0.1, 0.1],
            [0.8, 1.0, 0.9, 0.4, 0.2, 0.1],
            [0.3, 0.9, 1.0, 0.7, 0.3, 0.2],
            [0.2, 0.4, 0.7, 1.0, 0.8, 0.6],
            [0.1, 0.2, 0.3, 0.8, 1.0, 0.9],
            [0.1, 0.1, 0.2, 0.6, 0.9, 1.0]
        ])
        
        # 学习到的注意力权重
        np.random.seed(42)
        learned_attention = theoretical_coupling + 0.1 * np.random.randn(6, 6)
        learned_attention = np.maximum(learned_attention, 0)
        learned_attention = learned_attention / learned_attention.sum(axis=1, keepdims=True)
        
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 5))
        
        # 1. 理论耦合矩阵
        im1 = ax1.imshow(theoretical_coupling, cmap='Blues', aspect='auto', vmin=0, vmax=1)
        ax1.set_title('(a) 理论关节耦合矩阵', fontsize=14, fontweight='bold')
        ax1.set_xticks(range(6))
        ax1.set_yticks(range(6))
        ax1.set_xticklabels(joint_names, rotation=45)
        ax1.set_yticklabels(joint_names)
        
        # 添加数值标注
        for i in range(6):
            for j in range(6):
                ax1.text(j, i, f'{theoretical_coupling[i,j]:.2f}', 
                        ha='center', va='center', fontsize=10, 
                        color='white' if theoretical_coupling[i,j] > 0.5 else 'black')
        
        cbar1 = plt.colorbar(im1, ax=ax1, shrink=0.8)
        cbar1.set_label('耦合强度', fontsize=11)
        
        # 2. 学习到的注意力权重
        im2 = ax2.imshow(learned_attention, cmap='Reds', aspect='auto', vmin=0, vmax=1)
        ax2.set_title('(b) 学习到的注意力权重', fontsize=14, fontweight='bold')
        ax2.set_xticks(range(6))
        ax2.set_yticks(range(6))
        ax2.set_xticklabels(joint_names, rotation=45)
        ax2.set_yticklabels(joint_names)
        
        # 添加数值标注
        for i in range(6):
            for j in range(6):
                ax2.text(j, i, f'{learned_attention[i,j]:.2f}', 
                        ha='center', va='center', fontsize=10,
                        color='white' if learned_attention[i,j] > 0.5 else 'black')
        
        cbar2 = plt.colorbar(im2, ax=ax2, shrink=0.8)
        cbar2.set_label('注意力权重', fontsize=11)
        
        # 3. 关节对耦合强度对比
        joint_pairs = []
        attention_values = []
        theoretical_values = []
        
        for i in range(6):
            for j in range(i+1, 6):
                joint_pairs.append(f'{joint_names[i]}-{joint_names[j]}')
                attention_values.append(learned_attention[i, j])
                theoretical_values.append(theoretical_coupling[i, j])
        
        x_pos = np.arange(len(joint_pairs))
        width = 0.35
        
        bars1 = ax3.bar(x_pos - width/2, theoretical_values, width, 
                       label='理论耦合', alpha=0.8, color=ACADEMIC_COLORS['primary'])
        bars2 = ax3.bar(x_pos + width/2, attention_values, width, 
                       label='学习注意力', alpha=0.8, color=ACADEMIC_COLORS['accent'])
        
        ax3.set_title('(c) 关节对耦合强度对比', fontsize=14, fontweight='bold')
        ax3.set_xlabel('关节对', fontsize=12)
        ax3.set_ylabel('耦合强度/注意力权重', fontsize=12)
        ax3.set_xticks(x_pos)
        ax3.set_xticklabels(joint_pairs, rotation=45, ha='right')
        ax3.legend(framealpha=0.9)
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(f'{self.output_dir}/图4_注意力机制.png', dpi=300, bbox_inches='tight')
        plt.savefig(f'{self.output_dir}/图4_注意力机制.pdf', dpi=300, bbox_inches='tight')
        plt.savefig(f'{self.output_dir}/图4_注意力机制.eps', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_all_figures(self):
        """生成所有论文图表"""
        print("🎨 开始生成论文图表...")
        print("=" * 50)
        
        self.generate_loss_landscape_comparison()
        self.generate_physics_constraints_effects()
        self.generate_multi_objective_optimization()
        self.generate_attention_mechanism()
        
        print("\n✅ 所有论文图表生成完成!")
        print(f"📁 图表保存位置: {self.output_dir}")
        print("\n📋 生成的图表列表:")
        print("  - 图1: 损失函数地形图对比 (PNG/PDF/EPS)")
        print("  - 图2: 物理约束效果 (PNG/PDF/EPS)")
        print("  - 图3: 多目标优化 (PNG/PDF/EPS)")
        print("  - 图4: 注意力机制 (PNG/PDF/EPS)")
        print("\n💡 使用建议:")
        print("  - PNG格式适用于Word文档插入")
        print("  - PDF格式适用于LaTeX论文")
        print("  - EPS格式适用于期刊投稿")

def main():
    """主函数"""
    generator = AcademicFigureGenerator()
    generator.generate_all_figures()

if __name__ == "__main__":
    main()
