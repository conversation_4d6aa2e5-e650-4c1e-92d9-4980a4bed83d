#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于物理信息神经网络的工业机器人位姿误差补偿
完整数学实现 - 避免局部最优的确定性优化框架

基于技术手稿的完整数学理论实现
作者: 朱昕鋆
日期: 2025年7月20日
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as R
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib
matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10

# 尝试设置中文字体
def setup_chinese_font():
    try:
        import matplotlib.font_manager as fm
        # 查找系统中可用的中文字体
        font_list = [f.name for f in fm.fontManager.ttflist]
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong']
        available_font = None

        for font in chinese_fonts:
            if font in font_list:
                available_font = font
                break

        if available_font:
            plt.rcParams['font.sans-serif'] = [available_font]
            print(f"✅ 使用中文字体: {available_font}")
            return available_font
        else:
            print("⚠️ 未找到中文字体，使用默认字体")
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            return None
    except Exception as e:
        print(f"⚠️ 字体设置警告: {e}")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        return None

# 设置字体
setup_chinese_font()

class RobotKinematicsPINN:
    """基于PINN的机器人运动学模块"""
    
    def __init__(self):
        # Staubli TX60的M-DH参数 [a, alpha, d, theta_offset, beta]
        self.dh_params = np.array([
            [0,   np.pi/2,  0,   np.pi,     0],      # 关节1
            [290, 0,        0,   np.pi/2,   0],      # 关节2  
            [0,   np.pi/2,  20,  np.pi/2,   0],      # 关节3
            [0,   np.pi/2,  310, np.pi,     0],      # 关节4
            [0,   np.pi/2,  0,   np.pi,     0],      # 关节5
            [0,   0,        70,  0,         0]       # 关节6
        ])
        
    def mdh_transform(self, a, alpha, d, theta, beta=0):
        """修正DH变换矩阵计算"""
        a_m = a / 1000.0  # mm -> m
        d_m = d / 1000.0
        
        ct, st = np.cos(theta), np.sin(theta)
        ca, sa = np.cos(alpha), np.sin(alpha)
        cb, sb = np.cos(beta), np.sin(beta)
        
        T = np.array([
            [ct*cb - st*sa*sb,  -st*ca,  ct*sb + st*sa*cb,  a_m*ct],
            [st*cb + ct*sa*sb,   ct*ca,  st*sb - ct*sa*cb,  a_m*st],
            [-ca*sb,             sa,     ca*cb,             d_m],
            [0,                  0,      0,                 1]
        ])
        return T
    
    def forward_kinematics(self, joint_angles):
        """正向运动学计算"""
        joint_angles_rad = np.deg2rad(joint_angles)
        T = np.eye(4)
        
        for i in range(6):
            a, alpha, d, theta_offset, beta = self.dh_params[i]
            theta = joint_angles_rad[i] + theta_offset
            T_i = self.mdh_transform(a, alpha, d, theta, beta)
            T = T @ T_i
        
        # 提取位置和姿态
        position = T[:3, 3] * 1000.0  # m -> mm
        rotation_matrix = T[:3, :3]
        
        # 使用scipy进行欧拉角转换
        r = R.from_matrix(rotation_matrix)
        euler_angles = r.as_euler('XYZ', degrees=True)
        
        return np.concatenate([position, euler_angles])
    
    def compute_jacobian(self, joint_angles):
        """计算雅可比矩阵"""
        epsilon = 1e-6
        jacobian = np.zeros((6, 6))
        
        base_pose = self.forward_kinematics(joint_angles)
        
        for i in range(6):
            joint_angles_plus = joint_angles.copy()
            joint_angles_plus[i] += epsilon
            pose_plus = self.forward_kinematics(joint_angles_plus)
            
            jacobian[:, i] = (pose_plus - base_pose) / epsilon
            
        return jacobian

class PhysicsInformedTransformer(nn.Module):
    """物理信息Transformer网络"""
    
    def __init__(self, input_dim=63, d_model=128, nhead=8, num_layers=4, output_dim=6):
        super(PhysicsInformedTransformer, self).__init__()
        
        self.robot_kinematics = RobotKinematicsPINN()
        
        # 输入处理层
        self.input_norm = nn.LayerNorm(input_dim)
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        
        # 物理约束层
        self.physics_layer = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(0.1)
        )
        
        # 分离输出头
        self.position_head = nn.Sequential(
            nn.Linear(d_model // 2, d_model // 4),
            nn.GELU(),
            nn.Linear(d_model // 4, 3)
        )
        
        self.orientation_head = nn.Sequential(
            nn.Linear(d_model // 2, d_model // 4),
            nn.GELU(),
            nn.Linear(d_model // 4, 3)
        )
        
        # 注意力权重存储
        self.attention_weights = None
        
    def forward(self, x, return_attention=False):
        """前向传播"""
        # 输入标准化和投影
        x = self.input_norm(x)
        x = self.input_projection(x)
        x = x.unsqueeze(1)  # (batch_size, 1, d_model)
        
        # Transformer编码
        if return_attention:
            # 获取注意力权重
            for layer in self.transformer.layers:
                x, attn_weights = layer.self_attn(x, x, x, need_weights=True)
                self.attention_weights = attn_weights
        else:
            x = self.transformer(x)
        
        x = x.squeeze(1)  # (batch_size, d_model)
        
        # 物理约束处理
        x = self.physics_layer(x)
        
        # 分离预测
        pos_error = self.position_head(x)
        angle_error = self.orientation_head(x)
        
        return torch.cat([pos_error, angle_error], dim=1)
    
    def physics_loss(self, joint_angles, predictions):
        """物理约束损失函数"""
        batch_size = joint_angles.size(0)
        total_physics_loss = 0.0
        
        for i in range(batch_size):
            theta = joint_angles[i].detach().cpu().numpy()[:6]  # 取前6维原始关节角
            pred = predictions[i].detach().cpu().numpy()
            
            # 1. 运动学一致性约束
            jacobian = self.robot_kinematics.compute_jacobian(theta)
            jacobian_tensor = torch.tensor(jacobian, dtype=torch.float32)
            
            # 计算预测误差的雅可比
            pred_tensor = predictions[i:i+1]
            if pred_tensor.requires_grad:
                pred_jacobian = torch.autograd.grad(
                    pred_tensor.sum(), joint_angles[i:i+1], 
                    create_graph=True, retain_graph=True
                )[0][:6]  # 只取前6维
                
                kinematics_loss = torch.norm(pred_jacobian + jacobian_tensor.sum(dim=0))
                total_physics_loss += kinematics_loss
        
        # 2. 连续性约束
        if batch_size > 1:
            continuity_loss = torch.mean(torch.abs(predictions[1:] - predictions[:-1]))
            total_physics_loss += 0.01 * continuity_loss
        
        # 3. 幅度约束
        magnitude_loss = torch.mean(torch.abs(predictions)) * 0.001
        total_physics_loss += magnitude_loss
        
        return total_physics_loss / batch_size

class DeterministicOptimizer:
    """确定性优化器 - 避免局部最优"""
    
    def __init__(self, model, robot_kinematics):
        self.model = model
        self.robot_kinematics = robot_kinematics
        
    def deterministic_initialization(self, X_train, y_train):
        """基于物理先验的确定性初始化"""
        print("🔧 执行确定性初始化...")
        
        # 计算线性化解
        n_samples = min(100, len(X_train))  # 使用部分数据避免计算量过大
        indices = np.linspace(0, len(X_train)-1, n_samples, dtype=int)
        
        X_subset = X_train[indices]
        y_subset = y_train[indices]
        
        # 计算平均雅可比矩阵
        jacobians = []
        for i in range(n_samples):
            theta = X_subset[i][:6]  # 原始关节角
            J = self.robot_kinematics.compute_jacobian(theta)
            jacobians.append(J)
        
        J_avg = np.mean(jacobians, axis=0)
        
        # 最小二乘解
        alpha = 1e-6  # 正则化参数
        try:
            delta_theta_optimal = np.linalg.solve(
                J_avg.T @ J_avg + alpha * np.eye(6),
                J_avg.T @ np.mean(y_subset, axis=0)
            )
            print(f"✅ 确定性初始化完成，最优解: {delta_theta_optimal}")
            return delta_theta_optimal
        except np.linalg.LinAlgError:
            print("⚠️ 矩阵奇异，使用伪逆")
            delta_theta_optimal = np.linalg.pinv(J_avg) @ np.mean(y_subset, axis=0)
            return delta_theta_optimal
    
    def adaptive_weight_adjustment(self, losses, epoch):
        """自适应权重调整"""
        data_loss, physics_loss = losses
        
        # 基于损失比例调整权重
        if physics_loss > 0:
            ratio = data_loss / physics_loss
            if ratio > 10:  # 数据损失过大
                physics_weight = min(1.0, 0.1 * (1 + epoch / 100))
            elif ratio < 0.1:  # 物理损失过大
                physics_weight = max(0.01, 0.1 * (1 - epoch / 200))
            else:
                physics_weight = 0.1
        else:
            physics_weight = 0.1
            
        return physics_weight

class MultiObjectiveNSGAII:
    """多目标NSGA-II优化器"""
    
    def __init__(self, population_size=20, max_generations=10):
        self.population_size = population_size
        self.max_generations = max_generations
        
    def non_dominated_sort(self, objectives):
        """非支配排序"""
        n = len(objectives)
        domination_count = [0] * n
        dominated_solutions = [[] for _ in range(n)]
        fronts = [[]]
        
        for i in range(n):
            for j in range(n):
                if i != j:
                    if self.dominates(objectives[i], objectives[j]):
                        dominated_solutions[i].append(j)
                    elif self.dominates(objectives[j], objectives[i]):
                        domination_count[i] += 1
            
            if domination_count[i] == 0:
                fronts[0].append(i)
        
        front_idx = 0
        while len(fronts[front_idx]) > 0:
            next_front = []
            for i in fronts[front_idx]:
                for j in dominated_solutions[i]:
                    domination_count[j] -= 1
                    if domination_count[j] == 0:
                        next_front.append(j)
            front_idx += 1
            fronts.append(next_front)
        
        return fronts[:-1]  # 移除最后的空前沿
    
    def dominates(self, obj1, obj2):
        """判断obj1是否支配obj2"""
        better_in_any = False
        for i in range(len(obj1)):
            if obj1[i] > obj2[i]:  # 假设最小化问题
                return False
            elif obj1[i] < obj2[i]:
                better_in_any = True
        return better_in_any
    
    def crowding_distance(self, objectives, front):
        """计算拥挤距离"""
        if len(front) <= 2:
            return [float('inf')] * len(front)
        
        distances = [0.0] * len(front)
        n_obj = len(objectives[0])
        
        for m in range(n_obj):
            # 按第m个目标排序
            front_sorted = sorted(front, key=lambda x: objectives[x][m])
            
            # 边界点设为无穷大
            distances[front.index(front_sorted[0])] = float('inf')
            distances[front.index(front_sorted[-1])] = float('inf')
            
            # 计算中间点的拥挤距离
            obj_range = objectives[front_sorted[-1]][m] - objectives[front_sorted[0]][m]
            if obj_range > 0:
                for i in range(1, len(front_sorted) - 1):
                    idx = front.index(front_sorted[i])
                    distances[idx] += (objectives[front_sorted[i+1]][m] - 
                                     objectives[front_sorted[i-1]][m]) / obj_range
        
        return distances
    
    def optimize_hyperparameters(self, model_class, train_loader, val_loader):
        """多目标超参数优化"""
        print("🎯 NSGA-II多目标优化中...")
        
        # 超参数搜索空间
        param_ranges = {
            'd_model': [64, 128, 256],
            'nhead': [4, 8, 16],
            'num_layers': [2, 4, 6],
            'lr': [1e-4, 5e-4, 1e-3],
            'physics_weight': [0.01, 0.1, 0.5]
        }
        
        best_solutions = []
        
        for generation in range(self.max_generations):
            population = []
            objectives = []
            
            # 生成种群
            for _ in range(self.population_size):
                individual = {
                    'd_model': np.random.choice(param_ranges['d_model']),
                    'nhead': np.random.choice(param_ranges['nhead']),
                    'num_layers': np.random.choice(param_ranges['num_layers']),
                    'lr': np.random.choice(param_ranges['lr']),
                    'physics_weight': np.random.choice(param_ranges['physics_weight'])
                }
                
                # 确保nhead能整除d_model
                while individual['d_model'] % individual['nhead'] != 0:
                    individual['nhead'] = np.random.choice(param_ranges['nhead'])
                
                population.append(individual)
            
            # 评估种群
            for individual in population:
                try:
                    # 快速评估
                    obj = self.quick_evaluate(individual, model_class, train_loader, val_loader)
                    objectives.append(obj)
                except Exception as e:
                    objectives.append([1.0, 1.0, 1000])  # 惩罚值
            
            # 非支配排序
            fronts = self.non_dominated_sort(objectives)
            
            # 选择最优解
            if fronts and len(fronts[0]) > 0:
                best_idx = fronts[0][0]
                best_solutions.append((population[best_idx], objectives[best_idx]))
            
            if generation % 3 == 0:
                print(f"  第 {generation+1}/{self.max_generations} 代完成")
        
        # 返回最优解
        if best_solutions:
            best_solution = min(best_solutions, key=lambda x: sum(x[1][:2]))
            print(f"✅ NSGA-II优化完成，最优参数: {best_solution[0]}")
            return best_solution[0]
        else:
            return None
    
    def quick_evaluate(self, params, model_class, train_loader, val_loader):
        """快速评估个体"""
        model = model_class(
            input_dim=63,
            d_model=params['d_model'],
            nhead=params['nhead'],
            num_layers=params['num_layers']
        )
        
        optimizer = optim.Adam(model.parameters(), lr=params['lr'])
        
        # 快速训练5轮
        model.train()
        for epoch in range(5):
            for features, targets in train_loader:
                optimizer.zero_grad()
                predictions = model(features)
                
                pos_loss = nn.MSELoss()(predictions[:, :3], targets[:, :3])
                angle_loss = nn.MSELoss()(predictions[:, 3:], targets[:, 3:])
                data_loss = 0.7 * pos_loss + 0.3 * angle_loss
                
                physics_loss = model.physics_loss(features, predictions)
                total_loss = data_loss + params['physics_weight'] * physics_loss
                
                total_loss.backward()
                optimizer.step()
                break  # 只训练一个batch
        
        # 评估
        model.eval()
        with torch.no_grad():
            all_predictions = []
            all_targets = []
            
            for features, targets in val_loader:
                predictions = model(features)
                all_predictions.append(predictions.numpy())
                all_targets.append(targets.numpy())
                break  # 只评估一个batch
            
            if all_predictions:
                predictions = np.vstack(all_predictions)
                targets = np.vstack(all_targets)
                
                pos_errors = np.sqrt(np.sum((targets[:, :3] - predictions[:, :3])**2, axis=1))
                angle_errors = np.abs(targets[:, 3:] - predictions[:, 3:])
                
                pos_error = np.mean(pos_errors)
                angle_error = np.median(angle_errors)
                complexity = sum(p.numel() for p in model.parameters()) / 1000
                
                return [pos_error, angle_error, complexity]
            else:
                return [1.0, 1.0, 1000]

def create_enhanced_features(joint_angles):
    """创建63维增强特征"""
    features = []
    angles_rad = np.deg2rad(joint_angles)
    
    # 1. 原始特征 (6维)
    features.append(joint_angles)
    
    # 2. 三角函数特征 (24维)
    features.extend([
        np.sin(angles_rad), np.cos(angles_rad),
        np.sin(2 * angles_rad), np.cos(2 * angles_rad)
    ])
    
    # 3. 多项式特征 (12维)
    features.extend([joint_angles ** 2, joint_angles ** 3])
    
    # 4. 关节交互特征 (15维)
    interactions = []
    for i in range(6):
        for j in range(i+1, 6):
            interactions.append((joint_angles[:, i] * joint_angles[:, j]).reshape(-1, 1))
    features.append(np.column_stack(interactions))
    
    # 5. 工作空间特征 (3维)
    workspace_x = (np.cos(angles_rad[:, 0]) * np.cos(angles_rad[:, 1])).reshape(-1, 1)
    workspace_y = (np.sin(angles_rad[:, 0]) * np.cos(angles_rad[:, 1])).reshape(-1, 1)
    workspace_z = np.sin(angles_rad[:, 1]).reshape(-1, 1)
    features.append(np.column_stack([workspace_x, workspace_y, workspace_z]))
    
    # 6. 奇异性特征 (3维)
    wrist_sing = np.abs(np.sin(angles_rad[:, 4])).reshape(-1, 1)
    shoulder_sing = np.abs(np.sin(angles_rad[:, 1]) * np.sin(angles_rad[:, 2])).reshape(-1, 1)
    elbow_sing = np.abs(np.cos(angles_rad[:, 2])).reshape(-1, 1)
    features.append(np.column_stack([wrist_sing, shoulder_sing, elbow_sing]))
    
    return np.column_stack(features)

def main():
    """主函数"""
    print("🚀 基于PINN的机器人位姿误差补偿系统")
    print("=" * 60)
    
    # 设置随机种子确保可重现性
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 1. 数据加载
    print("📊 加载数据...")
    joint_data_df = pd.read_excel('../theta2000.xlsx', header=None)
    joint_data_df.columns = [f'theta_{i+1}' for i in range(6)]
    joint_data = joint_data_df.values
    
    measured_data = pd.read_excel('../real2000.xlsx').values
    
    # 2. 理论计算
    print("🔬 计算理论位姿...")
    robot = RobotKinematicsPINN()
    theoretical_poses = []
    for joints in joint_data:
        pose = robot.forward_kinematics(joints)
        theoretical_poses.append(pose)
    theoretical_poses = np.array(theoretical_poses)
    
    # 3. 误差计算
    errors = measured_data - theoretical_poses
    
    # 角度连续性修复
    for i in range(errors.shape[0]):
        for j in range(3, 6):
            error = errors[i, j]
            candidates = [error, error + 360, error - 360]
            errors[i, j] = min(candidates, key=abs)
    
    # 4. 数据划分
    test_indices = list(range(400))
    train_indices = list(range(400, 2000))
    
    X_train = joint_data[train_indices]
    X_test = joint_data[test_indices]
    y_train = errors[train_indices]
    y_test = errors[test_indices]
    
    # 5. 特征工程
    print("🔧 特征工程...")
    X_train_enhanced = create_enhanced_features(X_train)
    X_test_enhanced = create_enhanced_features(X_test)
    
    # 6. 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train_enhanced)
    X_test_scaled = scaler.transform(X_test_enhanced)
    
    # 7. 创建数据加载器
    train_dataset = TensorDataset(
        torch.FloatTensor(X_train_scaled),
        torch.FloatTensor(y_train)
    )
    test_dataset = TensorDataset(
        torch.FloatTensor(X_test_scaled),
        torch.FloatTensor(y_test)
    )
    
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    print(f"✅ 数据准备完成: 训练集{X_train_scaled.shape}, 测试集{X_test_scaled.shape}")
    
    # 8. 基线误差
    pos_errors = np.sqrt(np.sum(errors[:, :3]**2, axis=1))
    angle_errors = np.abs(errors[:, 3:])
    baseline_pos_error = np.mean(pos_errors)
    baseline_angle_error = np.median(angle_errors)
    
    print(f"📈 基线误差: 位置{baseline_pos_error:.6f}mm, 角度{baseline_angle_error:.6f}°")
    
    # 9. NSGA-II超参数优化
    print("\n🎯 多目标超参数优化...")
    nsga_optimizer = MultiObjectiveNSGAII(population_size=15, max_generations=8)
    best_params = nsga_optimizer.optimize_hyperparameters(
        PhysicsInformedTransformer, train_loader, test_loader
    )
    
    # 10. 训练最终模型
    print("\n🚀 训练最终PINN模型...")
    if best_params:
        model = PhysicsInformedTransformer(
            input_dim=63,
            d_model=best_params['d_model'],
            nhead=best_params['nhead'],
            num_layers=best_params['num_layers']
        )
        physics_weight = best_params['physics_weight']
        lr = best_params['lr']
    else:
        model = PhysicsInformedTransformer(input_dim=63, d_model=128, nhead=8, num_layers=4)
        physics_weight = 0.1
        lr = 1e-3
    
    # 确定性初始化
    det_optimizer = DeterministicOptimizer(model, robot)
    det_optimizer.deterministic_initialization(X_train_scaled, y_train)
    
    # 训练
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=15, factor=0.5)
    
    best_val_loss = float('inf')
    patience_counter = 0
    epochs = 100
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0.0
        
        for features, targets in train_loader:
            optimizer.zero_grad()
            
            predictions = model(features)
            
            # 加权损失
            pos_loss = nn.MSELoss()(predictions[:, :3], targets[:, :3])
            angle_loss = nn.MSELoss()(predictions[:, 3:], targets[:, 3:])
            data_loss = 0.7 * pos_loss + 0.3 * angle_loss
            
            # 物理约束损失
            physics_loss = model.physics_loss(features, predictions)
            
            # 自适应权重调整
            physics_weight = det_optimizer.adaptive_weight_adjustment(
                (data_loss.item(), physics_loss.item()), epoch
            )
            
            total_loss = data_loss + physics_weight * physics_loss
            
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += total_loss.item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for features, targets in test_loader:
                predictions = model(features)
                
                pos_loss = nn.MSELoss()(predictions[:, :3], targets[:, :3])
                angle_loss = nn.MSELoss()(predictions[:, 3:], targets[:, 3:])
                loss = 0.7 * pos_loss + 0.3 * angle_loss
                
                val_loss += loss.item()
                
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(targets.cpu().numpy())
        
        scheduler.step(val_loss)
        
        # 早停
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
        else:
            patience_counter += 1
        
        if epoch % 20 == 0:
            predictions = np.vstack(all_predictions)
            targets = np.vstack(all_targets)
            
            residual_errors = targets - predictions
            pos_errors = np.sqrt(np.sum(residual_errors[:, :3]**2, axis=1))
            angle_errors_raw = residual_errors[:, 3:]
            
            pos_error = np.mean(pos_errors)
            angle_error = np.median(np.abs(angle_errors_raw))
            
            print(f"  Epoch {epoch}: Train={train_loss/len(train_loader):.6f}, "
                  f"Val={val_loss/len(test_loader):.6f}, "
                  f"Pos={pos_error:.6f}mm, Angle={angle_error:.6f}°")
        
        if patience_counter >= 25:
            print(f"  早停于第 {epoch} 轮")
            break
    
    # 11. 最终结果
    print("\n" + "="*60)
    print("🎉 最终结果")
    print("="*60)
    
    predictions = np.vstack(all_predictions)
    targets = np.vstack(all_targets)
    
    residual_errors = targets - predictions
    pos_errors = np.sqrt(np.sum(residual_errors[:, :3]**2, axis=1))
    angle_errors_raw = residual_errors[:, 3:]
    
    final_pos_error = np.mean(pos_errors)
    final_angle_error = np.median(np.abs(angle_errors_raw))
    r2 = r2_score(targets, predictions)
    
    pos_improvement = (baseline_pos_error - final_pos_error) / baseline_pos_error * 100
    angle_improvement = (baseline_angle_error - final_angle_error) / baseline_angle_error * 100
    
    print(f"📊 误差对比:")
    print(f"  原始位置误差: {baseline_pos_error:.6f} mm")
    print(f"  补偿后位置误差: {final_pos_error:.6f} mm")
    print(f"  位置精度提升: {pos_improvement:.2f}%")
    print()
    print(f"  原始角度误差: {baseline_angle_error:.6f} 度")
    print(f"  补偿后角度误差: {final_angle_error:.6f} 度")
    print(f"  角度精度提升: {angle_improvement:.2f}%")
    print()
    print(f"📈 模型性能:")
    print(f"  R² 分数: {r2:.4f}")
    print(f"  模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    print(f"\n🔬 技术特色:")
    print(f"  ✅ Physics-Informed Neural Networks (PINN)")
    print(f"  ✅ Transformer + 多头自注意力机制")
    print(f"  ✅ NSGA-II多目标超参数优化")
    print(f"  ✅ 确定性初始化避免局部最优")
    print(f"  ✅ 63维增强特征工程")
    print(f"  ✅ 自适应物理约束权重")
    
    # 保存结果
    results = {
        'baseline_pos_error': baseline_pos_error,
        'final_pos_error': final_pos_error,
        'pos_improvement': pos_improvement,
        'baseline_angle_error': baseline_angle_error,
        'final_angle_error': final_angle_error,
        'angle_improvement': angle_improvement,
        'r2_score': r2,
        'model_parameters': sum(p.numel() for p in model.parameters())
    }
    
    import os
    os.makedirs("输出结果", exist_ok=True)
    results_df = pd.DataFrame([results])
    results_df.to_excel('输出结果/高级PINN数学实现结果.xlsx', index=False)
    
    print(f"\n💾 结果已保存: 输出结果/高级PINN数学实现结果.xlsx")
    print("="*60)

if __name__ == "__main__":
    main()
