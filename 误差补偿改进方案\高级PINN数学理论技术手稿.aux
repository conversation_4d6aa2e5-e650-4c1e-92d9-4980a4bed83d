\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\@writefile{toc}{\contentsline {section}{\numberline {1}数学理论基础}{1}{section.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.1}机器人运动学数学模型}{1}{subsection.1.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.2}位置-角度误差耦合数学分析}{1}{subsection.1.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.3}局部最优问题的数学表征}{2}{subsection.1.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {2}物理信息神经网络架构设计}{2}{section.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1}PINN基本框架}{2}{subsection.2.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces 损失函数地形图对比。(a)传统损失函数存在多个局部最优点（红色圆点），优化容易陷入局部解；(b)PINN通过物理约束平滑化损失函数，有效避免局部最优陷阱，更容易收敛到全局最优点（金色星号）。}}{3}{figure.1}\protected@file@percent }
\newlabel{fig:loss_landscape}{{1}{3}{损失函数地形图对比。(a)传统损失函数存在多个局部最优点（红色圆点），优化容易陷入局部解；(b)PINN通过物理约束平滑化损失函数，有效避免局部最优陷阱，更容易收敛到全局最优点（金色星号）。}{figure.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2}数据拟合损失设计}{3}{subsection.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.3}物理约束损失函数}{3}{subsection.2.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Transformer注意力机制学习关节耦合关系。(a)基于运动学理论的关节耦合矩阵，颜色深度和数值标注表示耦合强度；(b)神经网络学习到的注意力权重矩阵，与理论矩阵高度一致；(c)关节对耦合强度对比，蓝色柱为理论耦合，橙色柱为学习注意力，验证了注意力机制能够有效学习物理系统的内在结构。}}{4}{figure.2}\protected@file@percent }
\newlabel{fig:attention_mechanism}{{2}{4}{Transformer注意力机制学习关节耦合关系。(a)基于运动学理论的关节耦合矩阵，颜色深度和数值标注表示耦合强度；(b)神经网络学习到的注意力权重矩阵，与理论矩阵高度一致；(c)关节对耦合强度对比，蓝色柱为理论耦合，橙色柱为学习注意力，验证了注意力机制能够有效学习物理系统的内在结构。}{figure.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {2.3.1}运动学约束}{4}{subsubsection.2.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {2.3.2}动力学约束}{5}{subsubsection.2.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {2.3.3}几何约束}{5}{subsubsection.2.3.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3}确定性优化策略}{6}{section.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.1}替代随机初始化的确定性方法}{6}{subsection.3.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces 物理约束对神经网络预测的影响。(a)运动学约束确保预测符合机器人运动规律，橙色散点为无约束预测，蓝色实线为有约束预测；(b)能量守恒约束维持系统物理一致性，橙色虚线违反守恒定律，蓝色实线符合守恒；(c)关节限制约束避免超出物理限制的预测，灰色虚线为关节限制边界；(d)适当的物理约束权重λ显著改善收敛稳定性。}}{7}{figure.3}\protected@file@percent }
\newlabel{fig:physics_constraints}{{3}{7}{物理约束对神经网络预测的影响。(a)运动学约束确保预测符合机器人运动规律，橙色散点为无约束预测，蓝色实线为有约束预测；(b)能量守恒约束维持系统物理一致性，橙色虚线违反守恒定律，蓝色实线符合守恒；(c)关节限制约束避免超出物理限制的预测，灰色虚线为关节限制边界；(d)适当的物理约束权重λ显著改善收敛稳定性。}{figure.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.2}自适应权重调整机制}{8}{subsection.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {4}多目标优化算法设计}{8}{section.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1}改进的NSGA-II算法}{8}{subsection.4.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.1.1}多目标函数定义}{8}{subsubsection.4.1.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.1.2}非支配关系定义}{8}{subsubsection.4.1.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.1.3}拥挤距离计算}{8}{subsubsection.4.1.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.1.4}改进的NSGA-II完整算法}{9}{subsubsection.4.1.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.1.5}关键算法组件的数学实现}{9}{subsubsection.4.1.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2}Pareto最优解选择策略}{9}{subsection.4.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.2.1}加权距离法}{9}{subsubsection.4.2.1}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces 改进的NSGA-II for PINN多目标优化}}{10}{algorithm.1}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {2}{\ignorespaces FastNonDominatedSort算法}}{11}{algorithm.2}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {3}{\ignorespaces CrowdingDistanceAssignment算法}}{12}{algorithm.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.2.2}TOPSIS方法}{12}{subsubsection.4.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.2.3}膝点检测方法}{13}{subsubsection.4.2.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.2.4}收敛性分析}{13}{subsubsection.4.2.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.2.5}算法复杂度分析}{14}{subsubsection.4.2.5}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces NSGA-II多目标优化结果。(a)二维Pareto前沿展示位置精度与角度精度的权衡关系，深绿色点为所有候选解，橙色点为Pareto最优解，蓝色虚线连接前沿轨迹；(b)三维目标空间显示精度与复杂度的平衡关系；(c)收敛历史展示最佳位置误差（蓝线）和角度误差（紫线）随进化代数的变化，验证了算法的有效性。}}{15}{figure.4}\protected@file@percent }
\newlabel{fig:multi_objective}{{4}{15}{NSGA-II多目标优化结果。(a)二维Pareto前沿展示位置精度与角度精度的权衡关系，深绿色点为所有候选解，橙色点为Pareto最优解，蓝色虚线连接前沿轨迹；(b)三维目标空间显示精度与复杂度的平衡关系；(c)收敛历史展示最佳位置误差（蓝线）和角度误差（紫线）随进化代数的变化，验证了算法的有效性。}{figure.4}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5}实验设计与数学验证}{15}{section.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.1}实验平台与数据}{15}{subsection.5.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.2}基于物理原理的特征工程数学设计}{16}{subsection.5.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.2.1}理论基础与动机}{16}{subsubsection.5.2.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.2.2}误差传播的数学建模}{16}{subsubsection.5.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.2.3}李群理论在误差建模中的应用}{16}{subsubsection.5.2.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.2.4}物理驱动特征构造}{17}{subsubsection.5.2.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.2.5}特征重要性的理论分析}{18}{subsubsection.5.2.5}\protected@file@percent }
\citation{qiao2019svr}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.2.6}维度优化的数学依据}{19}{subsubsection.5.2.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.2.7}物理约束与特征的一致性}{19}{subsubsection.5.2.7}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6}结果分析与讨论}{19}{section.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.1}基线误差验证}{19}{subsection.6.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.2}特征工程有效性验证}{19}{subsection.6.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.2.1}消融实验分析}{19}{subsubsection.6.2.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {1}{\ignorespaces 不同特征组合的性能对比}}{20}{table.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.2.2}特征重要性定量分析}{20}{subsubsection.6.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.3}PINN模型性能}{20}{subsection.6.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.4}局部最优避免效果}{20}{subsection.6.4}\protected@file@percent }
\bibcite{robotics_survey_2024}{1}
\@writefile{lot}{\contentsline {table}{\numberline {2}{\ignorespaces 特征工程方法对比}}{21}{table.2}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {3}{\ignorespaces 不同初始化策略的性能对比}}{21}{table.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.5}完整PINN实验验证}{21}{subsection.6.5}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces 确定性初始化与随机初始化对比。(a)随机初始化收敛曲线显示较大的方差和不稳定性；(b)确定性初始化收敛曲线更加稳定一致；(c)最终收敛性能对比显示确定性方法的优势；(d)收敛速度分布表明确定性方法平均收敛轮数更少。}}{22}{figure.5}\protected@file@percent }
\newlabel{fig:deterministic_comparison}{{5}{22}{确定性初始化与随机初始化对比。(a)随机初始化收敛曲线显示较大的方差和不稳定性；(b)确定性初始化收敛曲线更加稳定一致；(c)最终收敛性能对比显示确定性方法的优势；(d)收敛速度分布表明确定性方法平均收敛轮数更少。}{figure.5}{}}
\bibcite{raissi2019physics}{2}
\bibcite{robotics_survey_2024}{3}
\bibcite{raissi2019physics}{4}
\bibcite{qiao2019svr}{5}
\@writefile{lot}{\contentsline {table}{\numberline {4}{\ignorespaces 完整PINN实验结果}}{23}{table.4}\protected@file@percent }
\gdef \@abspage@last{23}
