#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于理论公式的机器人误差源计算
包含减速机传动误差和连杆柔性误差的正确计算方法

作者: AI助手
日期: 2025年
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.optimize import fsolve
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

from 理论计算模块 import RobotKinematics

class TheoreticalErrorCalculation:
    """基于理论公式的误差源计算"""

    def __init__(self):
        self.robot = RobotKinematics()

        # Staubli TX60 物理参数
        self.gear_ratios = [160, 160, 160, 72, 72, 72]  # 减速比
        self.link_masses = [15.0, 8.0, 6.0, 4.0, 2.0, 1.0]  # 连杆质量 (kg)
        self.link_lengths = [0.32, 0.225, 0.035, 0.225, 0.0, 0.065]  # 连杆长度 (m)
        self.link_cog = [0.16, 0.1125, 0.0175, 0.1125, 0.0, 0.0325]  # 质心位置 (m)

        # 连杆刚度参数 (N⋅m/rad) - 基于材料和几何特性
        self.link_stiffness = [1e6, 8e5, 6e5, 2e5, 1e5, 5e4]

        # 减速机误差参数 (基于经验和文献)
        self.gear_error_amplitudes = {
            'harmonic_1': [0.01, 0.008, 0.006, 0.004, 0.003, 0.002],  # 一阶谐波幅值 (度)
            'harmonic_2': [0.005, 0.004, 0.003, 0.002, 0.0015, 0.001],  # 二阶谐波幅值 (度)
            'backlash': [0.02, 0.015, 0.01, 0.008, 0.006, 0.004]  # 间隙误差 (度)
        }

    def load_experimental_data(self):
        """加载实验数据"""
        print("=== 加载实验数据 ===")

        # 加载指令角度
        theta_df = pd.read_excel('../theta2000.xlsx', header=None)
        theta_df.columns = [f'theta_{i+1}' for i in range(6)]
        self.theta_command = theta_df.values  # 指令角度 (度)

        # 加载实测末端位姿
        self.real_poses = pd.read_excel('../real2000.xlsx').values  # 实测位姿

        # 计算理论末端位姿
        self.theoretical_poses = []
        for joints in self.theta_command:
            pose = self.robot.forward_kinematics(joints)
            self.theoretical_poses.append(pose)
        self.theoretical_poses = np.array(self.theoretical_poses)

        # 计算总误差
        self.total_errors = self.real_poses - self.theoretical_poses

        # 修复角度连续性
        for i in range(self.total_errors.shape[0]):
            for j in range(3, 6):
                error = self.total_errors[i, j]
                candidates = [error, error + 360, error - 360]
                self.total_errors[i, j] = min(candidates, key=abs)

        print(f"数据加载完成: {self.theta_command.shape[0]} 个测量点")

    def calculate_actual_joint_angles(self):
        """通过逆运动学计算实际关节角度"""
        print("=== 计算实际关节角度 ===")

        self.theta_actual = []

        for i, real_pose in enumerate(self.real_poses):
            try:
                # 使用数值逆运动学求解
                actual_angles = self.inverse_kinematics_numerical(real_pose, self.theta_command[i])
                self.theta_actual.append(actual_angles)
            except:
                # 如果逆运动学失败，使用指令角度作为近似
                self.theta_actual.append(self.theta_command[i])

            if i % 500 == 0:
                print(f"  处理进度: {i+1}/{len(self.real_poses)}")

        self.theta_actual = np.array(self.theta_actual)
        print("实际关节角度计算完成")

    def inverse_kinematics_numerical(self, target_pose, initial_guess):
        """数值逆运动学求解"""

        def equations(angles):
            """逆运动学方程组"""
            calculated_pose = self.robot.forward_kinematics(angles)
            error = calculated_pose - target_pose

            # 角度误差需要考虑周期性
            for i in range(3, 6):
                while error[i] > 180:
                    error[i] -= 360
                while error[i] < -180:
                    error[i] += 360

            return error

        # 使用fsolve求解
        solution = fsolve(equations, initial_guess, xtol=1e-6)

        # 验证解的有效性
        verification_error = equations(solution)
        if np.linalg.norm(verification_error) > 1.0:  # 误差太大
            raise ValueError("逆运动学求解失败")

        return solution

    def calculate_gear_transmission_errors(self):
        """计算减速机传动误差"""
        print("=== 计算减速机传动误差 ===")

        # 基本传动误差 = 实际角度 - 指令角度
        basic_transmission_errors = self.theta_actual - self.theta_command

        # 周期性传动误差建模
        periodic_transmission_errors = np.zeros_like(self.theta_command)

        for joint_idx in range(6):
            theta_rad = np.deg2rad(self.theta_command[:, joint_idx])

            # 一阶谐波误差 (主要误差源)
            A1 = self.gear_error_amplitudes['harmonic_1'][joint_idx]
            harmonic_1 = A1 * np.sin(theta_rad)

            # 二阶谐波误差 (齿轮啮合频率)
            A2 = self.gear_error_amplitudes['harmonic_2'][joint_idx]
            harmonic_2 = A2 * np.sin(2 * theta_rad)

            # 间隙误差 (方向相关)
            backlash_amplitude = self.gear_error_amplitudes['backlash'][joint_idx]
            # 简化的间隙模型：与角度变化方向相关
            angle_diff = np.diff(self.theta_command[:, joint_idx], prepend=self.theta_command[0, joint_idx])
            backlash_error = backlash_amplitude * np.tanh(angle_diff * 10)  # tanh函数模拟间隙特性

            # 总周期性误差
            periodic_transmission_errors[:, joint_idx] = harmonic_1 + harmonic_2 + backlash_error

        # 总传动误差
        self.gear_transmission_errors = basic_transmission_errors + periodic_transmission_errors

        # 统计分析
        gear_error_stats = {}
        for joint_idx in range(6):
            joint_errors = self.gear_transmission_errors[:, joint_idx]
            gear_error_stats[f'joint_{joint_idx+1}'] = {
                'mean_error': np.mean(np.abs(joint_errors)),
                'std_error': np.std(joint_errors),
                'max_error': np.max(np.abs(joint_errors)),
                'rms_error': np.sqrt(np.mean(joint_errors**2))
            }

            print(f"关节 {joint_idx+1} 传动误差:")
            print(f"  平均误差: {gear_error_stats[f'joint_{joint_idx+1}']['mean_error']:.4f} 度")
            print(f"  最大误差: {gear_error_stats[f'joint_{joint_idx+1}']['max_error']:.4f} 度")
            print(f"  RMS误差: {gear_error_stats[f'joint_{joint_idx+1}']['rms_error']:.4f} 度")

        self.gear_error_stats = gear_error_stats
        return self.gear_transmission_errors

    def calculate_gravity_torques(self, joint_angles):
        """计算重力矩"""

        angles_rad = np.deg2rad(joint_angles)
        gravity_torques = np.zeros(6)
        g = 9.81  # 重力加速度 (m/s²)

        # 使用递归牛顿-欧拉方法计算重力矩
        for i in range(6):
            # 计算该关节承受的重力矩
            for j in range(i, 6):  # 该关节及后续关节的重力影响

                # 计算连杆j相对于关节i的重力臂
                if i <= 2:  # 前三个关节主要受重力影响
                    # 简化的重力臂计算
                    if j == i:
                        gravity_arm = self.link_cog[j] * np.cos(angles_rad[i])
                    else:
                        # 累积效应
                        gravity_arm = self.link_lengths[j] * np.cos(sum(angles_rad[i:j+1]))
                else:  # 后三个关节重力影响较小
                    gravity_arm = self.link_cog[j] * 0.1

                # 重力矩 = 质量 × 重力加速度 × 重力臂
                gravity_torques[i] += self.link_masses[j] * g * gravity_arm

        return gravity_torques

    def calculate_jacobian(self, joint_angles):
        """计算雅可比矩阵 (简化版本)"""

        # 数值微分计算雅可比矩阵
        jacobian = np.zeros((6, 6))
        delta = 1e-6  # 微小增量

        # 当前位姿
        current_pose = self.robot.forward_kinematics(joint_angles)

        for i in range(6):
            # 对第i个关节角度加微小增量
            perturbed_angles = joint_angles.copy()
            perturbed_angles[i] += delta

            # 计算扰动后的位姿
            perturbed_pose = self.robot.forward_kinematics(perturbed_angles)

            # 计算偏导数
            jacobian[:, i] = (perturbed_pose - current_pose) / delta

        return jacobian

    def calculate_link_flexibility_errors(self):
        """计算连杆柔性误差"""
        print("=== 计算连杆柔性误差 ===")

        self.link_flexibility_errors = []

        for i, joint_angles in enumerate(self.theta_command):

            # 1. 计算重力矩
            gravity_torques = self.calculate_gravity_torques(joint_angles)

            # 2. 计算角度变形 (使用连杆刚度)
            angular_deformation = gravity_torques / self.link_stiffness

            # 3. 通过雅可比矩阵转换为末端误差
            jacobian = self.calculate_jacobian(joint_angles)
            end_effector_error = jacobian @ angular_deformation

            self.link_flexibility_errors.append(end_effector_error)

            if i % 500 == 0:
                print(f"  处理进度: {i+1}/{len(self.theta_command)}")

        self.link_flexibility_errors = np.array(self.link_flexibility_errors)

        # 统计分析
        flexibility_stats = {
            'mean_pos_error': np.mean(np.sqrt(np.sum(self.link_flexibility_errors[:, :3]**2, axis=1))),
            'mean_angle_error': np.mean(np.sqrt(np.sum(self.link_flexibility_errors[:, 3:]**2, axis=1))),
            'max_pos_error': np.max(np.sqrt(np.sum(self.link_flexibility_errors[:, :3]**2, axis=1))),
            'max_angle_error': np.max(np.sqrt(np.sum(self.link_flexibility_errors[:, 3:]**2, axis=1))),
            'std_pos_error': np.std(np.sqrt(np.sum(self.link_flexibility_errors[:, :3]**2, axis=1))),
            'std_angle_error': np.std(np.sqrt(np.sum(self.link_flexibility_errors[:, 3:]**2, axis=1)))
        }

        print(f"连杆柔性误差统计:")
        print(f"  平均位置误差: {flexibility_stats['mean_pos_error']:.6f} mm")
        print(f"  平均角度误差: {flexibility_stats['mean_angle_error']:.6f} 度")
        print(f"  最大位置误差: {flexibility_stats['max_pos_error']:.6f} mm")
        print(f"  最大角度误差: {flexibility_stats['max_angle_error']:.6f} 度")

        self.flexibility_stats = flexibility_stats
        return self.link_flexibility_errors

    def convert_joint_errors_to_end_effector(self):
        """将关节传动误差转换为末端误差"""
        print("=== 转换关节误差为末端误差 ===")

        self.gear_end_effector_errors = []

        for i, (joint_angles, gear_errors) in enumerate(zip(self.theta_command, self.gear_transmission_errors)):

            # 计算雅可比矩阵
            jacobian = self.calculate_jacobian(joint_angles)

            # 将关节角度误差转换为末端位姿误差
            # 角度误差需要转换为弧度
            gear_errors_rad = np.deg2rad(gear_errors)
            end_effector_error = jacobian @ gear_errors_rad

            self.gear_end_effector_errors.append(end_effector_error)

            if i % 500 == 0:
                print(f"  处理进度: {i+1}/{len(self.theta_command)}")

        self.gear_end_effector_errors = np.array(self.gear_end_effector_errors)

        # 统计分析
        gear_ee_stats = {
            'mean_pos_error': np.mean(np.sqrt(np.sum(self.gear_end_effector_errors[:, :3]**2, axis=1))),
            'mean_angle_error': np.mean(np.sqrt(np.sum(self.gear_end_effector_errors[:, 3:]**2, axis=1))),
            'max_pos_error': np.max(np.sqrt(np.sum(self.gear_end_effector_errors[:, :3]**2, axis=1))),
            'max_angle_error': np.max(np.sqrt(np.sum(self.gear_end_effector_errors[:, 3:]**2, axis=1)))
        }

        print(f"减速机误差对末端的影响:")
        print(f"  平均位置误差: {gear_ee_stats['mean_pos_error']:.6f} mm")
        print(f"  平均角度误差: {gear_ee_stats['mean_angle_error']:.6f} 度")

        self.gear_ee_stats = gear_ee_stats
        return self.gear_end_effector_errors

    def decompose_total_errors_theoretical(self):
        """基于理论公式分解总误差"""
        print("=== 基于理论公式的误差分解 ===")

        # 计算几何误差 (剩余误差)
        self.geometric_errors = (self.total_errors -
                               self.gear_end_effector_errors -
                               self.link_flexibility_errors)

        # 计算各误差源的贡献
        total_pos_error = np.mean(np.sqrt(np.sum(self.total_errors[:, :3]**2, axis=1)))
        gear_pos_error = np.mean(np.sqrt(np.sum(self.gear_end_effector_errors[:, :3]**2, axis=1)))
        flex_pos_error = np.mean(np.sqrt(np.sum(self.link_flexibility_errors[:, :3]**2, axis=1)))
        geom_pos_error = np.mean(np.sqrt(np.sum(self.geometric_errors[:, :3]**2, axis=1)))

        total_angle_error = np.mean(np.sqrt(np.sum(self.total_errors[:, 3:]**2, axis=1)))
        gear_angle_error = np.mean(np.sqrt(np.sum(self.gear_end_effector_errors[:, 3:]**2, axis=1)))
        flex_angle_error = np.mean(np.sqrt(np.sum(self.link_flexibility_errors[:, 3:]**2, axis=1)))
        geom_angle_error = np.mean(np.sqrt(np.sum(self.geometric_errors[:, 3:]**2, axis=1)))

        # 误差分解结果
        self.theoretical_decomposition = {
            'total_pos_error': total_pos_error,
            'gear_pos_error': gear_pos_error,
            'flexibility_pos_error': flex_pos_error,
            'geometric_pos_error': geom_pos_error,
            'total_angle_error': total_angle_error,
            'gear_angle_error': gear_angle_error,
            'flexibility_angle_error': flex_angle_error,
            'geometric_angle_error': geom_angle_error,
            'gear_pos_contribution': gear_pos_error / total_pos_error * 100,
            'flexibility_pos_contribution': flex_pos_error / total_pos_error * 100,
            'geometric_pos_contribution': geom_pos_error / total_pos_error * 100,
            'gear_angle_contribution': gear_angle_error / total_angle_error * 100,
            'flexibility_angle_contribution': flex_angle_error / total_angle_error * 100,
            'geometric_angle_contribution': geom_angle_error / total_angle_error * 100
        }

        print(f"\n基于理论公式的误差分解结果:")
        print(f"位置误差分解:")
        print(f"  总位置误差: {total_pos_error:.6f} mm")
        print(f"  减速机贡献: {gear_pos_error:.6f} mm ({self.theoretical_decomposition['gear_pos_contribution']:.1f}%)")
        print(f"  柔性贡献: {flex_pos_error:.6f} mm ({self.theoretical_decomposition['flexibility_pos_contribution']:.1f}%)")
        print(f"  几何贡献: {geom_pos_error:.6f} mm ({self.theoretical_decomposition['geometric_pos_contribution']:.1f}%)")

        print(f"\n角度误差分解:")
        print(f"  总角度误差: {total_angle_error:.6f} 度")
        print(f"  减速机贡献: {gear_angle_error:.6f} 度 ({self.theoretical_decomposition['gear_angle_contribution']:.1f}%)")
        print(f"  柔性贡献: {flex_angle_error:.6f} 度 ({self.theoretical_decomposition['flexibility_angle_contribution']:.1f}%)")
        print(f"  几何贡献: {geom_angle_error:.6f} 度 ({self.theoretical_decomposition['geometric_angle_contribution']:.1f}%)")

        return self.theoretical_decomposition

    def create_theoretical_visualizations(self):
        """创建基于理论公式的可视化"""
        print("=== 生成理论误差分析图表 ===")

        import os
        os.makedirs("输出结果", exist_ok=True)

        # 1. 误差源对比图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 位置误差对比
        pos_errors = [
            self.theoretical_decomposition['gear_pos_error'],
            self.theoretical_decomposition['flexibility_pos_error'],
            self.theoretical_decomposition['geometric_pos_error']
        ]
        labels = ['减速机传动误差', '连杆柔性误差', '几何误差']
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']

        axes[0, 0].bar(labels, pos_errors, color=colors, alpha=0.8)
        axes[0, 0].set_title('位置误差源对比', fontsize=14, fontweight='bold')
        axes[0, 0].set_ylabel('位置误差 (mm)')
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True, alpha=0.3)

        # 角度误差对比
        angle_errors = [
            self.theoretical_decomposition['gear_angle_error'],
            self.theoretical_decomposition['flexibility_angle_error'],
            self.theoretical_decomposition['geometric_angle_error']
        ]

        axes[0, 1].bar(labels, angle_errors, color=colors, alpha=0.8)
        axes[0, 1].set_title('角度误差源对比', fontsize=14, fontweight='bold')
        axes[0, 1].set_ylabel('角度误差 (度)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        axes[0, 1].grid(True, alpha=0.3)

        # 位置误差贡献饼图
        pos_contributions = [
            self.theoretical_decomposition['gear_pos_contribution'],
            self.theoretical_decomposition['flexibility_pos_contribution'],
            self.theoretical_decomposition['geometric_pos_contribution']
        ]

        axes[1, 0].pie(pos_contributions, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        axes[1, 0].set_title('位置误差贡献分析', fontsize=14, fontweight='bold')

        # 角度误差贡献饼图
        angle_contributions = [
            self.theoretical_decomposition['gear_angle_contribution'],
            self.theoretical_decomposition['flexibility_angle_contribution'],
            self.theoretical_decomposition['geometric_angle_contribution']
        ]

        axes[1, 1].pie(angle_contributions, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        axes[1, 1].set_title('角度误差贡献分析', fontsize=14, fontweight='bold')

        plt.tight_layout()
        plt.savefig('输出结果/理论误差源分析.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 2. 减速机误差周期性分析
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        for i in range(6):
            row = i // 3
            col = i % 3

            joint_angles = self.theta_command[:, i]
            gear_errors = self.gear_transmission_errors[:, i]

            axes[row, col].scatter(joint_angles, gear_errors, alpha=0.6, s=1, color='red')
            axes[row, col].set_xlabel(f'关节{i+1}指令角度 (度)')
            axes[row, col].set_ylabel('传动误差 (度)')
            axes[row, col].set_title(f'关节{i+1}减速机传动误差')
            axes[row, col].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('输出结果/减速机传动误差分析.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ 理论误差分析图表已生成")

    def run_theoretical_analysis(self):
        """运行基于理论公式的完整分析"""
        print("="*80)
        print("🔬 基于理论公式的机器人误差源计算")
        print("="*80)

        # 1. 加载数据
        self.load_experimental_data()

        # 2. 计算实际关节角度
        self.calculate_actual_joint_angles()

        # 3. 计算减速机传动误差
        self.calculate_gear_transmission_errors()

        # 4. 计算连杆柔性误差
        self.calculate_link_flexibility_errors()

        # 5. 转换关节误差为末端误差
        self.convert_joint_errors_to_end_effector()

        # 6. 分解总误差
        self.decompose_total_errors_theoretical()

        # 7. 生成可视化
        self.create_theoretical_visualizations()

        print("\n✅ 基于理论公式的误差源计算完成!")
        return True

def main():
    """主函数"""
    # 创建理论误差计算系统
    calculator = TheoreticalErrorCalculation()

    # 运行完整分析
    calculator.run_theoretical_analysis()

if __name__ == "__main__":
    main()