#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试理论计算是否正确
"""

import numpy as np
import pandas as pd
from scipy.spatial.transform import Rotation as R

class RobotKinematics:
    """机器人运动学计算类 - 直接复制正确实现"""
    
    def __init__(self, dh_params=None):
        """初始化机器人运动学参数"""
        if dh_params is None:
            # Staubli TX60机器人的M-DH参数 (基于论文中的表1)
            self.dh_params = np.array([
                [0,   np.pi/2,        0,   np.pi,     0],      # 关节1: θ=π, d=0, a=0, α=π/2
                [290, 0,        0,   np.pi/2,   0],      # 关节2: θ=π/2, d=0, a=290, α=0
                [0,   np.pi/2,  20,  np.pi/2,   0],      # 关节3: θ=π/2, d=20, a=0, α=π/2
                [0,   np.pi/2,  310, np.pi,     0],      # 关节4: θ=π, d=310, a=0, α=π/2
                [0,   np.pi/2,  0,   np.pi,     0],      # 关节5: θ=π, d=0, a=0, α=π/2
                [0,   0,        70,  0,         0]       # 关节6: θ=0, d=70, a=0, α=0
            ])
        else:
            self.dh_params = np.array(dh_params)
    
    def mdh_transform(self, a, alpha, d, theta, beta=0):
        """计算修正DH变换矩阵 (M-DH)"""
        # 转换单位：mm -> m
        a_m = a / 1000.0
        d_m = d / 1000.0

        ct = np.cos(theta)
        st = np.sin(theta)
        ca = np.cos(alpha)
        sa = np.sin(alpha)
        cb = np.cos(beta)
        sb = np.sin(beta)

        # 根据论文公式(1)的M-DH变换矩阵
        T = np.array([
            [ct*cb - st*sa*sb,  -st*ca,  ct*sb + st*sa*cb,  a_m*ct],
            [st*cb + ct*sa*sb,   ct*ca,  st*sb - ct*sa*cb,  a_m*st],
            [-ca*sb,             sa,     ca*cb,             d_m],
            [0,                  0,      0,                 1]
        ])

        return T
    
    def forward_kinematics(self, joint_angles_deg):
        """正向运动学计算"""
        # 确保输入是numpy数组，并转换为弧度
        joint_angles = np.array(joint_angles_deg)
        joint_angles_rad = np.deg2rad(joint_angles)

        # 初始化变换矩阵为单位矩阵
        T = np.eye(4)

        # 逐个关节计算变换矩阵
        for i in range(6):
            a, alpha, d, theta_offset, beta = self.dh_params[i]
            theta = joint_angles_rad[i] + theta_offset

            # 计算当前关节的M-DH变换矩阵
            T_i = self.mdh_transform(a, alpha, d, theta, beta)

            # 累积变换
            T = T @ T_i

        # 提取位置 (转换为mm)
        position = T[:3, 3] * 1000.0  # m -> mm

        # 提取旋转矩阵并转换为欧拉角
        rotation_matrix = T[:3, :3]

        # 使用XYZ外旋欧拉角（经过测试验证的最佳方法）
        r = R.from_matrix(rotation_matrix)
        euler_angles = r.as_euler('XYZ', degrees=True)  # XYZ外旋

        # 组合位姿
        pose = np.concatenate([position, euler_angles])

        return pose

def test_theoretical_calculation():
    """测试理论计算是否正确"""
    print("=== 测试理论计算 ===")
    
    try:
        # 加载数据
        joint_data = pd.read_excel('../theta2000.xlsx', header=None)
        joint_data.columns = [f'theta_{i+1}' for i in range(6)]
        measured_data = pd.read_excel('../real2000.xlsx').values
        
        # 创建机器人模型
        robot = RobotKinematics()
        
        # 计算理论位姿（前100个样本）
        n_test = 100
        theoretical_poses = []
        for i in range(n_test):
            joint_angles = joint_data.iloc[i].values
            pose = robot.forward_kinematics(joint_angles)
            theoretical_poses.append(pose)
        
        theoretical_poses = np.array(theoretical_poses)
        measured_poses = measured_data[:n_test]
        
        # 计算误差并修复角度连续性
        raw_errors = measured_poses - theoretical_poses
        errors = raw_errors.copy()
        
        # 修复角度连续性
        for i in range(errors.shape[0]):
            for j in range(3, 6):
                error = errors[i, j]
                candidates = [error, error + 360, error - 360]
                errors[i, j] = min(candidates, key=abs)
        
        # 计算统计
        pos_errors = np.sqrt(np.sum(errors[:, :3]**2, axis=1))
        angle_errors = np.sqrt(np.sum(errors[:, 3:]**2, axis=1))
        
        avg_pos_error = np.mean(pos_errors)
        avg_angle_error = np.mean(angle_errors)
        
        print(f"测试样本数: {n_test}")
        print(f"平均位置误差: {avg_pos_error:.6f} mm")
        print(f"平均角度误差: {avg_angle_error:.6f} °")
        
        # 与论文基准对比
        target_pos = 0.708
        target_angle = 0.179
        
        print(f"\n与论文基准对比:")
        print(f"位置误差: {avg_pos_error:.6f} mm (目标: {target_pos:.3f} mm)")
        print(f"角度误差: {avg_angle_error:.6f} ° (目标: {target_angle:.3f} °)")
        
        pos_diff = abs(avg_pos_error - target_pos)
        angle_diff = abs(avg_angle_error - target_angle)
        
        if pos_diff < 0.05 and angle_diff < 0.05:
            print("✅ 理论计算正确！")
        else:
            print("❌ 理论计算有问题")
            
        return avg_pos_error, avg_angle_error
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None, None

if __name__ == "__main__":
    test_theoretical_calculation()
