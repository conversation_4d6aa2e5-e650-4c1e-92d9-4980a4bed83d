#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模型训练和评估脚本
用于预测机器人位姿参数
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class MLAnalysis:
    def __init__(self):
        self.models = {}
        self.results = {}
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")

        # 加载目标变量 (real2000.xlsx) - 有表头
        self.y_data = pd.read_excel('real2000.xlsx')
        print(f"目标变量形状: {self.y_data.shape}")
        print(f"目标变量列名: {self.y_data.columns.tolist()}")

        # 加载特征变量 (theta2000.xlsx) - 没有表头，第一行是数据
        self.X_data = pd.read_excel('theta2000.xlsx', header=None)
        print(f"特征变量原始形状: {self.X_data.shape}")

        # 重命名theta2000的列名
        self.X_data.columns = [f'theta_{i+1}' for i in range(self.X_data.shape[1])]

        # 现在两个数据集都应该有2000行
        print(f"修正后的数据形状 - X: {self.X_data.shape}, y: {self.y_data.shape}")

        # 验证数据对应关系
        if len(self.X_data) != len(self.y_data):
            print(f"警告：数据长度不匹配！X: {len(self.X_data)}, y: {len(self.y_data)}")
            min_length = min(len(self.X_data), len(self.y_data))
            self.X_data = self.X_data.iloc[:min_length]
            self.y_data = self.y_data.iloc[:min_length]
            print(f"已调整为相同长度: {min_length}")

        print(f"最终数据形状 - X: {self.X_data.shape}, y: {self.y_data.shape}")

        return self.X_data, self.y_data
    
    def explore_data(self):
        """数据探索性分析"""
        print("\n=== 数据探索性分析 ===")
        
        # 基本统计信息
        print("\n特征变量统计信息:")
        print(self.X_data.describe())
        
        print("\n目标变量统计信息:")
        print(self.y_data.describe())
        
        # 检查缺失值
        print(f"\n特征变量缺失值: {self.X_data.isnull().sum().sum()}")
        print(f"目标变量缺失值: {self.y_data.isnull().sum().sum()}")
        
        # 相关性分析
        plt.figure(figsize=(15, 10))
        
        # 特征变量相关性
        plt.subplot(2, 2, 1)
        sns.heatmap(self.X_data.corr(), annot=True, cmap='coolwarm', center=0)
        plt.title('特征变量相关性矩阵')
        
        # 目标变量相关性
        plt.subplot(2, 2, 2)
        sns.heatmap(self.y_data.corr(), annot=True, cmap='coolwarm', center=0)
        plt.title('目标变量相关性矩阵')
        
        # 特征分布
        plt.subplot(2, 2, 3)
        self.X_data.hist(bins=30, alpha=0.7)
        plt.suptitle('特征变量分布')
        
        # 目标变量分布
        plt.subplot(2, 2, 4)
        self.y_data.hist(bins=30, alpha=0.7)
        plt.suptitle('目标变量分布')
        
        plt.tight_layout()
        plt.savefig('data_exploration.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def prepare_data(self):
        """数据预处理"""
        print("\n=== 数据预处理 ===")
        
        # 分割训练集和测试集
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            self.X_data, self.y_data, test_size=0.2, random_state=42
        )
        
        # 标准化特征
        self.X_train_scaled = self.scaler_X.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler_X.transform(self.X_test)
        
        print(f"训练集形状: X={self.X_train_scaled.shape}, y={self.y_train.shape}")
        print(f"测试集形状: X={self.X_test_scaled.shape}, y={self.y_test.shape}")
    
    def initialize_models(self):
        """初始化机器学习模型"""
        print("\n=== 初始化模型 ===")
        
        self.models = {
            '线性回归': LinearRegression(),
            '岭回归': Ridge(alpha=1.0),
            'Lasso回归': Lasso(alpha=1.0),
            '随机森林': RandomForestRegressor(n_estimators=100, random_state=42),
            '梯度提升': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'XGBoost': xgb.XGBRegressor(n_estimators=100, random_state=42),
            'LightGBM': lgb.LGBMRegressor(n_estimators=100, random_state=42, verbose=-1),
            'SVM': SVR(kernel='rbf'),
            '神经网络': MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=500, random_state=42)
        }
        
        print(f"初始化了 {len(self.models)} 个模型")
    
    def train_and_evaluate(self):
        """训练和评估所有模型"""
        print("\n=== 模型训练和评估 ===")

        self.results = {}
        target_names = self.y_data.columns.tolist()

        # 为每个目标变量分别训练模型
        for target_idx, target_name in enumerate(target_names):
            print(f"\n正在预测目标变量: {target_name}")
            print("-" * 50)

            y_train_single = self.y_train.iloc[:, target_idx]
            y_test_single = self.y_test.iloc[:, target_idx]

            target_results = {}

            for name, model in self.models.items():
                print(f"训练 {name}...")

                try:
                    # 创建模型副本
                    from sklearn.base import clone
                    model_copy = clone(model)

                    # 训练模型
                    if name in ['SVM', '神经网络']:
                        # 对于SVM和神经网络，使用标准化数据
                        model_copy.fit(self.X_train_scaled, y_train_single)
                        y_pred = model_copy.predict(self.X_test_scaled)

                        # 交叉验证
                        cv_scores = cross_val_score(model_copy, self.X_train_scaled, y_train_single,
                                                  cv=5, scoring='r2')
                    else:
                        # 对于树模型，使用原始数据
                        model_copy.fit(self.X_train, y_train_single)
                        y_pred = model_copy.predict(self.X_test)

                        # 交叉验证
                        cv_scores = cross_val_score(model_copy, self.X_train, y_train_single,
                                                  cv=5, scoring='r2')

                    # 计算评估指标
                    mse = mean_squared_error(y_test_single, y_pred)
                    mae = mean_absolute_error(y_test_single, y_pred)
                    r2 = r2_score(y_test_single, y_pred)
                    rmse = np.sqrt(mse)

                    target_results[name] = {
                        'model': model_copy,
                        'mse': mse,
                        'mae': mae,
                        'rmse': rmse,
                        'r2': r2,
                        'cv_mean': cv_scores.mean(),
                        'cv_std': cv_scores.std(),
                        'predictions': y_pred,
                        'actual': y_test_single
                    }

                    print(f"  {name} - R²: {r2:.4f}, RMSE: {rmse:.4f}, CV R²: {cv_scores.mean():.4f}±{cv_scores.std():.4f}")

                except Exception as e:
                    print(f"  {name} 训练失败: {str(e)}")
                    continue

            self.results[target_name] = target_results
    
    def generate_report(self):
        """生成详细报告"""
        print("\n" + "="*80)
        print("机器学习模型性能对比报告")
        print("="*80)

        # 为每个目标变量生成报告
        all_reports = {}

        for target_name, target_results in self.results.items():
            print(f"\n目标变量: {target_name}")
            print("-" * 50)

            # 创建结果DataFrame
            report_data = []
            for model_name, result in target_results.items():
                report_data.append({
                    '模型': model_name,
                    'R²得分': result['r2'],
                    'RMSE': result['rmse'],
                    'MAE': result['mae'],
                    '交叉验证R²': result['cv_mean'],
                    '交叉验证标准差': result['cv_std']
                })

            df_report = pd.DataFrame(report_data)
            df_report = df_report.sort_values('R²得分', ascending=False)

            print("\n模型性能排名:")
            print(df_report.to_string(index=False, float_format='%.4f'))

            all_reports[target_name] = df_report

        # 保存所有报告到Excel文件
        with pd.ExcelWriter('model_performance_report.xlsx', engine='openpyxl') as writer:
            for target_name, df_report in all_reports.items():
                # 清理工作表名称（Excel不允许某些字符）
                sheet_name = target_name.replace('/', '_').replace('\\', '_').replace(':', '_')
                sheet_name = sheet_name[:31]  # Excel工作表名称限制为31个字符
                df_report.to_excel(writer, sheet_name=sheet_name, index=False)

        # 生成综合性能图表
        self.plot_performance_comparison()

        return all_reports

    def plot_performance_comparison(self):
        """绘制性能对比图表"""
        print("\n生成性能对比图表...")

        # 准备数据
        models = list(self.models.keys())
        targets = list(self.results.keys())

        # 创建R²得分矩阵
        r2_matrix = np.zeros((len(models), len(targets)))
        rmse_matrix = np.zeros((len(models), len(targets)))

        for i, model_name in enumerate(models):
            for j, target_name in enumerate(targets):
                if model_name in self.results[target_name]:
                    r2_matrix[i, j] = self.results[target_name][model_name]['r2']
                    rmse_matrix[i, j] = self.results[target_name][model_name]['rmse']
                else:
                    r2_matrix[i, j] = np.nan
                    rmse_matrix[i, j] = np.nan

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(20, 15))

        # R²得分热力图
        sns.heatmap(r2_matrix, annot=True, fmt='.3f', cmap='RdYlBu_r',
                   xticklabels=targets, yticklabels=models, ax=axes[0, 0])
        axes[0, 0].set_title('R²得分对比 (越高越好)')
        axes[0, 0].set_xlabel('目标变量')
        axes[0, 0].set_ylabel('模型')

        # RMSE热力图
        sns.heatmap(rmse_matrix, annot=True, fmt='.1f', cmap='RdYlBu',
                   xticklabels=targets, yticklabels=models, ax=axes[0, 1])
        axes[0, 1].set_title('RMSE对比 (越低越好)')
        axes[0, 1].set_xlabel('目标变量')
        axes[0, 1].set_ylabel('模型')

        # 平均R²得分条形图
        avg_r2 = np.nanmean(r2_matrix, axis=1)
        axes[1, 0].bar(models, avg_r2)
        axes[1, 0].set_title('各模型平均R²得分')
        axes[1, 0].set_ylabel('平均R²得分')
        axes[1, 0].tick_params(axis='x', rotation=45)

        # 平均RMSE条形图
        avg_rmse = np.nanmean(rmse_matrix, axis=1)
        axes[1, 1].bar(models, avg_rmse)
        axes[1, 1].set_title('各模型平均RMSE')
        axes[1, 1].set_ylabel('平均RMSE')
        axes[1, 1].tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 生成模型优缺点分析
        self.analyze_model_pros_cons()

    def analyze_model_pros_cons(self):
        """分析各模型的优缺点"""
        print("\n" + "="*80)
        print("模型优缺点分析")
        print("="*80)

        model_analysis = {
            '线性回归': {
                '优点': ['计算速度快', '模型简单易解释', '不容易过拟合', '适合线性关系'],
                '缺点': ['无法捕捉非线性关系', '对异常值敏感', '假设特征间线性无关'],
                '适用场景': '数据量小、特征间线性关系明显的场景'
            },
            '岭回归': {
                '优点': ['防止过拟合', '处理多重共线性', '稳定性好'],
                '缺点': ['仍然是线性模型', '需要调参', '特征选择能力弱'],
                '适用场景': '特征较多、存在多重共线性的线性回归问题'
            },
            'Lasso回归': {
                '优点': ['自动特征选择', '防止过拟合', '模型稀疏'],
                '缺点': ['可能丢失重要特征', '对相关特征选择不稳定'],
                '适用场景': '高维数据、需要特征选择的场景'
            },
            '随机森林': {
                '优点': ['处理非线性关系', '不易过拟合', '可处理缺失值', '提供特征重要性'],
                '缺点': ['模型复杂度高', '内存消耗大', '对噪声敏感'],
                '适用场景': '中等规模数据、非线性关系复杂的问题'
            },
            '梯度提升': {
                '优点': ['预测精度高', '处理非线性关系强', '可处理不同类型特征'],
                '缺点': ['容易过拟合', '训练时间长', '参数调优复杂'],
                '适用场景': '对精度要求高、愿意花时间调参的场景'
            },
            'XGBoost': {
                '优点': ['性能优异', '内置正则化', '支持并行计算', '处理缺失值'],
                '缺点': ['参数众多', '内存消耗大', '对参数敏感'],
                '适用场景': '竞赛、对性能要求极高的商业场景'
            },
            'LightGBM': {
                '优点': ['训练速度快', '内存占用少', '精度高', '支持类别特征'],
                '缺点': ['小数据集容易过拟合', '对参数敏感'],
                '适用场景': '大规模数据、对训练速度有要求的场景'
            },
            'SVM': {
                '优点': ['适合高维数据', '内存效率高', '核函数灵活'],
                '缺点': ['大数据集训练慢', '对参数和核函数选择敏感', '不提供概率估计'],
                '适用场景': '中小规模、高维数据的分类和回归问题'
            },
            '神经网络': {
                '优点': ['强大的非线性拟合能力', '适应性强', '可处理复杂模式'],
                '缺点': ['需要大量数据', '训练时间长', '容易过拟合', '黑盒模型'],
                '适用场景': '大数据集、复杂非线性关系的问题'
            }
        }

        for model_name, analysis in model_analysis.items():
            print(f"\n{model_name}:")
            print(f"  优点: {', '.join(analysis['优点'])}")
            print(f"  缺点: {', '.join(analysis['缺点'])}")
            print(f"  适用场景: {analysis['适用场景']}")

        # 基于实际性能给出推荐
        print(f"\n基于当前数据的模型推荐:")
        self.recommend_models()

    def recommend_models(self):
        """基于性能结果推荐模型"""
        # 计算每个模型的平均性能
        model_avg_performance = {}

        for model_name in self.models.keys():
            r2_scores = []
            for target_name in self.results.keys():
                if model_name in self.results[target_name]:
                    r2_scores.append(self.results[target_name][model_name]['r2'])

            if r2_scores:
                model_avg_performance[model_name] = np.mean(r2_scores)

        # 排序推荐
        sorted_models = sorted(model_avg_performance.items(), key=lambda x: x[1], reverse=True)

        print("\n推荐排序 (基于平均R²得分):")
        for i, (model_name, avg_r2) in enumerate(sorted_models[:3], 1):
            print(f"{i}. {model_name}: 平均R² = {avg_r2:.4f}")

def main():
    """主函数"""
    print("开始机器学习分析...")

    # 创建分析对象
    ml_analysis = MLAnalysis()

    # 加载数据
    ml_analysis.load_data()

    # 数据探索
    ml_analysis.explore_data()

    # 数据预处理
    ml_analysis.prepare_data()

    # 初始化模型
    ml_analysis.initialize_models()

    # 训练和评估
    ml_analysis.train_and_evaluate()

    # 生成报告
    ml_analysis.generate_report()

    print("\n分析完成！")
    print("结果已保存到:")
    print("- model_performance_report.xlsx (详细性能报告)")
    print("- data_exploration.png (数据探索图表)")
    print("- model_performance_comparison.png (性能对比图表)")

if __name__ == "__main__":
    main()
