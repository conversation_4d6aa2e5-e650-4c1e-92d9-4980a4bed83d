#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
理论计算模块
基于M-DH运动学模型计算机器人末端位姿的理论值
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as R

class MDHKinematics:
    """
    修正DH参数运动学计算类
    """
    
    def __init__(self):
        # Staubli TX60机器人的M-DH参数 (根据论文调整)
        # [alpha, a, d, theta_offset]
        self.dh_params = np.array([
            [np.pi/2,      0,     0,   np.pi],      # 关节1
            [0,          290,     0, np.pi/2], # 关节2  
            [np.pi/2,      0,    20,     np.pi/2],      # 关节3
            [np.pi/2,      0,   310,   np.pi],      # 关节4
            [np.pi/2,      0,     0,     np.pi],      # 关节5
            [0,            0,    70,    0]       # 关节6
        ])
        
    def rotation_x(self, angle):
        """绕X轴旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [1, 0,  0],
            [0, c, -s],
            [0, s,  c]
        ])
    
    def rotation_y(self, angle):
        """绕Y轴旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [c,  0, s],
            [0,  1, 0],
            [-s, 0, c]
        ])
    
    def rotation_z(self, angle):
        """绕Z轴旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [c, -s, 0],
            [s,  c, 0],
            [0,  0, 1]
        ])
    
    def translation_matrix(self, x, y, z):
        """平移矩阵"""
        return np.array([
            [1, 0, 0, x],
            [0, 1, 0, y],
            [0, 0, 1, z],
            [0, 0, 0, 1]
        ])
    
    def rotation_matrix_4x4(self, R_3x3):
        """将3x3旋转矩阵转换为4x4齐次矩阵"""
        T = np.eye(4)
        T[:3, :3] = R_3x3
        return T
    
    def mdh_transform(self, alpha, a, d, theta):
        """
        计算修正DH参数的变换矩阵
        根据论文公式: A_i = Rot(Z_i, θ_i) * Trans(Z_i, d_i) * Trans(X_i, a_i) * Rot(X_i, α_i) * Rot(Y_i, β_i)
        这里简化为标准M-DH: A_i = Rot(X_{i-1}, α_{i-1}) * Trans(X_{i-1}, a_{i-1}) * Rot(Z_i, θ_i) * Trans(Z_i, d_i)
        """
        
        # 绕X轴旋转alpha
        Rx = self.rotation_matrix_4x4(self.rotation_x(alpha))
        
        # 沿X轴平移a
        Tx = self.translation_matrix(a, 0, 0)
        
        # 绕Z轴旋转theta
        Rz = self.rotation_matrix_4x4(self.rotation_z(theta))
        
        # 沿Z轴平移d
        Tz = self.translation_matrix(0, 0, d)
        
        # 组合变换矩阵
        T = Rx @ Tx @ Rz @ Tz
        
        return T
    
    def forward_kinematics(self, joint_angles):
        """
        正运动学计算
        输入: joint_angles - 6个关节角度 [θ1, θ2, θ3, θ4, θ5, θ6]
        输出: 末端执行器位姿 [x, y, z, rx, ry, rz]
        """
        
        # 初始化基坐标系
        T = np.eye(4)
        
        # 逐个关节计算变换矩阵
        for i in range(6):
            alpha = self.dh_params[i, 0]
            a = self.dh_params[i, 1]
            d = self.dh_params[i, 2]
            theta_offset = self.dh_params[i, 3]
            
            # 当前关节角度
            theta = joint_angles[i] + theta_offset
            
            # 计算当前关节的变换矩阵
            T_i = self.mdh_transform(alpha, a, d, theta)
            
            # 累积变换
            T = T @ T_i
        
        # 提取位置
        position = T[:3, 3]
        
        # 提取旋转矩阵并转换为欧拉角
        rotation_matrix = T[:3, :3]
        rotation = R.from_matrix(rotation_matrix)
        euler_angles = rotation.as_euler('xyz', degrees=False)
        
        return np.concatenate([position, euler_angles])
    
    def batch_forward_kinematics(self, joint_angles_batch):
        """
        批量正运动学计算
        输入: joint_angles_batch - (n, 6) 数组
        输出: poses - (n, 6) 数组 [x, y, z, rx, ry, rz]
        """
        n_samples = joint_angles_batch.shape[0]
        poses = np.zeros((n_samples, 6))
        
        for i in range(n_samples):
            poses[i] = self.forward_kinematics(joint_angles_batch[i])
            
        return poses
    
    def calculate_position_only(self, joint_angles_batch):
        """
        只计算位置，不计算姿态（用于与实际位置数据对比）
        输入: joint_angles_batch - (n, 6) 数组
        输出: positions - (n, 3) 数组 [x, y, z]
        """
        n_samples = joint_angles_batch.shape[0]
        positions = np.zeros((n_samples, 3))
        
        for i in range(n_samples):
            pose = self.forward_kinematics(joint_angles_batch[i])
            positions[i] = pose[:3]  # 只取位置部分
            
        return positions

class TheoreticalCalculator:
    """
    理论计算主类
    """
    
    def __init__(self):
        self.kinematics = MDHKinematics()
        self.theoretical_positions = None
        self.actual_positions = None
        self.joint_angles = None
        
    def load_test_data(self, joint_angles, actual_positions):
        """
        加载测试数据
        """
        self.joint_angles = joint_angles
        self.actual_positions = actual_positions
        
        print(f"加载测试数据: {len(joint_angles)} 个样本")
        
    def calculate_theoretical_positions(self):
        """
        计算理论位置
        """
        print("正在计算理论位置...")
        
        self.theoretical_positions = self.kinematics.calculate_position_only(self.joint_angles)
        
        print(f"理论位置计算完成: {self.theoretical_positions.shape}")
        
        return self.theoretical_positions
    
    def calculate_errors(self):
        """
        计算误差指标
        """
        if self.theoretical_positions is None:
            self.calculate_theoretical_positions()
            
        # 位置误差
        position_errors = np.linalg.norm(
            self.theoretical_positions - self.actual_positions, axis=1
        )
        
        # 各轴误差
        x_errors = np.abs(self.theoretical_positions[:, 0] - self.actual_positions[:, 0])
        y_errors = np.abs(self.theoretical_positions[:, 1] - self.actual_positions[:, 1])
        z_errors = np.abs(self.theoretical_positions[:, 2] - self.actual_positions[:, 2])
        
        # 统计指标
        error_stats = {
            'mean_position_error': np.mean(position_errors),
            'std_position_error': np.std(position_errors),
            'max_position_error': np.max(position_errors),
            'min_position_error': np.min(position_errors),
            'rmse_position': np.sqrt(np.mean(position_errors**2)),
            'mean_x_error': np.mean(x_errors),
            'mean_y_error': np.mean(y_errors),
            'mean_z_error': np.mean(z_errors),
            'rmse_x': np.sqrt(np.mean(x_errors**2)),
            'rmse_y': np.sqrt(np.mean(y_errors**2)),
            'rmse_z': np.sqrt(np.mean(z_errors**2))
        }
        
        return position_errors, error_stats
    
    def visualize_results(self):
        """
        可视化理论计算结果
        """
        if self.theoretical_positions is None:
            self.calculate_theoretical_positions()
            
        position_errors, error_stats = self.calculate_errors()
        
        # 创建图形
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 位置对比散点图
        axes[0, 0].scatter(self.actual_positions[:, 0], self.theoretical_positions[:, 0], alpha=0.6)
        axes[0, 0].plot([self.actual_positions[:, 0].min(), self.actual_positions[:, 0].max()],
                       [self.actual_positions[:, 0].min(), self.actual_positions[:, 0].max()], 'r--')
        axes[0, 0].set_xlabel('实际X位置 (mm)')
        axes[0, 0].set_ylabel('理论X位置 (mm)')
        axes[0, 0].set_title('X轴位置对比')
        axes[0, 0].grid(True)
        
        axes[0, 1].scatter(self.actual_positions[:, 1], self.theoretical_positions[:, 1], alpha=0.6)
        axes[0, 1].plot([self.actual_positions[:, 1].min(), self.actual_positions[:, 1].max()],
                       [self.actual_positions[:, 1].min(), self.actual_positions[:, 1].max()], 'r--')
        axes[0, 1].set_xlabel('实际Y位置 (mm)')
        axes[0, 1].set_ylabel('理论Y位置 (mm)')
        axes[0, 1].set_title('Y轴位置对比')
        axes[0, 1].grid(True)
        
        axes[0, 2].scatter(self.actual_positions[:, 2], self.theoretical_positions[:, 2], alpha=0.6)
        axes[0, 2].plot([self.actual_positions[:, 2].min(), self.actual_positions[:, 2].max()],
                       [self.actual_positions[:, 2].min(), self.actual_positions[:, 2].max()], 'r--')
        axes[0, 2].set_xlabel('实际Z位置 (mm)')
        axes[0, 2].set_ylabel('理论Z位置 (mm)')
        axes[0, 2].set_title('Z轴位置对比')
        axes[0, 2].grid(True)
        
        # 误差分布
        axes[1, 0].hist(position_errors, bins=50, alpha=0.7, edgecolor='black')
        axes[1, 0].set_xlabel('位置误差 (mm)')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title(f'位置误差分布\n平均误差: {error_stats["mean_position_error"]:.2f} mm')
        axes[1, 0].grid(True)
        
        # 各轴误差对比
        x_errors = np.abs(self.theoretical_positions[:, 0] - self.actual_positions[:, 0])
        y_errors = np.abs(self.theoretical_positions[:, 1] - self.actual_positions[:, 1])
        z_errors = np.abs(self.theoretical_positions[:, 2] - self.actual_positions[:, 2])
        
        axes[1, 1].boxplot([x_errors, y_errors, z_errors], labels=['X', 'Y', 'Z'])
        axes[1, 1].set_ylabel('绝对误差 (mm)')
        axes[1, 1].set_title('各轴误差分布')
        axes[1, 1].grid(True)
        
        # 误差随样本变化
        axes[1, 2].plot(position_errors, alpha=0.7)
        axes[1, 2].set_xlabel('样本序号')
        axes[1, 2].set_ylabel('位置误差 (mm)')
        axes[1, 2].set_title('位置误差变化趋势')
        axes[1, 2].grid(True)
        
        plt.tight_layout()
        plt.savefig('新实验设计/理论计算结果.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return error_stats
    
    def save_results(self, filename='新实验设计/理论计算结果.xlsx'):
        """
        保存理论计算结果
        """
        if self.theoretical_positions is None:
            self.calculate_theoretical_positions()
            
        position_errors, error_stats = self.calculate_errors()
        
        # 创建结果DataFrame
        results_df = pd.DataFrame({
            'Joint_1': self.joint_angles[:, 0],
            'Joint_2': self.joint_angles[:, 1],
            'Joint_3': self.joint_angles[:, 2],
            'Joint_4': self.joint_angles[:, 3],
            'Joint_5': self.joint_angles[:, 4],
            'Joint_6': self.joint_angles[:, 5],
            'Actual_X': self.actual_positions[:, 0],
            'Actual_Y': self.actual_positions[:, 1],
            'Actual_Z': self.actual_positions[:, 2],
            'Theoretical_X': self.theoretical_positions[:, 0],
            'Theoretical_Y': self.theoretical_positions[:, 1],
            'Theoretical_Z': self.theoretical_positions[:, 2],
            'Error_X': np.abs(self.theoretical_positions[:, 0] - self.actual_positions[:, 0]),
            'Error_Y': np.abs(self.theoretical_positions[:, 1] - self.actual_positions[:, 1]),
            'Error_Z': np.abs(self.theoretical_positions[:, 2] - self.actual_positions[:, 2]),
            'Position_Error': position_errors
        })
        
        # 保存到Excel
        with pd.ExcelWriter(filename) as writer:
            results_df.to_excel(writer, sheet_name='详细结果', index=False)
            
            # 保存统计信息
            stats_df = pd.DataFrame(list(error_stats.items()), columns=['指标', '数值'])
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
        
        print(f"理论计算结果已保存到: {filename}")
        
        return results_df, error_stats

def main():
    """
    主函数 - 执行理论计算
    """
    print("=== 机器人位姿预测实验 - 理论计算 ===\n")
    
    # 这里需要从数据预处理模块获取数据
    # 为了演示，我们创建一些示例数据
    print("注意: 请先运行数据预处理模块获取测试数据")
    
    return None

if __name__ == "__main__":
    main()
