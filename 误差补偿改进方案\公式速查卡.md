# 公式速查卡 - 快速参考

## 🚀 最重要的15个公式（必须记住）

### 【新增】特征工程核心公式

### 1. 误差传播机制 - 公式(44)
```
ε = J(θ)Δθ + ε_nonlinear(θ, θ̇, θ̈)
```
**一句话**：误差 = 线性传播 + 非线性项（包含三角函数）

### 2. 动力学特征 - 公式(52-53)
```
F_inertial = [cos(θᵢ - θⱼ)]  (惯性耦合)
F_coriolis = [sin(θᵢ - θⱼ)]  (科里奥利力)
```
**一句话**：这就是老师要求的sin(θ₁-θ₂)类型特征！

### 3. 运动学特征 - 公式(48-49)
```
F_trig = [sin(θᵢ), cos(θᵢ)]
F_compound = [sin(2θᵢ), cos(2θᵢ), sin(θᵢ/2), cos(θᵢ/2)]
```
**一句话**：来自DH变换矩阵的三角函数项

### 4. 特征完备性 - 公式(45)
```
F = [F_kinematic, F_dynamic, F_coupling, F_singular, F_workspace]
总维度 = 6 + 24 + 30 + 15 + 14 = 89维
```
**一句话**：基于物理原理的完整特征设计

### 5. 奇异性特征 - 公式(58-60)
```
F_internal = [|sin(θ₂ ± θ₃)|, |cos(θ₂ ± θ₃)|]
```
**一句话**：预测机器人什么时候会"卡住"

### 【经典】核心公式

### 6. 正向运动学 - 公式(3)
```
p = F(θ) = [x, y, z, α, β, γ]
```
**一句话**：给定关节角度，计算末端位置

### 7. 误差定义 - 公式(4)
```
ε = p_actual - p_theory
```
**一句话**：误差 = 实际值 - 理论值

### 8. PINN总损失 - 公式(13)
```
L_PINN = L_data + λ_physics × L_physics
```
**一句话**：既要预测准确，又要符合物理定律

### 9. 加权数据损失 - 公式(14)
```
L_data = 0.7 × L_pos + 0.3 × L_ori
```
**一句话**：位置误差比角度误差更重要

### 10. 运动学约束 - 公式(17)
```
L_kinematics = ||∂F/∂θ - J(θ)||²
```
**一句话**：神经网络学到的关系要和理论一致

### 11. 多目标优化 - 公式(9-11)
```
min f1(位置误差), f2(角度误差), f3(模型复杂度)
```
**一句话**：同时优化精度和简单性

### 12. 注意力权重 - 公式(32)
```
Attention = softmax(QK^T/√d_k) × V
```
**一句话**：计算关节间的相互影响

### 13. 确定性初始化 - 公式(23)
```
w_init = (J^T J + αI)^(-1) J^T ε
```
**一句话**：基于物理原理找好起点

### 14. 位置误差计算 - 公式(28)
```
位置误差 = √(εx² + εy² + εz²)
```
**一句话**：三维空间中的直线距离

### 15. 改进率计算 - 公式(43)
```
改进率 = (原误差 - 新误差) / 原误差 × 100%
```
**一句话**：衡量方法好了多少

---

## 📊 公式分类速查

### 🔬 特征工程类（新增重点）
| 公式 | 作用 | 关键词 | 物理意义 |
|------|------|--------|----------|
| (44) | 误差传播机制 | 非线性项 | 为什么需要三角函数 |
| (45) | 特征总体设计 | 89维→63维 | 物理驱动完整性 |
| (48-50) | 运动学特征 | sin,cos | DH变换矩阵项 |
| (52-53) | 动力学特征 | sin(θᵢ-θⱼ) | 老师要求的核心！ |
| (58-60) | 奇异性特征 | sin(θ₂±θ₃) | 预测卡住位置 |
| (64-66) | 完备性定理 | 李群理论 | 数学证明完整性 |

### 🤖 机器人运动学类
| 公式 | 作用 | 关键词 |
|------|------|--------|
| (1) | 变换矩阵连乘 | T = ∏T_i |
| (2) | 单关节变换 | DH参数 |
| (3) | 正向运动学 | θ→位置 |
| (5) | 误差传播 | 雅可比矩阵 |
| (6) | 雅可比定义 | 敏感性分析 |

### 🧠 神经网络类
| 公式 | 作用 | 关键词 |
|------|------|--------|
| (7) | 传统损失 | MSE |
| (8) | Hessian矩阵 | 二阶导数 |
| (13) | PINN损失 | 物理约束 |
| (30-32) | 注意力机制 | Transformer |

### ⚖️ 物理约束类
| 公式 | 作用 | 关键词 |
|------|------|--------|
| (17) | 运动学约束 | 雅可比一致性 |
| (19) | 动力学约束 | 牛顿定律 |
| (20) | 关节限制 | 物理边界 |
| (21) | 几何约束 | 旋转矩阵 |

### 🎯 优化算法类
| 公式 | 作用 | 关键词 |
|------|------|--------|
| (22-24) | 确定性优化 | 避免随机 |
| (25-27) | 多目标优化 | Pareto最优 |
| (34-35) | 收敛保证 | 数学证明 |

---

## 🔤 符号含义速查表

### 希腊字母
| 符号 | 读音 | 含义 | 记忆 |
|------|------|------|------|
| θ | theta | 关节角度 | 角度符号 |
| ε | epsilon | 误差 | error的首字母 |
| λ | lambda | 权重系数 | 调节重要性 |
| α,β,γ | alpha,beta,gamma | 欧拉角 | 姿态角度 |
| μ | mu | 正则化参数 | 防过拟合 |
| τ | tau | 关节力矩 | 扭矩 |

### 数学符号
| 符号 | 含义 | 记忆 |
|------|------|------|
| ∂/∂x | 偏导数 | 对x的变化率 |
| ‖‖ | 范数 | 向量长度 |
| ∏ | 连乘 | 1×2×3×... |
| ∑ | 求和 | 1+2+3+... |
| argmin | 最小化参数 | 找最小值位置 |
| ∀ | 对所有 | for all |
| ∃ | 存在 | there exists |

### 矩阵符号
| 符号 | 含义 | 维度 |
|------|------|------|
| T | 变换矩阵 | 4×4 |
| J | 雅可比矩阵 | 6×6 |
| H | Hessian矩阵 | n×n |
| R | 旋转矩阵 | 3×3 |
| I | 单位矩阵 | n×n |

---

## 🎯 汇报时的公式解释模板

### 模板1：简单解释
> "这个公式的意思是[用大白话说作用]，就像[生活中的比喻]。"

**例子**：
> "公式(13)的意思是PINN不仅要预测准确，还要符合物理定律，就像学生不仅要考试得高分，还要遵守考试规则。"

### 模板2：重要性解释
> "这个公式很重要，因为它解决了[什么问题]，让我们的方法[有什么优势]。"

**例子**：
> "公式(17)很重要，因为它确保神经网络学到的关系符合运动学原理，让我们的预测更可靠。"

### 模板3：结果解释
> "通过这个公式，我们实现了[具体数字]的改进，这意味着[实际意义]。"

**例子**：
> "通过公式(43)计算，我们实现了91.8%的位置精度改进，这意味着机器人可以用于精密制造。"

---

## 🤔 常见问题的公式回答

### Q: "你们的方法和传统方法有什么区别？"
**A**: "主要区别在公式(13)，传统方法只有L_data项，我们加入了L_physics项，确保预测符合物理定律。"

### Q: "为什么要用多目标优化？"
**A**: "因为公式(9-11)显示我们要同时优化三个目标：位置精度、角度精度和模型复杂度，需要找平衡点。"

### Q: "确定性初始化有什么好处？"
**A**: "公式(23)基于物理原理计算初始点，不依赖随机，每次都能得到稳定的好结果。"

### Q: "注意力机制是怎么工作的？"
**A**: "公式(32)计算每个关节对其他关节的影响程度，就像人知道'牵一发而动全身'。"

### Q: "你们的特征工程有什么特别的？"
**A**: "我们不是随便用sin、cos，而是基于机器人动力学方程。比如sin(θ₁-θ₂)来源于惯性矩阵的耦合项，cos(θ₁-θ₂)反映科里奥利力，都有明确的物理意义。"

### Q: "为什么要用sin(θᵢ-θⱼ)这种特征？"
**A**: "这来源于拉格朗日动力学方程中的耦合项。当关节i和j同时运动时，会产生相互作用力，数学表达就是sin(θᵢ-θⱼ)和cos(θᵢ-θⱼ)。"

### Q: "89维特征是怎么设计的？"
**A**: "基于机器人学理论：30维运动学特征（来自DH变换）、24维动力学特征（来自惯性矩阵）、30维耦合特征（来自雅可比分析）、15维奇异性特征、14维工作空间特征。每一维都有物理依据。"

---

## 💡 记忆技巧

### 按重要性记忆
1. **最重要**：公式(3,4,13) - 核心概念
2. **很重要**：公式(17,28,43) - 关键技术
3. **了解即可**：其他公式 - 技术细节

### 按类型记忆
1. **定义类**：什么是什么 (3,4,6)
2. **损失类**：如何优化 (7,13,17)
3. **算法类**：如何实现 (23,32,38)
4. **评估类**：如何衡量 (28,41,43)

### 关键数字记忆
- **0.7, 0.3**：位置和角度权重
- **91.8%, 79.3%**：改进百分比
- **0.708→0.058mm**：位置误差改进
- **6个关节**：机器人自由度

---

## 🎪 汇报小贴士

### 公式展示原则
1. **不要展示复杂公式**：只说作用
2. **多用数字说话**：91.8%比公式更有说服力
3. **结合图表解释**：公式+图=完美
4. **准备简化版本**：复杂公式用大白话

### 应对策略
- **如果被问公式推导**：说"这是基于[某理论]的标准推导"
- **如果被问参数选择**：说"通过实验优化得到"
- **如果被问数学证明**：说"有严格的数学证明，核心思想是..."

**记住**：你是工程师，不是数学家！重点是解决问题，不是推导公式！ 🚀
