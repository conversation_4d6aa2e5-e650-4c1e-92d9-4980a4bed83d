\documentclass[12pt,a4paper]{article}
\usepackage[UTF8]{ctex}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{algorithm}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{bm}
\usepackage{hyperref}
\usepackage{xcolor}
\usepackage{framed}
\usepackage{listings}

\geometry{left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}

% 代码样式设置
\lstset{
    language=Python,
    basicstyle=\ttfamily\small,
    keywordstyle=\color{blue},
    commentstyle=\color{green},
    stringstyle=\color{red},
    numbers=left,
    numberstyle=\tiny,
    frame=single,
    breaklines=true,
    showstringspaces=false
}

\title{\textbf{PINN机器人误差补偿详细公式讲解手册\\第三部分：编程实现与实际应用}}

\date{\today}

\begin{document}

\maketitle

\section{第十四章：核心公式的Python实现}

\subsection{机器人运动学实现}

\subsubsection{DH变换矩阵}
让我们把复杂的DH变换矩阵用代码实现：

\begin{lstlisting}[caption=DH变换矩阵实现]
import numpy as np

def dh_transform(theta, d, a, alpha):
    """
    计算DH变换矩阵
    theta: 关节角度 (弧度)
    d: 连杆偏距
    a: 连杆长度  
    alpha: 连杆扭转角
    """
    ct = np.cos(theta)
    st = np.sin(theta)
    ca = np.cos(alpha)
    sa = np.sin(alpha)
    
    T = np.array([
        [ct, -st*ca,  st*sa, a*ct],
        [st,  ct*ca, -ct*sa, a*st],
        [0,   sa,     ca,    d   ],
        [0,   0,      0,     1   ]
    ])
    return T

def forward_kinematics(joint_angles, dh_params):
    """
    计算正向运动学
    joint_angles: [theta1, theta2, ..., theta6]
    dh_params: [[d1,a1,alpha1], [d2,a2,alpha2], ...]
    """
    T_total = np.eye(4)  # 4x4单位矩阵
    
    for i, theta in enumerate(joint_angles):
        d, a, alpha = dh_params[i]
        T_i = dh_transform(theta, d, a, alpha)
        T_total = T_total @ T_i  # 矩阵乘法
    
    return T_total
\end{lstlisting}

\textcolor{blue}{\textbf{代码解释}}：
\begin{itemize}
\item \texttt{np.eye(4)}：创建4×4单位矩阵
\item \texttt{@}：Python中的矩阵乘法运算符
\item 循环中逐步累乘变换矩阵，得到总变换
\end{itemize}

\subsubsection{雅可比矩阵计算}
雅可比矩阵的数值计算：

\begin{lstlisting}[caption=雅可比矩阵数值计算]
def compute_jacobian(joint_angles, dh_params, delta=1e-6):
    """
    数值计算雅可比矩阵
    delta: 数值微分的步长
    """
    n_joints = len(joint_angles)
    jacobian = np.zeros((6, n_joints))
    
    # 计算当前位姿
    T_current = forward_kinematics(joint_angles, dh_params)
    pos_current = T_current[:3, 3]  # 位置
    rot_current = T_current[:3, :3]  # 旋转矩阵
    
    for i in range(n_joints):
        # 关节角度微小变化
        joint_angles_plus = joint_angles.copy()
        joint_angles_plus[i] += delta
        
        # 计算变化后的位姿
        T_plus = forward_kinematics(joint_angles_plus, dh_params)
        pos_plus = T_plus[:3, 3]
        rot_plus = T_plus[:3, :3]
        
        # 位置雅可比（前3行）
        jacobian[:3, i] = (pos_plus - pos_current) / delta
        
        # 角度雅可比（后3行）- 简化计算
        # 实际应用中需要更复杂的角度微分计算
        rot_diff = rot_plus @ rot_current.T
        angle_diff = rotation_matrix_to_euler(rot_diff)
        jacobian[3:, i] = angle_diff / delta
    
    return jacobian
\end{lstlisting}

\subsection{物理约束的实现}

\subsubsection{运动学约束}
$$\mathcal{L}_{kinematics} = \frac{1}{N} \sum_{i=1}^{N} \left\|\frac{\partial \mathcal{F}(\bm{\theta}_i)}{\partial \bm{\theta}} - \bm{J}(\bm{\theta}_i)\right\|_F^2$$

\begin{lstlisting}[caption=运动学约束实现]
import torch
import torch.nn as nn

class KinematicsConstraint(nn.Module):
    def __init__(self, dh_params):
        super().__init__()
        self.dh_params = dh_params
    
    def forward(self, theta_batch, network_output):
        """
        theta_batch: (batch_size, 6) 关节角度
        network_output: (batch_size, 6) 网络预测的位姿
        """
        batch_size = theta_batch.shape[0]
        constraint_loss = 0.0
        
        for i in range(batch_size):
            theta = theta_batch[i]
            
            # 计算理论雅可比矩阵
            J_theory = self.compute_jacobian_torch(theta)
            
            # 计算网络输出的雅可比矩阵（自动微分）
            J_network = torch.autograd.grad(
                outputs=network_output[i].sum(),
                inputs=theta,
                create_graph=True,
                retain_graph=True
            )[0].reshape(6, 6)
            
            # Frobenius范数
            constraint_loss += torch.norm(J_network - J_theory, 'fro')**2
        
        return constraint_loss / batch_size
\end{lstlisting}

\subsubsection{几何约束}
$$\mathcal{L}_{geometry} = \|\bm{R}^T\bm{R} - \bm{I}\|_F^2 + (\det(\bm{R}) - 1)^2$$

\begin{lstlisting}[caption=几何约束实现]
def geometry_constraint(rotation_matrices):
    """
    rotation_matrices: (batch_size, 3, 3) 旋转矩阵
    """
    batch_size = rotation_matrices.shape[0]
    I = torch.eye(3).expand(batch_size, 3, 3)
    
    # 正交性约束: R^T @ R = I
    RTR = torch.bmm(rotation_matrices.transpose(-1, -2), rotation_matrices)
    orthogonal_loss = torch.norm(RTR - I, dim=(-1, -2))**2
    
    # 行列式约束: det(R) = 1
    det_R = torch.det(rotation_matrices)
    det_loss = (det_R - 1)**2
    
    return (orthogonal_loss + det_loss).mean()
\end{lstlisting}

\section{第十五章：NSGA-II算法的完整实现}

\subsection{个体和种群的数据结构}

\begin{lstlisting}[caption=NSGA-II数据结构]
class Individual:
    def __init__(self, weights):
        self.weights = weights  # 神经网络权重
        self.objectives = None  # [f1, f2, f3] 目标函数值
        self.rank = None        # 前沿等级
        self.crowding_distance = 0.0
        self.dominated_solutions = []  # 被此个体支配的解
        self.domination_count = 0      # 支配此个体的解的数量

class Population:
    def __init__(self, size):
        self.individuals = []
        self.size = size
        self.fronts = []  # 各个前沿
\end{lstlisting}

\subsection{支配关系判断}

\begin{lstlisting}[caption=支配关系实现]
def dominates(ind1, ind2):
    """
    判断ind1是否支配ind2
    返回True如果ind1支配ind2
    """
    # 检查是否在所有目标上都不差于ind2
    all_better_or_equal = all(f1 <= f2 for f1, f2 in 
                             zip(ind1.objectives, ind2.objectives))
    
    # 检查是否在至少一个目标上严格更好
    at_least_one_better = any(f1 < f2 for f1, f2 in 
                             zip(ind1.objectives, ind2.objectives))
    
    return all_better_or_equal and at_least_one_better
\end{lstlisting}

\subsection{非支配排序}

\begin{lstlisting}[caption=非支配排序实现]
def fast_non_dominated_sort(population):
    """
    快速非支配排序算法
    """
    fronts = [[]]  # 第一个前沿
    
    for p in population.individuals:
        p.dominated_solutions = []
        p.domination_count = 0
        
        for q in population.individuals:
            if dominates(p, q):
                p.dominated_solutions.append(q)
            elif dominates(q, p):
                p.domination_count += 1
        
        if p.domination_count == 0:
            p.rank = 0
            fronts[0].append(p)
    
    i = 0
    while len(fronts[i]) > 0:
        next_front = []
        for p in fronts[i]:
            for q in p.dominated_solutions:
                q.domination_count -= 1
                if q.domination_count == 0:
                    q.rank = i + 1
                    next_front.append(q)
        i += 1
        fronts.append(next_front)
    
    return fronts[:-1]  # 移除最后的空前沿
\end{lstlisting}

\subsection{拥挤距离计算}

\begin{lstlisting}[caption=拥挤距离计算]
def calculate_crowding_distance(front):
    """
    计算前沿中每个个体的拥挤距离
    """
    if len(front) <= 2:
        for ind in front:
            ind.crowding_distance = float('inf')
        return
    
    # 初始化拥挤距离
    for ind in front:
        ind.crowding_distance = 0
    
    n_objectives = len(front[0].objectives)
    
    for obj_idx in range(n_objectives):
        # 按第obj_idx个目标排序
        front.sort(key=lambda x: x.objectives[obj_idx])
        
        # 边界点设为无穷大
        front[0].crowding_distance = float('inf')
        front[-1].crowding_distance = float('inf')
        
        # 计算目标函数的范围
        obj_min = front[0].objectives[obj_idx]
        obj_max = front[-1].objectives[obj_idx]
        
        if obj_max - obj_min == 0:
            continue
        
        # 计算中间点的拥挤距离
        for i in range(1, len(front) - 1):
            if front[i].crowding_distance != float('inf'):
                distance = (front[i+1].objectives[obj_idx] - 
                           front[i-1].objectives[obj_idx])
                front[i].crowding_distance += distance / (obj_max - obj_min)
\end{lstlisting}

\section{第十六章：特征工程的实现}

\subsection{140维物理特征构造}

\begin{lstlisting}[caption=物理特征构造]
class PhysicsFeatureExtractor:
    def __init__(self, dh_params):
        self.dh_params = dh_params
    
    def extract_features(self, joint_angles):
        """
        从6维关节角度提取140维物理特征
        """
        theta = np.array(joint_angles)
        features = []
        
        # 1. 运动学特征 (42维)
        features.extend(self.kinematic_features(theta))
        
        # 2. 动力学特征 (36维)
        features.extend(self.dynamic_features(theta))
        
        # 3. 耦合特征 (30维)
        features.extend(self.coupling_features(theta))
        
        # 4. 奇异性特征 (15维)
        features.extend(self.singularity_features(theta))
        
        # 5. 工作空间特征 (17维)
        features.extend(self.workspace_features(theta))
        
        return np.array(features)
    
    def kinematic_features(self, theta):
        """运动学特征 (42维)"""
        features = []
        
        # 基础角度 (6维)
        features.extend(theta)
        
        # 三角函数 (12维)
        features.extend(np.sin(theta))
        features.extend(np.cos(theta))
        
        # 复合角度 (24维)
        features.extend(np.sin(2 * theta))
        features.extend(np.cos(2 * theta))
        features.extend(np.sin(theta / 2))
        features.extend(np.cos(theta / 2))
        
        return features
    
    def dynamic_features(self, theta):
        """动力学特征 (36维)"""
        features = []
        
        # 惯性耦合 (15维)
        for i in range(6):
            for j in range(i+1, 6):
                features.append(np.cos(theta[i] - theta[j]))
        
        # 科里奥利耦合 (15维)
        for i in range(6):
            for j in range(i+1, 6):
                features.append(np.sin(theta[i] - theta[j]))
        
        # 重力特征 (6维)
        for i in range(6):
            features.append(np.sin(np.sum(theta[:i+1])))
        
        return features
\end{lstlisting}

\subsection{特征降维实现}

\begin{lstlisting}[caption=PCA降维实现]
from sklearn.decomposition import PCA
from sklearn.feature_selection import mutual_info_regression

class FeatureDimensionReducer:
    def __init__(self, target_dim=63):
        self.target_dim = target_dim
        self.pca = PCA()
        self.selected_features = None
    
    def fit_transform(self, X, y):
        """
        X: (n_samples, 140) 特征矩阵
        y: (n_samples, 6) 误差向量
        """
        # 1. PCA分析
        self.pca.fit(X)
        cumsum_ratio = np.cumsum(self.pca.explained_variance_ratio_)
        n_components_95 = np.argmax(cumsum_ratio >= 0.95) + 1
        
        print(f"95%方差需要{n_components_95}个主成分")
        
        # 2. 互信息特征选择
        # 对每个输出维度计算互信息
        mi_scores = np.zeros(X.shape[1])
        for i in range(y.shape[1]):
            mi_scores += mutual_info_regression(X, y[:, i])
        
        # 选择互信息最高的特征
        self.selected_features = np.argsort(mi_scores)[-self.target_dim:]
        
        return X[:, self.selected_features]
    
    def transform(self, X):
        """对新数据应用相同的降维"""
        return X[:, self.selected_features]
\end{lstlisting}

\section{第十七章：确定性初始化的实现}

\subsection{基于最小二乘的初始化}

\begin{lstlisting}[caption=确定性初始化实现]
class DeterministicInitializer:
    def __init__(self, dh_params, alpha=1e-3):
        self.dh_params = dh_params
        self.alpha = alpha  # 正则化参数
    
    def compute_initial_weights(self, theta_train, error_train):
        """
        基于训练数据计算初始权重
        theta_train: (n_samples, 6) 训练关节角度
        error_train: (n_samples, 6) 训练误差
        """
        n_samples = theta_train.shape[0]
        
        # 计算雅可比矩阵
        J_total = []
        for i in range(n_samples):
            J_i = self.compute_jacobian(theta_train[i])
            J_total.append(J_i)
        
        J = np.vstack(J_total)  # (n_samples*6, 6)
        epsilon = error_train.flatten()  # (n_samples*6,)
        
        # 最小二乘解: w = (J^T J + αI)^(-1) J^T ε
        JTJ = J.T @ J
        JTe = J.T @ epsilon
        I = np.eye(JTJ.shape[0])
        
        w_init = np.linalg.solve(JTJ + self.alpha * I, JTe)
        
        return w_init
    
    def initialize_network(self, network, theta_train, error_train):
        """
        用计算出的权重初始化神经网络
        """
        w_init = self.compute_initial_weights(theta_train, error_train)
        
        # 将权重分配给网络层
        # 这里需要根据具体的网络结构来实现
        param_idx = 0
        for param in network.parameters():
            param_size = param.numel()
            if param_idx + param_size <= len(w_init):
                param.data = torch.tensor(
                    w_init[param_idx:param_idx + param_size]
                ).reshape(param.shape).float()
                param_idx += param_size
\end{lstlisting}

\section{第十八章：完整的PINN训练流程}

\subsection{PINN网络定义}

\begin{lstlisting}[caption=PINN网络结构]
class PINNNetwork(nn.Module):
    def __init__(self, input_dim=63, hidden_dims=[512, 256, 128, 64], output_dim=6):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(0.1))
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.network = nn.Sequential(*layers)
        
        # 分支输出
        self.position_head = nn.Linear(output_dim, 3)
        self.orientation_head = nn.Linear(output_dim, 3)
    
    def forward(self, x):
        features = self.network(x)
        position = self.position_head(features)
        orientation = self.orientation_head(features)
        return torch.cat([position, orientation], dim=1)
\end{lstlisting}

\subsection{多目标损失函数}

\begin{lstlisting}[caption=多目标损失函数]
class MultiObjectiveLoss:
    def __init__(self, dh_params):
        self.dh_params = dh_params
        self.physics_constraint = KinematicsConstraint(dh_params)
    
    def compute_objectives(self, predictions, targets, network, inputs):
        """
        计算三个目标函数值
        """
        # 目标1: 位置误差
        pos_pred = predictions[:, :3]
        pos_target = targets[:, :3]
        f1 = torch.mean((pos_pred - pos_target)**2)
        
        # 目标2: 角度误差
        ori_pred = predictions[:, 3:]
        ori_target = targets[:, 3:]
        f2 = torch.mean((ori_pred - ori_target)**2)
        
        # 目标3: 模型复杂度
        l1_reg = sum(torch.norm(param, 1) for param in network.parameters())
        dropout_penalty = 0.1  # 假设dropout率
        f3 = l1_reg + dropout_penalty
        
        return [f1.item(), f2.item(), f3.item()]
    
    def compute_pinn_loss(self, predictions, targets, network, inputs, 
                         lambda_physics=1.0):
        """
        计算PINN总损失
        """
        # 数据拟合损失
        data_loss = torch.mean((predictions - targets)**2)
        
        # 物理约束损失
        physics_loss = self.physics_constraint(inputs, predictions)
        
        # 总损失
        total_loss = data_loss + lambda_physics * physics_loss
        
        return total_loss, data_loss, physics_loss
\end{lstlisting}

\section{第十九章：实验结果的深入分析}

\subsection{收敛性分析}

\begin{lstlisting}[caption=收敛性分析代码]
def analyze_convergence(loss_history, window_size=50):
    """
    分析训练收敛性
    """
    # 计算移动平均
    moving_avg = np.convolve(loss_history, 
                            np.ones(window_size)/window_size, 
                            mode='valid')
    
    # 计算收敛速度
    convergence_rate = []
    for i in range(1, len(moving_avg)):
        rate = (moving_avg[i-1] - moving_avg[i]) / moving_avg[i-1]
        convergence_rate.append(rate)
    
    # 判断是否收敛
    recent_rates = convergence_rate[-20:]  # 最近20个点
    is_converged = np.mean(recent_rates) < 1e-4
    
    return {
        'moving_average': moving_avg,
        'convergence_rate': convergence_rate,
        'is_converged': is_converged,
        'final_loss': loss_history[-1]
    }
\end{lstlisting}

\subsection{统计显著性检验}

\begin{lstlisting}[caption=统计检验实现]
from scipy import stats

def statistical_significance_test(traditional_errors, pinn_errors):
    """
    进行统计显著性检验
    """
    # t检验
    t_stat, p_value = stats.ttest_ind(traditional_errors, pinn_errors)
    
    # 效应大小 (Cohen's d)
    pooled_std = np.sqrt(((len(traditional_errors) - 1) * np.var(traditional_errors) + 
                         (len(pinn_errors) - 1) * np.var(pinn_errors)) / 
                        (len(traditional_errors) + len(pinn_errors) - 2))
    
    cohens_d = (np.mean(traditional_errors) - np.mean(pinn_errors)) / pooled_std
    
    return {
        't_statistic': t_stat,
        'p_value': p_value,
        'cohens_d': cohens_d,
        'significant': p_value < 0.05,
        'effect_size': 'large' if abs(cohens_d) > 0.8 else 
                      'medium' if abs(cohens_d) > 0.5 else 'small'
    }
\end{lstlisting}

\section{第二十章：创新点的实际价值}

\subsection{工业应用价值}

\textcolor{red}{\textbf{经济效益计算}}：

假设一个工厂有100台机器人，每台机器人因为精度问题导致的废品率为2%，每个废品损失10元，每天生产1000个零件：

\textbf{传统方法年损失}：
$$\text{年损失} = 100 \times 1000 \times 365 \times 0.02 \times 10 = 7,300,000 \text{元}$$

\textbf{我们的方法}：
- 位置精度提升91.7%，假设废品率降低到0.2%
- 年损失：$100 \times 1000 \times 365 \times 0.002 \times 10 = 730,000$ 元
- \textcolor{blue}{\textbf{年节省：6,570,000元}}

\subsection{技术创新的可推广性}

我们的三大创新点可以推广到：

\begin{framed}
\textcolor{blue}{\textbf{推广应用}}
\begin{enumerate}
\item \textbf{其他机器人系统}：协作机器人、移动机器人、并联机器人
\item \textbf{其他工程优化}：结构优化、流体优化、电磁优化
\item \textbf{其他物理系统}：航空航天、汽车工程、生物医学工程
\end{enumerate}
\end{framed}

\subsection{学术贡献总结}

\begin{table}[h]
\centering
\caption{学术贡献对比}
\begin{tabular}{|l|l|l|}
\hline
\textbf{传统方法} & \textbf{存在问题} & \textbf{我们的解决方案} \\
\hline
单目标优化 & 权重选择主观 & 多目标Pareto优化 \\
\hline
数据驱动 & 缺乏物理意义 & 物理信息神经网络 \\
\hline
随机初始化 & 结果不稳定 & 确定性初始化 \\
\hline
经验特征 & 泛化能力差 & 物理驱动特征工程 \\
\hline
局部最优 & 精度受限 & 多策略避免陷阱 \\
\hline
\end{tabular}
\end{table}

\section{总结：从数学到实践的完整链条}

通过这三部分的详细讲解，我们建立了从基础数学概念到实际工程应用的完整知识链条：

\begin{framed}
\textcolor{red}{\textbf{知识链条}}
\begin{enumerate}
\item \textbf{数学基础}：向量、矩阵、三角函数、微积分
\item \textbf{机器人学}：运动学、动力学、雅可比矩阵
\item \textbf{优化理论}：多目标优化、进化算法、物理约束
\item \textbf{机器学习}：神经网络、物理信息网络、特征工程
\item \textbf{编程实现}：Python、PyTorch、数值计算
\item \textbf{工程应用}：误差补偿、精度提升、经济效益
\end{enumerate}
\end{framed}

\textcolor{blue}{\textbf{最终目标}}：让本科生能够理解复杂的工程问题，掌握先进的解决方法，并能够在实际项目中应用这些技术。

这不仅仅是一个技术方案，更是一个完整的工程思维训练过程！

\end{document}
