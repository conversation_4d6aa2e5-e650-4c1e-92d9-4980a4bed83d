#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Staubli TX60 机器人逆运动学求解器
包含解析解和数值解两种方法

作者: AI助手
日期: 2025年
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import fsolve, least_squares
from scipy.spatial.transform import Rotation as R
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

from 理论计算模块 import RobotKinematics

class StaubliTX60InverseKinematics:
    """Staubli TX60 逆运动学求解器"""
    
    def __init__(self):
        self.robot = RobotKinematics()
        
        # Staubli TX60 几何参数 (mm)
        self.a2 = 290  # 连杆2长度
        self.d3 = 20   # 连杆3偏距
        self.d4 = 310  # 连杆4偏距
        self.d6 = 70   # 连杆6偏距
        
        # 关节限制 (度)
        self.joint_limits = [
            (-180, 180),  # θ1
            (-125, 125),  # θ2
            (-138, 138),  # θ3
            (-270, 270),  # θ4
            (-120, 133),  # θ5
            (-270, 270)   # θ6
        ]
        
    def pose_to_matrix(self, pose):
        """将位姿向量转换为齐次变换矩阵"""
        x, y, z, rx, ry, rz = pose
        
        # 位置向量
        position = np.array([x, y, z])
        
        # 旋转矩阵 (假设是ZYX欧拉角，单位：度)
        rotation = R.from_euler('ZYX', [rz, ry, rx], degrees=True).as_matrix()
        
        # 齐次变换矩阵
        T = np.eye(4)
        T[:3, :3] = rotation
        T[:3, 3] = position
        
        return T
    
    def analytical_inverse_kinematics(self, target_pose):
        """
        解析逆运动学求解 (基于Staubli TX60几何结构)
        
        参数:
        target_pose: 目标位姿 [x, y, z, rx, ry, rz]
        
        返回:
        solutions: 所有可能的解 (最多8个)
        """
        
        T_target = self.pose_to_matrix(target_pose)
        
        # 提取目标位置和旋转矩阵
        px, py, pz = T_target[:3, 3]
        R_target = T_target[:3, :3]
        
        solutions = []
        
        # 计算腕部中心位置
        # P_wrist = P_target - d6 * R_target * [0, 0, 1]
        wrist_offset = self.d6 * R_target @ np.array([0, 0, 1])
        px_w = px - wrist_offset[0]
        py_w = py - wrist_offset[1]
        pz_w = pz - wrist_offset[2]
        
        # 求解θ1 (两个解)
        theta1_solutions = []
        
        # θ1 = atan2(py_w, px_w) 和 θ1 = atan2(py_w, px_w) + π
        theta1_1 = np.arctan2(py_w, px_w)
        theta1_2 = theta1_1 + np.pi
        
        # 规范化到 [-π, π]
        theta1_1 = np.arctan2(np.sin(theta1_1), np.cos(theta1_1))
        theta1_2 = np.arctan2(np.sin(theta1_2), np.cos(theta1_2))
        
        theta1_solutions = [theta1_1, theta1_2]
        
        for theta1 in theta1_solutions:
            try:
                # 计算θ2和θ3 (每个θ1对应2个解)
                theta23_solutions = self.solve_theta2_theta3(px_w, py_w, pz_w, theta1)
                
                for theta2, theta3 in theta23_solutions:
                    # 计算θ4, θ5, θ6 (每组θ1,θ2,θ3对应2个解)
                    theta456_solutions = self.solve_theta4_theta5_theta6(
                        R_target, theta1, theta2, theta3
                    )
                    
                    for theta4, theta5, theta6 in theta456_solutions:
                        # 检查关节限制
                        joint_angles = [theta1, theta2, theta3, theta4, theta5, theta6]
                        if self.check_joint_limits(joint_angles):
                            solutions.append(np.rad2deg(joint_angles))
                            
            except Exception as e:
                continue
        
        return solutions
    
    def solve_theta2_theta3(self, px_w, py_w, pz_w, theta1):
        """求解θ2和θ3"""
        
        # 投影到第一关节坐标系
        r = np.sqrt(px_w**2 + py_w**2)
        
        # 考虑第一关节的影响
        x2 = r * np.cos(theta1) + px_w * np.cos(theta1) + py_w * np.sin(theta1)
        y2 = pz_w
        
        # 使用余弦定理求解
        # 距离到腕部中心
        d_to_wrist = np.sqrt(x2**2 + (y2 - self.d3)**2)
        
        # 检查是否在工作空间内
        max_reach = self.a2 + self.d4
        min_reach = abs(self.a2 - self.d4)
        
        if d_to_wrist > max_reach or d_to_wrist < min_reach:
            raise ValueError("目标点超出工作空间")
        
        # 余弦定理计算θ3
        cos_theta3 = (d_to_wrist**2 - self.a2**2 - self.d4**2) / (2 * self.a2 * self.d4)
        
        # 确保在有效范围内
        cos_theta3 = np.clip(cos_theta3, -1, 1)
        
        # θ3的两个解
        theta3_1 = np.arccos(cos_theta3)
        theta3_2 = -theta3_1
        
        solutions = []
        
        for theta3 in [theta3_1, theta3_2]:
            # 计算对应的θ2
            # 使用几何关系
            alpha = np.arctan2(y2 - self.d3, x2)
            beta = np.arctan2(self.d4 * np.sin(theta3), self.a2 + self.d4 * np.cos(theta3))
            
            theta2_1 = alpha - beta
            theta2_2 = alpha + beta
            
            for theta2 in [theta2_1, theta2_2]:
                solutions.append((theta2, theta3))
        
        return solutions
    
    def solve_theta4_theta5_theta6(self, R_target, theta1, theta2, theta3):
        """求解θ4, θ5, θ6 (腕部关节)"""
        
        # 计算前三个关节的旋转矩阵
        T1 = self.robot.mdh_transform(0, np.pi/2, 0, theta1 + np.pi)
        T2 = self.robot.mdh_transform(self.a2, 0, 0, theta2 + np.pi/2)
        T3 = self.robot.mdh_transform(0, np.pi/2, self.d3, theta3 + np.pi/2)
        
        R_03 = T1[:3, :3] @ T2[:3, :3] @ T3[:3, :3]
        
        # 腕部旋转矩阵
        R_36 = R_03.T @ R_target
        
        solutions = []
        
        # 从旋转矩阵提取欧拉角 (ZYZ约定)
        # R_36 = Rz(θ4) * Ry(θ5) * Rz(θ6)
        
        # θ5的两个解
        sy = np.sqrt(R_36[0, 2]**2 + R_36[1, 2]**2)
        
        if sy > 1e-6:  # 非奇异情况
            theta5_1 = np.arctan2(sy, R_36[2, 2])
            theta5_2 = np.arctan2(-sy, R_36[2, 2])
            
            for theta5 in [theta5_1, theta5_2]:
                if abs(np.sin(theta5)) > 1e-6:
                    theta4 = np.arctan2(R_36[1, 2], R_36[0, 2])
                    theta6 = np.arctan2(R_36[2, 1], -R_36[2, 0])
                else:
                    # 奇异情况处理
                    theta4 = 0
                    theta6 = np.arctan2(-R_36[1, 0], R_36[1, 1])
                
                solutions.append((theta4, theta5, theta6))
        else:
            # 奇异情况
            theta5 = 0
            theta4 = 0
            theta6 = np.arctan2(-R_36[1, 0], R_36[1, 1])
            solutions.append((theta4, theta5, theta6))
        
        return solutions
    
    def check_joint_limits(self, joint_angles_rad):
        """检查关节限制"""
        joint_angles_deg = np.rad2deg(joint_angles_rad)
        
        for i, (angle, (min_limit, max_limit)) in enumerate(zip(joint_angles_deg, self.joint_limits)):
            if angle < min_limit or angle > max_limit:
                return False
        return True
    
    def numerical_inverse_kinematics(self, target_pose, initial_guess=None):
        """
        数值逆运动学求解
        
        参数:
        target_pose: 目标位姿 [x, y, z, rx, ry, rz]
        initial_guess: 初始猜测 [θ1, θ2, θ3, θ4, θ5, θ6] (度)
        
        返回:
        solution: 求解结果 [θ1, θ2, θ3, θ4, θ5, θ6] (度)
        """
        
        if initial_guess is None:
            initial_guess = [0, 0, 0, 0, 0, 0]  # 默认初始猜测
        
        def objective_function(joint_angles_deg):
            """目标函数：最小化位姿误差"""
            try:
                # 计算正运动学
                calculated_pose = self.robot.forward_kinematics(joint_angles_deg)
                
                # 计算误差
                pose_error = calculated_pose - target_pose
                
                # 角度误差需要考虑周期性
                for i in range(3, 6):
                    while pose_error[i] > 180:
                        pose_error[i] -= 360
                    while pose_error[i] < -180:
                        pose_error[i] += 360
                
                return pose_error
                
            except Exception as e:
                return np.array([1e6, 1e6, 1e6, 1e6, 1e6, 1e6])  # 惩罚值
        
        def constraint_function(joint_angles_deg):
            """约束函数：关节限制"""
            constraints = []
            for i, (angle, (min_limit, max_limit)) in enumerate(zip(joint_angles_deg, self.joint_limits)):
                constraints.append(angle - min_limit)  # angle >= min_limit
                constraints.append(max_limit - angle)  # angle <= max_limit
            return np.array(constraints)
        
        try:
            # 使用least_squares求解
            result = least_squares(
                objective_function,
                initial_guess,
                bounds=([limit[0] for limit in self.joint_limits], 
                       [limit[1] for limit in self.joint_limits]),
                method='trf',
                ftol=1e-8,
                xtol=1e-8
            )
            
            if result.success:
                return result.x
            else:
                raise ValueError("数值求解失败")
                
        except Exception as e:
            # 如果least_squares失败，尝试fsolve
            try:
                solution = fsolve(objective_function, initial_guess, xtol=1e-8)
                
                # 检查解的有效性
                final_error = objective_function(solution)
                if np.linalg.norm(final_error) < 1e-3:
                    return solution
                else:
                    raise ValueError("数值求解精度不足")
                    
            except Exception as e2:
                raise ValueError(f"所有数值方法都失败: {e}, {e2}")
    
    def solve_inverse_kinematics(self, target_pose, method='analytical', initial_guess=None):
        """
        逆运动学求解主函数
        
        参数:
        target_pose: 目标位姿 [x, y, z, rx, ry, rz]
        method: 求解方法 ('analytical' 或 'numerical')
        initial_guess: 数值方法的初始猜测
        
        返回:
        solutions: 解列表
        """
        
        if method == 'analytical':
            try:
                solutions = self.analytical_inverse_kinematics(target_pose)
                if solutions:
                    return solutions
                else:
                    print("解析解失败，尝试数值解...")
                    return [self.numerical_inverse_kinematics(target_pose, initial_guess)]
            except Exception as e:
                print(f"解析解失败: {e}，尝试数值解...")
                return [self.numerical_inverse_kinematics(target_pose, initial_guess)]
        
        elif method == 'numerical':
            return [self.numerical_inverse_kinematics(target_pose, initial_guess)]
        
        else:
            raise ValueError("method 必须是 'analytical' 或 'numerical'")
    
    def verify_solution(self, joint_angles, target_pose):
        """验证逆运动学解的正确性"""
        
        # 计算正运动学
        calculated_pose = self.robot.forward_kinematics(joint_angles)
        
        # 计算误差
        pose_error = calculated_pose - target_pose
        
        # 角度误差规范化
        for i in range(3, 6):
            while pose_error[i] > 180:
                pose_error[i] -= 360
            while pose_error[i] < -180:
                pose_error[i] += 360
        
        # 计算误差范数
        position_error = np.linalg.norm(pose_error[:3])  # mm
        orientation_error = np.linalg.norm(pose_error[3:])  # 度
        
        return {
            'position_error': position_error,
            'orientation_error': orientation_error,
            'pose_error': pose_error,
            'is_valid': position_error < 0.1 and orientation_error < 0.1
        }

def test_inverse_kinematics():
    """测试逆运动学求解器"""
    
    print("=== Staubli TX60 逆运动学求解器测试 ===")
    
    # 创建求解器
    ik_solver = StaubliTX60InverseKinematics()
    
    # 测试用例
    test_poses = [
        [400, 200, 300, 0, 0, 0],      # 简单位姿
        [300, -100, 400, 10, 20, 30], # 复杂位姿
        [500, 0, 200, 0, 90, 0],      # 边界位姿
    ]
    
    for i, target_pose in enumerate(test_poses):
        print(f"\n--- 测试用例 {i+1} ---")
        print(f"目标位姿: {target_pose}")
        
        try:
            # 解析解
            print("\n解析逆运动学:")
            analytical_solutions = ik_solver.solve_inverse_kinematics(
                target_pose, method='analytical'
            )
            
            print(f"找到 {len(analytical_solutions)} 个解析解:")
            for j, solution in enumerate(analytical_solutions[:3]):  # 只显示前3个
                print(f"  解 {j+1}: {np.round(solution, 2)}")
                
                # 验证解
                verification = ik_solver.verify_solution(solution, target_pose)
                print(f"    位置误差: {verification['position_error']:.6f} mm")
                print(f"    角度误差: {verification['orientation_error']:.6f} 度")
                print(f"    解有效: {verification['is_valid']}")
            
            # 数值解
            print("\n数值逆运动学:")
            numerical_solution = ik_solver.solve_inverse_kinematics(
                target_pose, method='numerical'
            )[0]
            
            print(f"数值解: {np.round(numerical_solution, 2)}")
            
            # 验证数值解
            verification = ik_solver.verify_solution(numerical_solution, target_pose)
            print(f"  位置误差: {verification['position_error']:.6f} mm")
            print(f"  角度误差: {verification['orientation_error']:.6f} 度")
            print(f"  解有效: {verification['is_valid']}")
            
        except Exception as e:
            print(f"求解失败: {e}")

def main():
    """主函数"""
    test_inverse_kinematics()

if __name__ == "__main__":
    main()
