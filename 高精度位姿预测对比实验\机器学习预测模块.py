#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习位姿预测模块
基于之前训练的最优模型进行位姿预测
"""

import pandas as pd
import numpy as np
import joblib
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import lightgbm as lgb
from sklearn.neural_network import MLPRegressor
import warnings
warnings.filterwarnings('ignore')

class MLPosePredictor:
    """机器学习位姿预测器"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.target_names = ['X', 'Y', 'Z', 'Rx - Euler XYZ Angle (度)', 
                           'Ry - Euler XYZ Angle (度)', 'Rz - Euler XYZ Angle (度)']
        self.best_models = {
            'X': 'LightGBM',
            'Y': '神经网络', 
            'Z': 'LightGBM',
            'Rx - Euler XYZ Angle (度)': '神经网络',
            'Ry - Euler XYZ Angle (度)': 'LightGBM',
            'Rz - Euler XYZ Angle (度)': '神经网络'
        }
    
    def load_data(self):
        """加载训练数据"""
        print("=== 加载机器学习训练数据 ===")
        
        # 加载特征数据
        self.X_data = pd.read_excel('../theta2000.xlsx', header=None)
        self.X_data.columns = [f'theta_{i+1}' for i in range(6)]
        
        # 加载目标数据
        self.y_data = pd.read_excel('../real2000.xlsx')
        
        print(f"特征数据形状: {self.X_data.shape}")
        print(f"目标数据形状: {self.y_data.shape}")
        
        return self.X_data, self.y_data
    
    def train_optimal_models(self):
        """训练最优模型组合"""
        print("\n=== 训练最优模型组合 ===")
        
        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            self.X_data, self.y_data, test_size=0.2, random_state=42
        )
        
        # 为每个目标变量训练最优模型
        for i, target_name in enumerate(self.target_names):
            print(f"\n训练 {target_name} 的最优模型...")
            
            y_train_single = y_train.iloc[:, i]
            y_test_single = y_test.iloc[:, i]
            
            best_model_type = self.best_models[target_name]
            
            if best_model_type == 'LightGBM':
                # 训练LightGBM模型
                model = lgb.LGBMRegressor(
                    n_estimators=200,
                    learning_rate=0.1,
                    max_depth=8,
                    random_state=42,
                    verbose=-1
                )
                model.fit(X_train, y_train_single)
                y_pred = model.predict(X_test)
                
            elif best_model_type == '神经网络':
                # 训练神经网络模型
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                
                model = MLPRegressor(
                    hidden_layer_sizes=(100, 50),
                    max_iter=1000,
                    random_state=42,
                    early_stopping=True,
                    validation_fraction=0.1
                )
                model.fit(X_train_scaled, y_train_single)
                y_pred = model.predict(X_test_scaled)
                
                # 保存标准化器
                self.scalers[target_name] = scaler
            
            # 计算性能指标
            r2 = r2_score(y_test_single, y_pred)
            rmse = np.sqrt(mean_squared_error(y_test_single, y_pred))
            mae = mean_absolute_error(y_test_single, y_pred)
            
            print(f"  {best_model_type}: R²={r2:.4f}, RMSE={rmse:.2f}, MAE={mae:.2f}")
            
            # 保存模型
            self.models[target_name] = model
        
        print("\n所有最优模型训练完成！")
    
    def predict_poses(self, joint_angles):
        """
        预测位姿
        
        Parameters:
        joint_angles: 关节角度数据 (N, 6) 或 (6,)
        
        Returns:
        predicted_poses: 预测的位姿 (N, 6) 或 (6,)
        """
        # 确保输入是2D数组
        if joint_angles.ndim == 1:
            joint_angles = joint_angles.reshape(1, -1)
            single_sample = True
        else:
            single_sample = False
        
        # 转换为DataFrame
        if isinstance(joint_angles, np.ndarray):
            joint_df = pd.DataFrame(joint_angles, columns=[f'theta_{i+1}' for i in range(6)])
        else:
            joint_df = joint_angles
        
        predictions = []
        
        for i, target_name in enumerate(self.target_names):
            model = self.models[target_name]
            best_model_type = self.best_models[target_name]
            
            if best_model_type == 'LightGBM':
                pred = model.predict(joint_df)
            elif best_model_type == '神经网络':
                scaler = self.scalers[target_name]
                joint_scaled = scaler.transform(joint_df)
                pred = model.predict(joint_scaled)
            
            predictions.append(pred)
        
        # 转置以获得正确的形状
        predicted_poses = np.array(predictions).T
        
        if single_sample:
            return predicted_poses[0]
        else:
            return predicted_poses
    
    def batch_predict_poses(self, joint_angles_batch):
        """批量预测位姿"""
        return self.predict_poses(joint_angles_batch)
    
    def save_models(self, save_dir='./models/'):
        """保存训练好的模型"""
        import os
        os.makedirs(save_dir, exist_ok=True)
        
        for target_name, model in self.models.items():
            model_filename = f"{save_dir}{target_name.replace(' ', '_').replace('(', '').replace(')', '')}_model.pkl"
            joblib.dump(model, model_filename)
        
        for target_name, scaler in self.scalers.items():
            scaler_filename = f"{save_dir}{target_name.replace(' ', '_').replace('(', '').replace(')', '')}_scaler.pkl"
            joblib.dump(scaler, scaler_filename)
        
        print(f"模型已保存到: {save_dir}")
    
    def load_models(self, save_dir='./models/'):
        """加载训练好的模型"""
        import os
        
        for target_name in self.target_names:
            model_filename = f"{save_dir}{target_name.replace(' ', '_').replace('(', '').replace(')', '')}_model.pkl"
            if os.path.exists(model_filename):
                self.models[target_name] = joblib.load(model_filename)
            
            scaler_filename = f"{save_dir}{target_name.replace(' ', '_').replace('(', '').replace(')', '')}_scaler.pkl"
            if os.path.exists(scaler_filename):
                self.scalers[target_name] = joblib.load(scaler_filename)
        
        print(f"模型已从 {save_dir} 加载")

def generate_ml_predictions():
    """生成机器学习预测结果"""
    print("=== 生成机器学习预测结果 ===")
    
    # 创建预测器
    predictor = MLPosePredictor()
    
    # 加载数据
    X_data, y_data = predictor.load_data()
    
    # 训练模型
    predictor.train_optimal_models()
    
    # 生成所有样本的预测
    print("\n生成所有样本的预测结果...")
    predicted_poses = predictor.batch_predict_poses(X_data)
    
    # 创建预测结果DataFrame
    pred_columns = ['X_predicted', 'Y_predicted', 'Z_predicted',
                   'Rx_predicted', 'Ry_predicted', 'Rz_predicted']
    predicted_df = pd.DataFrame(predicted_poses, columns=pred_columns)
    
    print(f"预测完成: {predicted_df.shape}")
    print("预测结果统计:")
    print(predicted_df.describe())
    
    # 保存预测结果
    predicted_df.to_excel('机器学习预测结果.xlsx', index=False)
    
    # 保存模型
    predictor.save_models()
    
    print("\n机器学习预测结果已保存到: 机器学习预测结果.xlsx")
    
    return predicted_df, predictor

def create_prediction_comparison():
    """创建预测对比数据"""
    print("\n=== 创建预测对比数据 ===")
    
    # 加载实际测量数据
    measured_data = pd.read_excel('../real2000.xlsx')
    
    # 生成机器学习预测
    predicted_df, predictor = generate_ml_predictions()
    
    # 加载关节角度数据
    joint_data = pd.read_excel('../theta2000.xlsx', header=None)
    joint_data.columns = [f'theta_{i+1}' for i in range(6)]
    
    # 选择代表性样本进行详细对比
    sample_indices = range(0, len(joint_data), 50)  # 每50个选1个
    
    comparison_data = []
    
    for idx in sample_indices:
        sample = {
            'sample_id': idx,
            
            # 关节角度
            'theta_1': joint_data.iloc[idx, 0],
            'theta_2': joint_data.iloc[idx, 1],
            'theta_3': joint_data.iloc[idx, 2], 
            'theta_4': joint_data.iloc[idx, 3],
            'theta_5': joint_data.iloc[idx, 4],
            'theta_6': joint_data.iloc[idx, 5],
            
            # 实测值
            'X_measured': measured_data.iloc[idx, 0],
            'Y_measured': measured_data.iloc[idx, 1],
            'Z_measured': measured_data.iloc[idx, 2],
            'Rx_measured': measured_data.iloc[idx, 3],
            'Ry_measured': measured_data.iloc[idx, 4],
            'Rz_measured': measured_data.iloc[idx, 5],
            
            # 预测值
            'X_predicted': predicted_df.iloc[idx, 0],
            'Y_predicted': predicted_df.iloc[idx, 1],
            'Z_predicted': predicted_df.iloc[idx, 2],
            'Rx_predicted': predicted_df.iloc[idx, 3],
            'Ry_predicted': predicted_df.iloc[idx, 4],
            'Rz_predicted': predicted_df.iloc[idx, 5],
        }
        comparison_data.append(sample)
    
    comparison_df = pd.DataFrame(comparison_data)
    
    # 计算预测误差
    for coord in ['X', 'Y', 'Z', 'Rx', 'Ry', 'Rz']:
        comparison_df[f'{coord}_pred_error'] = (comparison_df[f'{coord}_predicted'] - 
                                              comparison_df[f'{coord}_measured'])
    
    # 保存对比数据
    comparison_df.to_excel('机器学习预测对比数据.xlsx', index=False)
    print(f"预测对比数据已保存: {len(comparison_df)} 个样本")
    
    return comparison_df

if __name__ == "__main__":
    # 生成机器学习预测结果
    predicted_df, predictor = generate_ml_predictions()
    
    # 创建预测对比数据
    comparison_df = create_prediction_comparison()
    
    print("\n机器学习预测模块运行完成！")
    print("生成文件:")
    print("- 机器学习预测结果.xlsx")
    print("- 机器学习预测对比数据.xlsx")
    print("- models/ 文件夹 (保存的模型)")
